import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkMLKContent() {
  try {
    console.log('🔍 CHECKING MLK BOOK CONTENT STRUCTURE\n');

    // First, get the book info
    const { data: book, error: bookError } = await supabase
      .from('books')
      .select('*')
      .eq('id', 36)
      .single();

    if (bookError) {
      console.log('❌ Error fetching book:', bookError.message);
      return;
    }

    console.log('📚 Book Information:');
    console.log(`   ID: ${book.id}`);
    console.log(`   Title: "${book.title}"`);
    console.log(`   Author: "${book.author}"`);
    console.log(`   Description: "${book.description}"`);

    // Check if title needs updating
    const correctTitle = "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King";
    const needsTitleUpdate = book.title !== correctTitle;

    console.log(`\n📝 Title Status: ${needsTitleUpdate ? 'NEEDS UPDATE' : 'CORRECT'}`);
    if (needsTitleUpdate) {
      console.log(`   Current: "${book.title}"`);
      console.log(`   Should be: "${correctTitle}"`);
    }

    // Now check for content - try different approaches
    console.log('\n📖 Checking content...');
    
    // Try to get all content entries for this book
    const { data: allContent, error: allContentError } = await supabase
      .from('book_contents')
      .select('*')
      .eq('book_id', 36);

    if (allContentError) {
      console.log('❌ Error fetching content:', allContentError.message);
    } else {
      console.log(`   Found ${allContent.length} content entries`);
      
      if (allContent.length === 0) {
        console.log('⚠️ No content found for this book');
        console.log('   This book may need content to be added');
      } else if (allContent.length > 1) {
        console.log('⚠️ Multiple content entries found:');
        allContent.forEach((content, index) => {
          console.log(`     Entry ${index + 1}: ${JSON.stringify(content).substring(0, 100)}...`);
        });
      } else {
        const content = allContent[0];
        console.log('✅ Single content entry found');
        console.log(`   Content type: ${typeof content.content}`);
        
        if (content.content) {
          if (typeof content.content === 'string') {
            console.log(`   Content length: ${content.content.length} characters`);
            console.log(`   Preview: "${content.content.substring(0, 200)}..."`);
          } else if (typeof content.content === 'object') {
            console.log(`   Content structure: ${Object.keys(content.content).join(', ')}`);
            
            if (content.content.chapters) {
              console.log(`   Chapters: ${content.content.chapters.length}`);
              content.content.chapters.forEach((chapter, index) => {
                console.log(`     Chapter ${index + 1}: "${chapter.title}" (${chapter.content?.length || 0} chars)`);
                
                if (chapter.content) {
                  // Check for formatting issues
                  const hasIntroduction = chapter.content.includes('INTRODUÇÃO E CONTEXTUALIZAÇÃO');
                  const has1955 = chapter.content.includes('1955');
                  const hasProperHeaders = chapter.content.includes('## ') || chapter.content.includes('# ');
                  
                  console.log(`       Has introduction: ${hasIntroduction}`);
                  console.log(`       Has 1955 content: ${has1955}`);
                  console.log(`       Has proper headers: ${hasProperHeaders}`);
                  
                  if (!hasProperHeaders && (hasIntroduction || has1955)) {
                    console.log('       ⚠️ Content needs structure formatting');
                  }
                }
              });
            }
          }
        }
      }
    }

    // Return the analysis
    return {
      bookId: book.id,
      currentTitle: book.title,
      currentAuthor: book.author,
      needsTitleUpdate,
      contentEntries: allContent?.length || 0,
      content: allContent?.[0]?.content || null
    };

  } catch (error) {
    console.error('💥 Error during check:', error);
  }
}

checkMLKContent().then(result => {
  if (result) {
    console.log('\n🎯 SUMMARY:');
    console.log(`   Book ID: ${result.bookId}`);
    console.log(`   Title update needed: ${result.needsTitleUpdate}`);
    console.log(`   Content entries: ${result.contentEntries}`);
    console.log(`   Has content: ${result.content ? 'YES' : 'NO'}`);
  }
});
