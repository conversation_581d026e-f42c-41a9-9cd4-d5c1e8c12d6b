import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixMLKLibraryTitle() {
  try {
    console.log('🔧 CORRIGINDO TÍTULO DO MLK NA BIBLIOTECA\n');

    // Primeiro, vamos encontrar o livro com o título problemático
    console.log('1. Procurando livro com título problemático...');
    
    const { data: books, error: searchError } = await supabase
      .from('books')
      .select('*')
      .or('title.ilike.%Um Apelo A Consciencia%,title.ilike.%<PERSON> 3%,author.eq.Autor Desconhecido');

    if (searchError) {
      console.log('❌ Erro ao buscar livros:', searchError.message);
      return;
    }

    console.log(`📚 Encontrados ${books.length} livros para verificar:`);
    books.forEach(book => {
      console.log(`   ID: ${book.id} - "${book.title}" por ${book.author}`);
    });

    // Encontrar o livro específico que precisa ser corrigido
    const problemBook = books.find(book => 
      book.title.includes('Um Apelo A Consciencia') || 
      book.title.includes('Martin Luther King 3') ||
      book.author === 'Autor Desconhecido'
    );

    if (!problemBook) {
      console.log('❌ Livro problemático não encontrado');
      return;
    }

    console.log(`\n✅ Livro problemático encontrado:`);
    console.log(`   ID: ${problemBook.id}`);
    console.log(`   Título atual: "${problemBook.title}"`);
    console.log(`   Autor atual: "${problemBook.author}"`);
    console.log(`   Descrição: "${problemBook.description}"`);

    // Definir as informações corretas
    const correctTitle = "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King";
    const correctAuthor = "Martin Luther King Jr.";
    const correctDescription = "Os melhores discursos sobre direitos civis e justiça social de Martin Luther King Jr., incluindo 'I Have a Dream' e outros discursos históricos que mudaram o mundo.";

    // Atualizar o livro
    console.log('\n2. Atualizando informações do livro...');
    
    const { data: updatedBook, error: updateError } = await supabase
      .from('books')
      .update({
        title: correctTitle,
        author: correctAuthor,
        description: correctDescription
      })
      .eq('id', problemBook.id)
      .select()
      .single();

    if (updateError) {
      console.log('❌ Erro ao atualizar livro:', updateError.message);
      
      // Tentar atualização campo por campo
      console.log('\n3. Tentando atualização campo por campo...');
      
      // Atualizar título
      const { error: titleError } = await supabase
        .from('books')
        .update({ title: correctTitle })
        .eq('id', problemBook.id);
      
      if (titleError) {
        console.log('❌ Erro ao atualizar título:', titleError.message);
      } else {
        console.log('✅ Título atualizado com sucesso');
      }

      // Atualizar autor
      const { error: authorError } = await supabase
        .from('books')
        .update({ author: correctAuthor })
        .eq('id', problemBook.id);
      
      if (authorError) {
        console.log('❌ Erro ao atualizar autor:', authorError.message);
      } else {
        console.log('✅ Autor atualizado com sucesso');
      }

      // Atualizar descrição
      const { error: descError } = await supabase
        .from('books')
        .update({ description: correctDescription })
        .eq('id', problemBook.id);
      
      if (descError) {
        console.log('❌ Erro ao atualizar descrição:', descError.message);
      } else {
        console.log('✅ Descrição atualizada com sucesso');
      }

    } else {
      console.log('✅ Livro atualizado com sucesso em uma operação');
      console.log(`   Novo título: "${updatedBook.title}"`);
      console.log(`   Novo autor: "${updatedBook.author}"`);
    }

    // Verificar a atualização
    console.log('\n4. Verificando atualização...');
    
    const { data: verifiedBook, error: verifyError } = await supabase
      .from('books')
      .select('*')
      .eq('id', problemBook.id)
      .single();

    if (verifyError) {
      console.log('❌ Erro ao verificar atualização:', verifyError.message);
    } else {
      console.log('✅ Verificação da atualização:');
      console.log(`   ID: ${verifiedBook.id}`);
      console.log(`   Título: "${verifiedBook.title}"`);
      console.log(`   Autor: "${verifiedBook.author}"`);
      console.log(`   Descrição: "${verifiedBook.description?.substring(0, 100)}..."`);

      // Verificar se as correções foram aplicadas
      const titleCorrect = verifiedBook.title === correctTitle;
      const authorCorrect = verifiedBook.author === correctAuthor;
      const descCorrect = verifiedBook.description?.includes('I Have a Dream');

      console.log('\n📊 Status das correções:');
      console.log(`   ${titleCorrect ? '✅' : '❌'} Título: ${titleCorrect ? 'CORRETO' : 'PRECISA CORREÇÃO'}`);
      console.log(`   ${authorCorrect ? '✅' : '❌'} Autor: ${authorCorrect ? 'CORRETO' : 'PRECISA CORREÇÃO'}`);
      console.log(`   ${descCorrect ? '✅' : '❌'} Descrição: ${descCorrect ? 'CORRETA' : 'PRECISA CORREÇÃO'}`);

      if (titleCorrect && authorCorrect && descCorrect) {
        console.log('\n🎉 TODAS AS CORREÇÕES APLICADAS COM SUCESSO!');
      } else {
        console.log('\n⚠️ Algumas correções podem não ter sido aplicadas');
      }
    }

    // Verificar se há conteúdo para este livro
    console.log('\n5. Verificando conteúdo do livro...');
    
    const { data: content, error: contentError } = await supabase
      .from('book_contents')
      .select('*')
      .eq('book_id', problemBook.id);

    if (contentError) {
      console.log('❌ Erro ao verificar conteúdo:', contentError.message);
    } else {
      console.log(`📖 Conteúdo encontrado: ${content.length} entrada(s)`);
      
      if (content.length === 0) {
        console.log('⚠️ Este livro não tem conteúdo estruturado');
        console.log('   Pode ser necessário adicionar conteúdo formatado');
      } else {
        content.forEach((entry, index) => {
          console.log(`   Entrada ${index + 1}: ${typeof entry.content} com ${JSON.stringify(entry.content).length} caracteres`);
        });
      }
    }

    console.log('\n🎯 RESUMO DAS CORREÇÕES:');
    console.log('✅ Título corrigido para: "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King"');
    console.log('✅ Autor corrigido para: "Martin Luther King Jr."');
    console.log('✅ Descrição melhorada com informações sobre os discursos');
    console.log('✅ Livro agora aparecerá corretamente na biblioteca');

  } catch (error) {
    console.error('💥 Erro durante correção:', error);
  }
}

fixMLKLibraryTitle();
