import puppeteer from 'puppeteer';

async function clearCacheAndTest() {
  let browser;
  try {
    console.log('🧹 LIMPANDO CACHE E TESTANDO MLK\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🗑️ 1. Limpando cache do navegador...');
    
    // Limpar cache, cookies, localStorage, etc.
    await page.evaluateOnNewDocument(() => {
      // Limpar localStorage
      localStorage.clear();
      // Limpar sessionStorage
      sessionStorage.clear();
    });

    // Limpar cache do navegador
    const client = await page.target().createCDPSession();
    await client.send('Network.clearBrowserCache');
    await client.send('Network.clearBrowserCookies');

    console.log('✅ Cache limpo');
    
    console.log('🌐 2. Navegando para aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('🔑 3. Fazendo login...');
    
    const loginResult = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button, a'));
      const entrarButton = buttons.find(btn => 
        btn.textContent?.includes('Entrar') || btn.textContent?.includes('Login')
      );
      
      if (entrarButton) {
        entrarButton.click();
        return { success: true, buttonText: entrarButton.textContent };
      }
      
      return { success: false, error: 'Botão Entrar não encontrado' };
    });

    if (!loginResult.success) {
      console.log('❌ Não foi possível fazer login');
      return;
    }

    console.log(`✅ Login realizado: "${loginResult.buttonText}"`);
    
    // Aguardar navegação
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('🔍 4. Verificando se o BookLoader está sendo chamado...');
    
    // Interceptar chamadas de rede para ver se há problemas
    await page.setRequestInterception(true);
    
    const requests = [];
    page.on('request', request => {
      if (request.url().includes('supabase') || request.url().includes('books')) {
        requests.push({
          url: request.url(),
          method: request.method(),
          headers: request.headers()
        });
      }
      request.continue();
    });

    // Interceptar logs do console
    const consoleLogs = [];
    page.on('console', msg => {
      if (msg.text().includes('BookLoader') || msg.text().includes('Loading book') || msg.text().includes('MLK')) {
        consoleLogs.push(msg.text());
      }
    });

    console.log('📚 5. Procurando livro do MLK...');
    
    const mlkBookResult = await page.evaluate(() => {
      // Procurar pelo livro do MLK
      const searchTerms = [
        'Um Apelo à Consciência',
        'Martin Luther King',
        'Melhores Discursos',
        'Apelo à Consciência'
      ];
      
      const allElements = Array.from(document.querySelectorAll('*'));
      
      for (let term of searchTerms) {
        for (let element of allElements) {
          const text = element.textContent || '';
          if (text.includes(term)) {
            // Encontrou o livro, tentar clicar
            let clickable = element;
            
            // Procurar elemento clicável
            while (clickable && clickable !== document.body) {
              if (clickable.tagName === 'DIV' || clickable.tagName === 'BUTTON' ||
                  clickable.classList.contains('book-card') || 
                  clickable.classList.contains('book-item') ||
                  clickable.onclick || clickable.href) {
                
                clickable.click();
                return {
                  success: true,
                  foundTerm: term,
                  clickedText: text.substring(0, 100),
                  element: clickable.tagName + '.' + clickable.className
                };
              }
              clickable = clickable.parentElement;
            }
            
            // Se não encontrou pai clicável, clicar no próprio elemento
            element.click();
            return {
              success: true,
              foundTerm: term,
              clickedText: text.substring(0, 100),
              element: element.tagName + '.' + element.className
            };
          }
        }
      }
      
      return { success: false, error: 'Livro MLK não encontrado' };
    });

    if (!mlkBookResult.success) {
      console.log('❌ Livro do MLK não encontrado:', mlkBookResult.error);
      
      // Debug: mostrar todos os livros disponíveis
      const availableBooks = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*'));
        const bookLike = elements.filter(el => {
          const text = el.textContent || '';
          return text.length > 20 && text.length < 200 && 
                 (text.includes('Autor') || text.includes('por') || text.includes('de'));
        }).slice(0, 10);
        
        return bookLike.map(el => el.textContent.substring(0, 80));
      });
      
      console.log('📖 Livros disponíveis:');
      availableBooks.forEach((book, index) => {
        console.log(`   ${index + 1}. "${book}..."`);
      });
      
      return;
    }

    console.log(`✅ Livro MLK encontrado e clicado!`);
    console.log(`   Termo: "${mlkBookResult.foundTerm}"`);
    console.log(`   Texto: "${mlkBookResult.clickedText}..."`);

    console.log('⏳ 6. Aguardando leitor carregar...');
    await new Promise(resolve => setTimeout(resolve, 8000));

    console.log('📊 7. Verificando logs e requisições...');
    
    console.log('\n🌐 REQUISIÇÕES INTERCEPTADAS:');
    if (requests.length > 0) {
      requests.forEach((req, index) => {
        console.log(`   ${index + 1}. ${req.method} ${req.url}`);
      });
    } else {
      console.log('   ❌ Nenhuma requisição para Supabase interceptada');
    }

    console.log('\n📝 LOGS DO CONSOLE:');
    if (consoleLogs.length > 0) {
      consoleLogs.forEach((log, index) => {
        console.log(`   ${index + 1}. ${log}`);
      });
    } else {
      console.log('   ❌ Nenhum log do BookLoader encontrado');
    }

    console.log('\n🧪 8. Testando leitor...');
    
    const readerTest = await page.evaluate(() => {
      // Verificar se o leitor carregou
      const reader = document.querySelector('.formatted-content');
      if (!reader) {
        return { success: false, error: 'Leitor (.formatted-content) não encontrado' };
      }

      const content = reader.textContent || '';
      
      // Análise do conteúdo
      const contentAnalysis = {
        hasContent: content.length > 0,
        contentLength: content.length,
        
        // Verificar conteúdo específico do MLK
        hasMLKName: content.includes('Martin Luther King'),
        hasDiscursos: content.includes('discursos') || content.includes('Discursos'),
        hasDireitosCivis: content.includes('direitos civis'),
        hasIDream: content.includes('I Have a Dream') || content.includes('I have a dream'),
        hasBirmingham: content.includes('Birmingham'),
        hasMontgomery: content.includes('Montgomery'),
        
        // Preview do conteúdo
        contentPreview: content.substring(0, 500)
      };

      return {
        success: true,
        contentAnalysis
      };
    });

    if (!readerTest.success) {
      console.log('❌ Teste do leitor falhou:', readerTest.error);
      
      // Verificar se há algum elemento de loading ou erro
      const debugInfo = await page.evaluate(() => {
        const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"]');
        const errorElements = document.querySelectorAll('[class*="error"]');
        
        return {
          hasLoadingElements: loadingElements.length,
          hasErrorElements: errorElements.length,
          bodyText: document.body.textContent.substring(0, 500)
        };
      });
      
      console.log('🔍 DEBUG INFO:');
      console.log(`   Loading elements: ${debugInfo.hasLoadingElements}`);
      console.log(`   Error elements: ${debugInfo.hasErrorElements}`);
      console.log(`   Body text: "${debugInfo.bodyText}..."`);
      
      return;
    }

    const content = readerTest.contentAnalysis;

    console.log('\n📊 ANÁLISE DO CONTEÚDO:');
    console.log(`   📏 Tamanho: ${content.contentLength.toLocaleString()} caracteres`);
    
    console.log('\n📚 VERIFICAÇÃO DE CONTEÚDO AUTÊNTICO:');
    console.log(`   ${content.hasMLKName ? '✅' : '❌'} Contém "Martin Luther King"`);
    console.log(`   ${content.hasDiscursos ? '✅' : '❌'} Contém "discursos"`);
    console.log(`   ${content.hasDireitosCivis ? '✅' : '❌'} Contém "direitos civis"`);
    console.log(`   ${content.hasIDream ? '✅' : '❌'} Contém "I Have a Dream"`);
    console.log(`   ${content.hasBirmingham ? '✅' : '❌'} Contém "Birmingham"`);
    console.log(`   ${content.hasMontgomery ? '✅' : '❌'} Contém "Montgomery"`);
    
    console.log('\n📋 PREVIEW DO CONTEÚDO:');
    console.log(`"${content.contentPreview}..."`);

    // Calcular métricas de sucesso
    const contentSuccess = content.contentLength > 5000;
    const mlkContentSuccess = content.hasMLKName && content.hasDiscursos;

    console.log('\n🎯 AVALIAÇÃO FINAL:');
    console.log(`   ${contentSuccess ? '✅' : '❌'} Conteúdo Adequado (${content.contentLength} chars)`);
    console.log(`   ${mlkContentSuccess ? '✅' : '❌'} Conteúdo Autêntico do MLK`);
    console.log(`   ${requests.length > 0 ? '✅' : '❌'} Requisições para Supabase`);
    console.log(`   ${consoleLogs.length > 0 ? '✅' : '❌'} Logs do BookLoader`);

    const overallSuccess = contentSuccess && mlkContentSuccess;
    
    console.log(`\n🏆 STATUS GERAL: ${overallSuccess ? '✅ SUCESSO COMPLETO' : '⚠️ PRECISA INVESTIGAÇÃO'}`);

    if (overallSuccess) {
      console.log('\n🎉 EXCELENTE! APLICAÇÃO FUNCIONANDO PERFEITAMENTE:');
      console.log('   ✓ Cache limpo com sucesso');
      console.log('   ✓ Livro do MLK carregado');
      console.log('   ✓ Conteúdo autêntico exibido');
      console.log('   ✓ BookLoader funcionando');
    } else {
      console.log('\n🔧 ÁREAS QUE PRECISAM INVESTIGAÇÃO:');
      if (!contentSuccess) console.log('   • Conteúdo não está carregando adequadamente');
      if (!mlkContentSuccess) console.log('   • Conteúdo do MLK não está sendo exibido');
      if (requests.length === 0) console.log('   • Nenhuma requisição para Supabase detectada');
      if (consoleLogs.length === 0) console.log('   • BookLoader não está sendo chamado');
    }

    // Manter navegador aberto para verificação manual
    console.log('\n⏳ Mantendo navegador aberto por 30 segundos para verificação manual...');
    await new Promise(resolve => setTimeout(resolve, 30000));

  } catch (error) {
    console.error('💥 Erro durante teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

clearCacheAndTest();
