import puppeteer from 'puppeteer';

async function testMLKFormatting() {
  let browser;
  try {
    console.log('📚 TESTING MARTIN LUTHER KING JR. BOOK FORMATTING\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test the MLK book specifically
    console.log('🧪 Testing MLK book structure and formatting...');
    
    const mlkTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${Date.now()}`);
        
        // Test MLK book (ID 36)
        const bookText = await BookLoader.getBookText('36');
        
        // Analyze the structure
        const lines = bookText.split('\n');
        const analysis = {
          totalLines: lines.length,
          emptyLines: lines.filter(line => line.trim() === '').length,
          chapterTitles: (bookText.match(/^# /gm) || []).length,
          sectionTitles: (bookText.match(/^## /gm) || []).length,
          subsectionTitles: (bookText.match(/^### /gm) || []).length,
          bodyParagraphs: lines.filter(line => line.trim().length > 50 && !line.startsWith('#')).length,
          textLength: bookText.length,
          
          // MLK-specific checks
          hasCorrectTitle: bookText.includes('Um Apelo à Consciência: Os Melhores Discursos'),
          hasAuthor: bookText.includes('Martin Luther King Jr.'),
          hasIntroduction: bookText.includes('INTRODUÇÃO E CONTEXTUALIZAÇÃO'),
          has1955Content: bookText.includes('1955-1960: OS PRIMEIROS ANOS'),
          hasDreamSpeech: bookText.includes('I HAVE A DREAM'),
          hasLastYears: bookText.includes('1964-1968: OS ÚLTIMOS ANOS'),
          hasMontgomery: bookText.includes('Montgomery'),
          hasNobelPrize: bookText.includes('Prêmio Nobel'),
          
          preview: bookText.substring(0, 1000)
        };
        
        // Check for duplication
        const paragraphs = bookText.split('\n\n').filter(p => p.trim().length > 20);
        const uniqueParagraphs = new Set(paragraphs.map(p => p.toLowerCase().trim()));
        analysis.duplicationRate = ((paragraphs.length - uniqueParagraphs.size) / paragraphs.length * 100).toFixed(1);
        
        return {
          success: true,
          analysis,
          bookText: bookText.substring(0, 2000) // First 2000 chars for inspection
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (mlkTest.success) {
      console.log('✅ MLK book test completed successfully');
      
      const analysis = mlkTest.analysis;
      console.log('\n📊 MLK BOOK STRUCTURE ANALYSIS:');
      console.log(`   Total lines: ${analysis.totalLines}`);
      console.log(`   Empty lines: ${analysis.emptyLines} (${(analysis.emptyLines/analysis.totalLines*100).toFixed(1)}%)`);
      console.log(`   Chapter titles (# ): ${analysis.chapterTitles}`);
      console.log(`   Section titles (## ): ${analysis.sectionTitles}`);
      console.log(`   Subsection titles (### ): ${analysis.subsectionTitles}`);
      console.log(`   Body paragraphs: ${analysis.bodyParagraphs}`);
      console.log(`   Total text length: ${analysis.textLength.toLocaleString()} characters`);
      console.log(`   Duplication rate: ${analysis.duplicationRate}%`);
      
      console.log('\n📋 MLK-SPECIFIC CONTENT CHECKS:');
      console.log(`   ${analysis.hasCorrectTitle ? '✅' : '❌'} Correct full title`);
      console.log(`   ${analysis.hasAuthor ? '✅' : '❌'} Author "Martin Luther King Jr."`);
      console.log(`   ${analysis.hasIntroduction ? '✅' : '❌'} Introduction section`);
      console.log(`   ${analysis.has1955Content ? '✅' : '❌'} 1955-1960 period content`);
      console.log(`   ${analysis.hasDreamSpeech ? '✅' : '❌'} "I Have a Dream" speech`);
      console.log(`   ${analysis.hasLastYears ? '✅' : '❌'} 1964-1968 final years`);
      console.log(`   ${analysis.hasMontgomery ? '✅' : '❌'} Montgomery Bus Boycott`);
      console.log(`   ${analysis.hasNobelPrize ? '✅' : '❌'} Nobel Prize content`);
      
      console.log('\n📋 Content preview:');
      console.log(analysis.preview + '...');
      
      // Evaluate MLK book quality
      const mlkChecks = [
        { name: 'Correct title', passed: analysis.hasCorrectTitle },
        { name: 'Author present', passed: analysis.hasAuthor },
        { name: 'Introduction section', passed: analysis.hasIntroduction },
        { name: 'Historical periods', passed: analysis.has1955Content && analysis.hasLastYears },
        { name: 'Famous speeches', passed: analysis.hasDreamSpeech },
        { name: 'Key events', passed: analysis.hasMontgomery && analysis.hasNobelPrize },
        { name: 'Proper structure', passed: analysis.sectionTitles >= 4 },
        { name: 'Low duplication', passed: parseFloat(analysis.duplicationRate) < 5 }
      ];
      
      const passedChecks = mlkChecks.filter(check => check.passed).length;
      console.log(`\n🎯 MLK Book Quality Score: ${passedChecks}/${mlkChecks.length}`);
      
      mlkChecks.forEach(check => {
        console.log(`   ${check.passed ? '✅' : '❌'} ${check.name}`);
      });
      
    } else {
      console.log('❌ MLK book test failed:', mlkTest.error);
    }

    // Test CSS formatting for MLK content
    console.log('\n🎨 Testing CSS formatting for MLK content...');
    
    const cssTest = await page.evaluate(() => {
      // Create test content with MLK-specific elements
      const testContainer = document.createElement('div');
      testContainer.className = 'formatted-content light';
      testContainer.style.fontSize = '16pt';
      testContainer.innerHTML = `
        <div class="chapter-title">UM APELO À CONSCIÊNCIA: OS MELHORES DISCURSOS DE MARTIN LUTHER KING</div>
        <p class="body-text">Por Martin Luther King Jr.</p>
        <div class="section-title">INTRODUÇÃO E CONTEXTUALIZAÇÃO</div>
        <p class="body-text">Martin Luther King Jr. (1929-1968) foi um dos mais importantes líderes do movimento pelos direitos civis nos Estados Unidos.</p>
        <div class="subsection-title">O Contexto Histórico</div>
        <p class="body-text">Durante as décadas de 1950 e 1960, os Estados Unidos viviam sob o sistema de segregação racial.</p>
        <div class="section-title">I HAVE A DREAM</div>
        <p class="body-text">Tenho um sonho de que um dia esta nação se levantará e viverá o verdadeiro significado de seu credo.</p>
      `;
      
      document.body.appendChild(testContainer);
      
      // Get computed styles
      const styles = {
        chapter: window.getComputedStyle(testContainer.querySelector('.chapter-title')),
        section: window.getComputedStyle(testContainer.querySelector('.section-title')),
        subsection: window.getComputedStyle(testContainer.querySelector('.subsection-title')),
        body: window.getComputedStyle(testContainer.querySelector('.body-text'))
      };
      
      const results = {
        chapterSize: styles.chapter.fontSize,
        chapterAlign: styles.chapter.textAlign,
        chapterWeight: styles.chapter.fontWeight,
        chapterMarginTop: styles.chapter.marginTop,
        chapterMarginBottom: styles.chapter.marginBottom,
        
        sectionSize: styles.section.fontSize,
        sectionAlign: styles.section.textAlign,
        sectionMarginTop: styles.section.marginTop,
        
        subsectionSize: styles.subsection.fontSize,
        subsectionAlign: styles.subsection.textAlign,
        subsectionStyle: styles.subsection.fontStyle,
        
        bodyIndent: styles.body.textIndent,
        bodyAlign: styles.body.textAlign,
        bodyLineHeight: styles.body.lineHeight
      };
      
      // Clean up
      document.body.removeChild(testContainer);
      
      return results;
    });

    console.log('📐 MLK CONTENT CSS VERIFICATION:');
    console.log(`   Chapter title size: ${cssTest.chapterSize} (should be largest)`);
    console.log(`   Section title size: ${cssTest.sectionSize} (should be medium)`);
    console.log(`   Subsection title size: ${cssTest.subsectionSize} (should be smaller)`);
    console.log(`   Chapter alignment: ${cssTest.chapterAlign} (should be center)`);
    console.log(`   Section alignment: ${cssTest.sectionAlign} (should be center)`);
    console.log(`   Subsection alignment: ${cssTest.subsectionAlign} (should be left)`);
    console.log(`   Body text indent: ${cssTest.bodyIndent} (should be 1cm)`);
    console.log(`   Body text alignment: ${cssTest.bodyAlign} (should be justify)`);

    // Final assessment
    console.log('\n🏆 FINAL MLK BOOK ASSESSMENT:');
    
    if (mlkTest.success) {
      const analysis = mlkTest.analysis;
      const hasGoodStructure = analysis.sectionTitles >= 4 && analysis.chapterTitles >= 1;
      const hasMLKContent = analysis.hasDreamSpeech && analysis.hasIntroduction;
      const lowDuplication = parseFloat(analysis.duplicationRate) < 5;
      const properFormatting = cssTest.chapterAlign === 'center' && cssTest.bodyAlign === 'justify';
      
      console.log(`${hasGoodStructure ? '✅' : '❌'} Structure: ${hasGoodStructure ? 'EXCELLENT' : 'NEEDS WORK'}`);
      console.log(`${hasMLKContent ? '✅' : '❌'} MLK Content: ${hasMLKContent ? 'COMPLETE' : 'MISSING'}`);
      console.log(`${lowDuplication ? '✅' : '❌'} Duplication: ${lowDuplication ? 'MINIMAL' : 'TOO HIGH'}`);
      console.log(`${properFormatting ? '✅' : '❌'} Formatting: ${properFormatting ? 'KINDLE-STYLE' : 'NEEDS FIXES'}`);
      
      if (hasGoodStructure && hasMLKContent && lowDuplication && properFormatting) {
        console.log('\n🎉 MLK BOOK PERFECTLY FORMATTED!');
        console.log('✅ All requirements met');
        console.log('✅ Professional Kindle-style formatting');
        console.log('✅ Proper text hierarchy');
        console.log('✅ Historical content properly organized');
      } else {
        console.log('\n⚠️ Some aspects may need attention');
      }
    }

    console.log('\n🚀 MANUAL TESTING INSTRUCTIONS:');
    console.log('1. Open the application in browser');
    console.log('2. Find "Um Apelo à Consciência" by Martin Luther King Jr.');
    console.log('3. Click "Ler Agora" to open in PDF reader');
    console.log('4. Verify:');
    console.log('   • Title displays with full subtitle');
    console.log('   • Author shows as "Martin Luther King Jr."');
    console.log('   • Introduction section is properly formatted');
    console.log('   • Historical periods are clearly separated');
    console.log('   • "I Have a Dream" speech is included');
    console.log('   • No duplicate content when scrolling');
    console.log('   • Professional Kindle-style appearance');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Error during test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testMLKFormatting();
