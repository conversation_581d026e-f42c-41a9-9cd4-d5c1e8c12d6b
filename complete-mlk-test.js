import puppeteer from 'puppeteer';

async function completeMLKTest() {
  let browser;
  try {
    console.log('🎯 COMPLETE MLK BOOK TEST - AUTHENTIC CONTENT & FONT CONTROLS\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Step 1: Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('🔑 Step 2: Clicking "Entrar" to access book library...');
    
    const loginResult = await page.evaluate(() => {
      // Look for "Entrar" button
      const buttons = Array.from(document.querySelectorAll('button, a'));
      const entrarButton = buttons.find(btn => 
        btn.textContent?.includes('Entrar') || btn.textContent?.includes('Login')
      );
      
      if (entrarButton) {
        entrarButton.click();
        return { success: true, buttonText: entrarButton.textContent };
      }
      
      return { success: false, error: 'Entrar button not found' };
    });

    if (!loginResult.success) {
      console.log('❌ Could not find Entrar button:', loginResult.error);
      return;
    }

    console.log(`✅ Clicked "${loginResult.buttonText}" button`);
    
    // Wait for navigation to book library
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('📚 Step 3: Looking for MLK book in library...');
    
    const mlkBookSearch = await page.evaluate(() => {
      // Search for MLK book with various possible titles
      const searchTerms = [
        'Um Apelo à Consciência',
        'Martin Luther King',
        'Melhores Discursos',
        'Apelo à Consciência'
      ];
      
      const allElements = Array.from(document.querySelectorAll('*'));
      
      for (let term of searchTerms) {
        for (let element of allElements) {
          const text = element.textContent || '';
          if (text.includes(term)) {
            // Found MLK book, try to click it
            let clickable = element;
            
            // Find clickable parent
            while (clickable && clickable !== document.body) {
              if (clickable.tagName === 'DIV' || clickable.tagName === 'BUTTON' ||
                  clickable.classList.contains('book-card') || 
                  clickable.classList.contains('book-item') ||
                  clickable.onclick || clickable.href) {
                
                clickable.click();
                return {
                  success: true,
                  foundTerm: term,
                  clickedText: text.substring(0, 100),
                  element: clickable.tagName + '.' + clickable.className
                };
              }
              clickable = clickable.parentElement;
            }
            
            // If no clickable parent, click the element itself
            element.click();
            return {
              success: true,
              foundTerm: term,
              clickedText: text.substring(0, 100),
              element: element.tagName + '.' + element.className
            };
          }
        }
      }
      
      return { success: false, error: 'MLK book not found' };
    });

    if (!mlkBookSearch.success) {
      console.log('❌ MLK book not found in library:', mlkBookSearch.error);
      
      // Debug: show what books are available
      const availableBooks = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*'));
        const bookLike = elements.filter(el => {
          const text = el.textContent || '';
          return text.length > 20 && text.length < 200 && 
                 (text.includes('Autor') || text.includes('por') || text.includes('de'));
        }).slice(0, 5);
        
        return bookLike.map(el => el.textContent.substring(0, 80));
      });
      
      console.log('📖 Available books found:');
      availableBooks.forEach((book, index) => {
        console.log(`   ${index + 1}. "${book}..."`);
      });
      
      return;
    }

    console.log(`✅ MLK book found and clicked!`);
    console.log(`   Found by term: "${mlkBookSearch.foundTerm}"`);
    console.log(`   Clicked text: "${mlkBookSearch.clickedText}..."`);
    console.log(`   Element: ${mlkBookSearch.element}`);

    console.log('⏳ Step 4: Waiting for reader to load...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('🧪 Step 5: Testing content and font size controls...');
    
    const completeTest = await page.evaluate(() => {
      // Check if reader loaded
      const reader = document.querySelector('.formatted-content');
      if (!reader) {
        return { success: false, error: 'Reader (.formatted-content) not found' };
      }

      // Comprehensive content analysis
      const content = reader.textContent || '';
      const contentAnalysis = {
        hasContent: content.length > 0,
        contentLength: content.length,
        
        // Check for authentic MLK content
        hasMLKName: content.includes('Martin Luther King'),
        hasDiscursos: content.includes('discursos') || content.includes('Discursos'),
        hasDireitosCivis: content.includes('direitos civis'),
        hasNaoViolencia: content.includes('não-violenta') || content.includes('não-violência'),
        hasMontgomery: content.includes('Montgomery'),
        hasBirmingham: content.includes('Birmingham'),
        hasIDream: content.includes('I Have a Dream'),
        hasCartaPrisao: content.includes('Carta da Prisão') || content.includes('Birmingham'),
        
        // Check formatting structure
        hasChapterTitles: reader.querySelectorAll('.chapter-title').length,
        hasSectionTitles: reader.querySelectorAll('.section-title').length,
        hasBodyTexts: reader.querySelectorAll('.body-text').length,
        
        // Font size information
        computedFontSize: window.getComputedStyle(reader).fontSize,
        inlineFontSize: reader.style.fontSize || 'not set',
        
        // Content preview
        contentPreview: content.substring(0, 300)
      };

      // Test font size controls
      const fontControls = {
        increaseButtons: Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.title?.includes('Aumentar') || btn.title?.includes('Zoom In') || 
          btn.textContent?.includes('+') || btn.innerHTML?.includes('plus')
        ),
        
        decreaseButtons: Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.title?.includes('Diminuir') || btn.title?.includes('Zoom Out') || 
          btn.textContent?.includes('-') || btn.innerHTML?.includes('minus')
        ),
        
        fontSizeDisplays: Array.from(document.querySelectorAll('span, div')).filter(el => 
          el.textContent?.includes('pt') && el.textContent?.length < 10
        )
      };

      return {
        success: true,
        contentAnalysis,
        fontControls: {
          increaseCount: fontControls.increaseButtons.length,
          decreaseCount: fontControls.decreaseButtons.length,
          displayCount: fontControls.fontSizeDisplays.length,
          increaseButton: fontControls.increaseButtons[0] || null,
          decreaseButton: fontControls.decreaseButtons[0] || null
        }
      };
    });

    if (!completeTest.success) {
      console.log('❌ Complete test failed:', completeTest.error);
      return;
    }

    const content = completeTest.contentAnalysis;
    const controls = completeTest.fontControls;

    console.log('\n📊 CONTENT ANALYSIS RESULTS:');
    console.log(`   Content Length: ${content.contentLength.toLocaleString()} characters`);
    console.log(`   Current Font Size: ${content.computedFontSize} (inline: ${content.inlineFontSize})`);
    
    console.log('\n📚 AUTHENTIC MLK CONTENT VERIFICATION:');
    console.log(`   ${content.hasMLKName ? '✅' : '❌'} Contains "Martin Luther King"`);
    console.log(`   ${content.hasDiscursos ? '✅' : '❌'} Contains "discursos"`);
    console.log(`   ${content.hasDireitosCivis ? '✅' : '❌'} Contains "direitos civis"`);
    console.log(`   ${content.hasNaoViolencia ? '✅' : '❌'} Contains "não-violenta"`);
    console.log(`   ${content.hasMontgomery ? '✅' : '❌'} Contains "Montgomery"`);
    console.log(`   ${content.hasBirmingham ? '✅' : '❌'} Contains "Birmingham"`);
    console.log(`   ${content.hasIDream ? '✅' : '❌'} Contains "I Have a Dream"`);
    console.log(`   ${content.hasCartaPrisao ? '✅' : '❌'} Contains "Carta da Prisão"`);
    
    console.log('\n🏷️ KINDLE FORMATTING STRUCTURE:');
    console.log(`   Chapter Titles: ${content.hasChapterTitles}`);
    console.log(`   Section Titles: ${content.hasSectionTitles}`);
    console.log(`   Body Texts: ${content.hasBodyTexts}`);
    
    console.log('\n🔧 FONT SIZE CONTROLS:');
    console.log(`   Increase Buttons: ${controls.increaseCount}`);
    console.log(`   Decrease Buttons: ${controls.decreaseCount}`);
    console.log(`   Font Size Displays: ${controls.displayCount}`);
    
    console.log('\n📋 CONTENT PREVIEW:');
    console.log(`"${content.contentPreview}..."`);

    // Test font size increase if available
    if (controls.increaseCount > 0) {
      console.log('\n🔧 Step 6: Testing font size increase...');
      
      const fontIncreaseTest = await page.evaluate(() => {
        const reader = document.querySelector('.formatted-content');
        const increaseBtn = Array.from(document.querySelectorAll('button')).find(btn => 
          btn.title?.includes('Aumentar') || btn.title?.includes('Zoom In') || 
          btn.textContent?.includes('+')
        );
        
        if (!increaseBtn) return { success: false, error: 'Increase button not found' };
        
        const beforeComputed = window.getComputedStyle(reader).fontSize;
        const beforeInline = reader.style.fontSize;
        
        increaseBtn.click();
        
        return new Promise(resolve => {
          setTimeout(() => {
            const afterComputed = window.getComputedStyle(reader).fontSize;
            const afterInline = reader.style.fontSize;
            
            resolve({
              success: true,
              beforeComputed,
              afterComputed,
              beforeInline,
              afterInline,
              computedChanged: beforeComputed !== afterComputed,
              inlineChanged: beforeInline !== afterInline,
              anyChange: beforeComputed !== afterComputed || beforeInline !== afterInline
            });
          }, 1000);
        });
      });
      
      console.log('📈 FONT SIZE INCREASE TEST:');
      console.log(`   Before (computed): ${fontIncreaseTest.beforeComputed}`);
      console.log(`   After (computed): ${fontIncreaseTest.afterComputed}`);
      console.log(`   Before (inline): ${fontIncreaseTest.beforeInline}`);
      console.log(`   After (inline): ${fontIncreaseTest.afterInline}`);
      console.log(`   Font Size Changed: ${fontIncreaseTest.anyChange ? '✅ YES' : '❌ NO'}`);
    }

    // Calculate success metrics
    const contentSuccess = content.contentLength > 1000;
    const mlkContentSuccess = content.hasMLKName && (content.hasDiscursos || content.hasDireitosCivis);
    const formattingSuccess = content.hasBodyTexts > 0;
    const controlsSuccess = controls.increaseCount > 0 && controls.decreaseCount > 0;

    console.log('\n🎯 FINAL ASSESSMENT:');
    console.log(`   ${contentSuccess ? '✅' : '❌'} Content Loading (${content.contentLength} chars)`);
    console.log(`   ${mlkContentSuccess ? '✅' : '❌'} Authentic MLK Content`);
    console.log(`   ${formattingSuccess ? '✅' : '❌'} Kindle Formatting Applied`);
    console.log(`   ${controlsSuccess ? '✅' : '❌'} Font Size Controls Available`);

    const overallSuccess = contentSuccess && mlkContentSuccess && formattingSuccess && controlsSuccess;
    
    console.log(`\n🏆 OVERALL STATUS: ${overallSuccess ? '✅ COMPLETE SUCCESS' : '⚠️ PARTIAL SUCCESS'}`);

    if (overallSuccess) {
      console.log('\n🎉 EXCELLENT! MLK BOOK IS WORKING PERFECTLY:');
      console.log('   ✓ Authentic PDF-style content is displaying');
      console.log('   ✓ MLK-specific content verified');
      console.log('   ✓ Kindle-style formatting applied');
      console.log('   ✓ Font size controls available');
      console.log('   ✓ User experience is optimal');
      console.log('\n📚 Users now see the real MLK content with beautiful formatting!');
    } else {
      console.log('\n📋 SUMMARY:');
      console.log('   ✓ Successfully navigated to MLK book');
      console.log('   ✓ Content is loading and displaying');
      console.log('   ✓ Basic functionality working');
      if (!mlkContentSuccess) console.log('   ⚠️ MLK-specific content may need verification');
      if (!controlsSuccess) console.log('   ⚠️ Font size controls may need adjustment');
    }

    // Keep browser open for manual verification
    console.log('\n⏳ Keeping browser open for 30 seconds for manual verification...');
    console.log('🔍 Please manually verify:');
    console.log('   1. Content shows authentic MLK summary');
    console.log('   2. Font size +/- buttons work');
    console.log('   3. Text has proper Kindle-style formatting');
    console.log('   4. Themes (light/dark/sepia) work correctly');
    
    await new Promise(resolve => setTimeout(resolve, 30000));

  } catch (error) {
    console.error('💥 Error during complete test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

completeMLKTest();
