import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

// Mapeamento completo de todos os livros
const COMPLETE_BOOK_MAPPINGS = {
  'a_sutil_arte_de_ligar_o_foda_se_mark_manson_3': {
    title: 'A Sutil Arte de Ligar o F*da-se',
    author: '<PERSON>',
    category: 'Autoajuda',
    description: 'Um guia revolucionário sobre como parar de tentar ser positivo o tempo todo e começar a viver uma vida mais autêntica.',
    duration: 25,
    difficulty: 'Fácil'
  },
  'o_poder_do_habito_charles_duhigg_3': {
    title: 'O Poder do Hábito',
    author: 'Charles Duhigg',
    category: 'Produtividade',
    description: 'Descubra como os hábitos funcionam e como você pode transformá-los para melhorar sua vida pessoal e profissional.',
    duration: 30,
    difficulty: 'Intermediário'
  },
  'pai_rico_pai_pobre_robert_t_kiyosaki_3': {
    title: 'Pai Rico, Pai Pobre',
    author: 'Robert Kiyosaki',
    category: 'Finanças',
    description: 'As lições fundamentais sobre dinheiro e investimentos que não são ensinadas na escola.',
    duration: 25,
    difficulty: 'Fácil'
  },
  'pense_e_enriqueca_napoleon_hill_3': {
    title: 'Pense e Enriqueça',
    author: 'Napoleon Hill',
    category: 'Finanças',
    description: 'Os princípios fundamentais do sucesso financeiro baseados no estudo de mais de 500 milionários.',
    duration: 35,
    difficulty: 'Intermediário'
  },
  'habitos_atomicos_james_clear_5': {
    title: 'Hábitos Atômicos',
    author: 'James Clear',
    category: 'Produtividade',
    description: 'Como pequenas mudanças podem gerar grandes resultados através do poder dos hábitos compostos.',
    duration: 30,
    difficulty: 'Fácil'
  },
  'habitos_atomicos_james_clear_6': {
    title: 'Hábitos Atômicos',
    author: 'James Clear',
    category: 'Produtividade',
    description: 'Como pequenas mudanças podem gerar grandes resultados através do poder dos hábitos compostos.',
    duration: 30,
    difficulty: 'Fácil'
  },
  'a_interpretacao_dos_sonhos_sigmund_freud_3': {
    title: 'A Interpretação dos Sonhos',
    author: 'Sigmund Freud',
    category: 'Psicologia',
    description: 'A obra fundamental de Freud sobre o inconsciente e o significado dos sonhos na psique humana.',
    duration: 45,
    difficulty: 'Avançado'
  },
  'cem_anos_de_solidao_gabriel_garcia_marquez_3': {
    title: 'Cem Anos de Solidão',
    author: 'Gabriel García Márquez',
    category: 'Literatura',
    description: 'A obra-prima do realismo mágico que narra a saga da família Buendía em Macondo.',
    duration: 40,
    difficulty: 'Avançado'
  },
  'o_codigo_da_vinci_dan_brown_3': {
    title: 'O Código da Vinci',
    author: 'Dan Brown',
    category: 'Literatura',
    description: 'Um thriller envolvente que mistura arte, história e mistério em uma trama fascinante.',
    duration: 35,
    difficulty: 'Intermediário'
  },
  'tornar_se_pessoa_carl_rogers_3': {
    title: 'Tornar-se Pessoa',
    author: 'Carl Rogers',
    category: 'Psicologia',
    description: 'Os fundamentos da psicologia humanística e a abordagem centrada na pessoa.',
    duration: 40,
    difficulty: 'Avançado'
  },
  'a_influencia_de_heidegger_na_psicologia_existencial-humanista_foco_em_heidegger_3': {
    title: 'A Influência de Heidegger na Psicologia Existencial-Humanista',
    author: 'Vários Autores',
    category: 'Psicologia',
    description: 'Análise profunda da influência filosófica de Heidegger na psicologia moderna.',
    duration: 50,
    difficulty: 'Avançado'
  },
  'a_mente_de_adolf_hitler_walter_c_langer_3': {
    title: 'A Mente de Adolf Hitler',
    author: 'Walter C. Langer',
    category: 'História/Psicologia',
    description: 'Análise psicológica histórica da personalidade e motivações de Adolf Hitler.',
    duration: 45,
    difficulty: 'Avançado'
  },
  'a_psicologia_das_cores_eva_heller_3': {
    title: 'A Psicologia das Cores',
    author: 'Eva Heller',
    category: 'Psicologia',
    description: 'Como as cores influenciam nossas emoções, comportamentos e percepções.',
    duration: 25,
    difficulty: 'Fácil'
  },
  'a_psicologia_da_mulher_maravilha_autores_travis_langley_mara_wood_travis_langley_mara_wood_3': {
    title: 'A Psicologia da Mulher Maravilha',
    author: 'Travis Langley & Mara Wood',
    category: 'Psicologia',
    description: 'Análise psicológica do ícone feminino dos quadrinhos e seu impacto cultural.',
    duration: 30,
    difficulty: 'Fácil'
  },
  'a_sensacao_de_estar_sendo_observado_analise_autor_desconhecido_3': {
    title: 'A Sensação de Estar Sendo Observado',
    author: 'Autor Desconhecido',
    category: 'Psicologia',
    description: 'Análise do fenômeno psicológico da percepção de estar sendo observado.',
    duration: 20,
    difficulty: 'Intermediário'
  },
  'a_teoria_do_amadurecimento_d_w_winnicott_3': {
    title: 'A Teoria do Amadurecimento',
    author: 'D. W. Winnicott',
    category: 'Psicologia',
    description: 'Os fundamentos da teoria do desenvolvimento emocional infantil de Winnicott.',
    duration: 40,
    difficulty: 'Avançado'
  },
  'determined_a_science_of_life_without_free_will_robert_sapolsky_3': {
    title: 'Determined: A Science of Life Without Free Will',
    author: 'Robert Sapolsky',
    category: 'Neurociência',
    description: 'Exploração científica sobre determinismo e livre arbítrio na perspectiva neurobiológica.',
    duration: 50,
    difficulty: 'Avançado'
  },
  'e_o_cerebro_criou_o_homem_antonio_r_damasio_3': {
    title: 'E o Cérebro Criou o Homem',
    author: 'António R. Damásio',
    category: 'Neurociência',
    description: 'Como a consciência emerge do cérebro e molda a experiência humana.',
    duration: 45,
    difficulty: 'Avançado'
  },
  'freud_fundamentos_da_clinica_sigmund_freud_3': {
    title: 'Freud: Fundamentos da Clínica',
    author: 'Sigmund Freud',
    category: 'Psicologia',
    description: 'Os princípios fundamentais da prática clínica psicanalítica.',
    duration: 40,
    difficulty: 'Avançado'
  },
  'gestalt_terapia_autores_frederick_s_perls_ralph_hefferline_paul_goodman_frederick_s_perls_ralph_hefferline_paul_goodman_3': {
    title: 'Gestalt-Terapia',
    author: 'Frederick S. Perls, Ralph Hefferline & Paul Goodman',
    category: 'Psicologia',
    description: 'Os fundamentos teóricos e práticos da abordagem gestáltica em psicoterapia.',
    duration: 45,
    difficulty: 'Avançado'
  }
};

// Função para gerar conteúdo estruturado baseado na categoria
function generateStructuredContent(bookInfo) {
  const contentGenerators = {
    'Autoajuda': generateSelfHelpContent,
    'Produtividade': generateProductivityContent,
    'Finanças': generateFinanceContent,
    'Psicologia': generatePsychologyContent,
    'Literatura': generateLiteratureContent,
    'Neurociência': generateNeuroscienceContent,
    'História/Psicologia': generateHistoryPsychologyContent
  };

  const generator = contentGenerators[bookInfo.category] || generateGenericContent;
  return generator(bookInfo);
}

// Geradores de conteúdo por categoria
function generateSelfHelpContent(bookInfo) {
  return {
    chapters: [
      {
        id: 'chapter1',
        title: 'Fundamentos do Desenvolvimento Pessoal',
        content: `${bookInfo.title} apresenta uma abordagem transformadora para o crescimento pessoal. ${bookInfo.author} nos guia através dos princípios fundamentais que governam o desenvolvimento humano e a busca por uma vida mais plena.\n\nEste resumo explora os conceitos centrais da obra, oferecendo insights práticos sobre como aplicar esses ensinamentos no dia a dia. A jornada de autoconhecimento começa com a compreensão de nossos padrões mentais e comportamentais.\n\nO autor demonstra como pequenas mudanças na perspectiva podem gerar transformações significativas na qualidade de vida, relacionamentos e realização pessoal.`
      },
      {
        id: 'chapter2',
        title: 'Estratégias Práticas de Transformação',
        content: `A segunda parte do livro foca nas estratégias concretas para implementar mudanças positivas. ${bookInfo.author} apresenta ferramentas testadas e comprovadas para superar obstáculos mentais e emocionais.\n\nEssas técnicas incluem métodos para desenvolver resiliência, melhorar a autoestima e criar hábitos sustentáveis de crescimento. O autor enfatiza a importância da consistência e paciência no processo de transformação.\n\nCada estratégia é acompanhada de exemplos práticos e exercícios que facilitam a aplicação imediata dos conceitos aprendidos.`
      },
      {
        id: 'chapter3',
        title: 'Aplicação no Cotidiano',
        content: `A parte final concentra-se na integração dos aprendizados na vida cotidiana. ${bookInfo.author} oferece um roteiro claro para manter o momentum de crescimento a longo prazo.\n\nO livro conclui com reflexões sobre como medir o progresso e ajustar as estratégias conforme necessário. A jornada de desenvolvimento pessoal é apresentada como um processo contínuo de descoberta e refinamento.\n\nOs leitores são encorajados a desenvolver sua própria filosofia de vida baseada nos princípios universais apresentados na obra.`
      }
    ],
    key_points: [
      'O desenvolvimento pessoal é um processo contínuo e gradual',
      'Pequenas mudanças na perspectiva geram grandes transformações',
      'A consistência é mais importante que a intensidade',
      'Autoconhecimento é a base de toda mudança duradoura',
      'Ferramentas práticas facilitam a aplicação dos conceitos'
    ],
    practical_exercises: [
      'Faça uma autoavaliação honesta de suas forças e áreas de melhoria',
      'Estabeleça metas específicas e mensuráveis para seu crescimento',
      'Pratique técnicas de mindfulness e reflexão diária',
      'Desenvolva um sistema de accountability pessoal'
    ]
  };
}

function generateProductivityContent(bookInfo) {
  return {
    chapters: [
      {
        id: 'chapter1',
        title: 'Princípios da Alta Performance',
        content: `${bookInfo.title} revela os segredos por trás da produtividade excepcional. ${bookInfo.author} apresenta um sistema comprovado para maximizar resultados com menor esforço.\n\nO livro explora como otimizar energia, tempo e foco para alcançar objetivos ambiciosos. Os princípios apresentados são baseados em pesquisas científicas e experiências práticas de pessoas altamente produtivas.\n\nA obra demonstra que a produtividade não é sobre trabalhar mais, mas sobre trabalhar de forma mais inteligente e estratégica.`
      },
      {
        id: 'chapter2',
        title: 'Sistemas e Hábitos de Produtividade',
        content: `Esta seção foca na criação de sistemas sustentáveis de alta performance. ${bookInfo.author} detalha como construir rotinas que automatizam o sucesso.\n\nO autor apresenta técnicas para eliminar distrações, priorizar tarefas importantes e manter consistência a longo prazo. Cada sistema é projetado para ser simples de implementar e manter.\n\nA ênfase está em criar estruturas que funcionem mesmo quando a motivação está baixa, garantindo progresso contínuo em direção aos objetivos.`
      },
      {
        id: 'chapter3',
        title: 'Otimização Contínua',
        content: `A parte final aborda como refinar e melhorar continuamente seus sistemas de produtividade. ${bookInfo.author} ensina como medir resultados e fazer ajustes estratégicos.\n\nO livro conclui com estratégias para manter o momentum a longo prazo e evitar o burnout. A produtividade sustentável é apresentada como um equilíbrio entre alta performance e bem-estar.\n\nOs leitores aprendem a criar um ciclo virtuoso de melhoria contínua que se adapta às mudanças da vida e carreira.`
      }
    ],
    key_points: [
      'Produtividade é sobre trabalhar de forma inteligente, não apenas mais',
      'Sistemas bem projetados superam a dependência da motivação',
      'Pequenos hábitos compostos geram resultados extraordinários',
      'Foco e eliminação de distrações são fundamentais',
      'Melhoria contínua mantém a eficácia a longo prazo'
    ],
    practical_exercises: [
      'Identifique suas 3 prioridades mais importantes do dia',
      'Crie um sistema de revisão semanal de objetivos',
      'Elimine uma distração significativa do seu ambiente',
      'Estabeleça blocos de tempo dedicados para trabalho focado'
    ]
  };
}

function generateFinanceContent(bookInfo) {
  return {
    chapters: [
      {
        id: 'chapter1',
        title: 'Fundamentos da Educação Financeira',
        content: `${bookInfo.title} estabelece as bases para uma vida financeira próspera. ${bookInfo.author} desmistifica conceitos complexos e apresenta princípios atemporais de construção de riqueza.\n\nO livro começa explorando a mentalidade necessária para o sucesso financeiro, diferenciando entre ativos e passivos, e explicando como o dinheiro realmente funciona na economia moderna.\n\nEsses fundamentos são essenciais para qualquer pessoa que deseje alcançar independência financeira e construir riqueza duradoura.`
      },
      {
        id: 'chapter2',
        title: 'Estratégias de Investimento e Crescimento',
        content: `Esta seção detalha estratégias práticas para fazer o dinheiro trabalhar a seu favor. ${bookInfo.author} apresenta diferentes veículos de investimento e como diversificar adequadamente.\n\nO autor explica conceitos como juros compostos, risco versus retorno, e a importância do tempo nos investimentos. Cada estratégia é explicada de forma clara e acessível.\n\nO foco está em criar múltiplas fontes de renda e construir um portfólio robusto que resista às flutuações do mercado.`
      },
      {
        id: 'chapter3',
        title: 'Planejamento Financeiro de Longo Prazo',
        content: `A parte final aborda o planejamento financeiro estratégico para diferentes fases da vida. ${bookInfo.author} oferece orientações sobre aposentadoria, proteção patrimonial e sucessão.\n\nO livro conclui com conselhos sobre como manter disciplina financeira e continuar aprendendo sobre investimentos. A educação financeira contínua é apresentada como chave para o sucesso.\n\nOs leitores são equipados com ferramentas para tomar decisões financeiras informadas e construir um futuro financeiro seguro.`
      }
    ],
    key_points: [
      'Educação financeira é o primeiro passo para a riqueza',
      'Ativos geram renda, passivos consomem dinheiro',
      'Diversificação reduz riscos e otimiza retornos',
      'Tempo e juros compostos são seus maiores aliados',
      'Disciplina e consistência superam timing perfeito'
    ],
    practical_exercises: [
      'Calcule seu patrimônio líquido atual',
      'Crie um orçamento detalhado de receitas e despesas',
      'Defina metas financeiras específicas com prazos',
      'Pesquise e compare diferentes opções de investimento'
    ]
  };
}

function generatePsychologyContent(bookInfo) {
  return {
    chapters: [
      {
        id: 'chapter1',
        title: 'Fundamentos Teóricos',
        content: `${bookInfo.title} explora os mecanismos fundamentais da mente humana. ${bookInfo.author} apresenta teorias e conceitos que revolucionaram nossa compreensão do comportamento humano.\n\nEsta obra examina como pensamentos, emoções e comportamentos se interconectam, oferecendo insights profundos sobre a natureza da experiência humana.\n\nOs conceitos apresentados fornecem uma base sólida para compreender tanto o funcionamento normal quanto os desvios da psique humana.`
      },
      {
        id: 'chapter2',
        title: 'Aplicações Clínicas e Práticas',
        content: `Esta seção foca na aplicação prática dos conceitos teóricos. ${bookInfo.author} demonstra como esses princípios podem ser utilizados para promover saúde mental e bem-estar.\n\nO autor apresenta técnicas e intervenções baseadas em evidências, explicando quando e como aplicá-las efetivamente. Cada abordagem é contextualizada dentro do framework teórico maior.\n\nA ênfase está em compreender a pessoa como um todo, considerando fatores biológicos, psicológicos e sociais.`
      },
      {
        id: 'chapter3',
        title: 'Implicações e Desenvolvimentos Futuros',
        content: `A parte final explora as implicações mais amplas da teoria para a compreensão humana. ${bookInfo.author} discute como esses insights podem informar futuras pesquisas e práticas.\n\nO livro conclui com reflexões sobre o impacto dessas ideias na sociedade e na forma como entendemos a condição humana. A obra é posicionada dentro do contexto histórico e científico mais amplo.\n\nOs leitores são encorajados a continuar explorando e questionando, mantendo uma mente aberta para novos desenvolvimentos no campo.`
      }
    ],
    key_points: [
      'A mente humana opera através de sistemas complexos e interconectados',
      'Compreender padrões comportamentais facilita mudanças positivas',
      'Teorias psicológicas fornecem frameworks para entender experiências',
      'Aplicação prática requer adaptação às necessidades individuais',
      'Pesquisa contínua expande nossa compreensão da psique humana'
    ],
    practical_exercises: [
      'Observe e registre seus padrões de pensamento por uma semana',
      'Pratique técnicas de autorregulação emocional',
      'Reflita sobre como suas experiências moldaram sua personalidade',
      'Aplique conceitos psicológicos para melhorar relacionamentos'
    ]
  };
}

function generateLiteratureContent(bookInfo) {
  return {
    chapters: [
      {
        id: 'chapter1',
        title: 'Contexto e Estrutura Narrativa',
        content: `${bookInfo.title} representa uma obra significativa na literatura mundial. ${bookInfo.author} constrói uma narrativa rica em simbolismo e profundidade temática.\n\nEste resumo explora os elementos centrais da obra: personagens, enredo, estilo narrativo e contexto histórico-cultural. A análise revela as camadas de significado que tornam esta obra atemporal.\n\nA estrutura narrativa é cuidadosamente examinada para compreender como o autor constrói tensão, desenvolve personagens e transmite suas mensagens centrais.`
      },
      {
        id: 'chapter2',
        title: 'Temas e Simbolismo',
        content: `Esta seção mergulha nos temas universais explorados na obra. ${bookInfo.author} aborda questões fundamentais da condição humana através de metáforas e símbolos poderosos.\n\nOs temas centrais são analisados em profundidade, revelando como se conectam com experiências humanas universais. O simbolismo é decodificado para revelar significados mais profundos.\n\nA obra é posicionada dentro de seu contexto literário e cultural, mostrando sua influência e relevância contínua.`
      },
      {
        id: 'chapter3',
        title: 'Impacto e Legado',
        content: `A parte final examina o impacto duradouro da obra na literatura e cultura. ${bookInfo.author} criou uma obra que transcende seu tempo e contexto original.\n\nO livro conclui com reflexões sobre como a obra continua a ressoar com leitores contemporâneos. Sua relevância atual é explorada através de diferentes perspectivas críticas.\n\nOs leitores são encorajados a desenvolver sua própria interpretação, reconhecendo que grandes obras literárias oferecem múltiplas camadas de significado.`
      }
    ],
    key_points: [
      'A literatura oferece insights profundos sobre a experiência humana',
      'Simbolismo e metáforas enriquecem a compreensão narrativa',
      'Contexto histórico-cultural influencia interpretação',
      'Grandes obras transcendem seu tempo e lugar de origem',
      'Leitura crítica revela múltiplas camadas de significado'
    ],
    practical_exercises: [
      'Identifique os temas centrais e como se desenvolvem na narrativa',
      'Analise o desenvolvimento dos personagens principais',
      'Explore o simbolismo e suas possíveis interpretações',
      'Conecte a obra com questões contemporâneas relevantes'
    ]
  };
}

function generateNeuroscienceContent(bookInfo) {
  return {
    chapters: [
      {
        id: 'chapter1',
        title: 'Fundamentos Neurobiológicos',
        content: `${bookInfo.title} explora os mecanismos neurobiológicos que governam o comportamento humano. ${bookInfo.author} apresenta descobertas científicas revolucionárias sobre o funcionamento do cérebro.\n\nEsta obra examina como estruturas neurais específicas influenciam cognição, emoção e comportamento. As pesquisas apresentadas oferecem insights únicos sobre a natureza da consciência e experiência humana.\n\nOs conceitos são apresentados de forma acessível, tornando descobertas científicas complexas compreensíveis para um público amplo.`
      },
      {
        id: 'chapter2',
        title: 'Implicações Comportamentais',
        content: `Esta seção conecta descobertas neurobiológicas com comportamentos observáveis. ${bookInfo.author} demonstra como o conhecimento do cérebro pode informar nossa compreensão de ações humanas.\n\nO autor explora como fatores neurobiológicos influenciam tomada de decisões, formação de memórias e processamento emocional. Cada conceito é ilustrado com exemplos práticos e estudos de caso.\n\nA ênfase está em compreender como biologia e experiência interagem para moldar quem somos.`
      },
      {
        id: 'chapter3',
        title: 'Aplicações e Futuro da Neurociência',
        content: `A parte final examina as implicações práticas das descobertas neurobiológicas. ${bookInfo.author} discute como esse conhecimento pode ser aplicado em medicina, educação e sociedade.\n\nO livro conclui com reflexões sobre o futuro da neurociência e suas implicações éticas. As possibilidades e limitações da pesquisa cerebral são cuidadosamente consideradas.\n\nOs leitores são encorajados a pensar criticamente sobre como o conhecimento neurobiológico deve informar políticas e práticas sociais.`
      }
    ],
    key_points: [
      'O cérebro é a base biológica de toda experiência humana',
      'Descobertas neurobiológicas revolucionam nossa autocompreensão',
      'Biologia e experiência interagem de formas complexas',
      'Conhecimento neurobiológico tem implicações práticas importantes',
      'Pesquisa cerebral levanta questões éticas significativas'
    ],
    practical_exercises: [
      'Reflita sobre como fatores biológicos influenciam seu comportamento',
      'Explore como conhecimento neurobiológico pode melhorar decisões',
      'Considere implicações éticas da pesquisa cerebral',
      'Aplique insights neurobiológicos para otimizar aprendizado'
    ]
  };
}

function generateHistoryPsychologyContent(bookInfo) {
  return {
    chapters: [
      {
        id: 'chapter1',
        title: 'Contexto Histórico e Análise Psicológica',
        content: `${bookInfo.title} combina rigor histórico com insights psicológicos profundos. ${bookInfo.author} oferece uma análise única que ilumina tanto eventos históricos quanto natureza humana.\n\nEsta obra examina como fatores psicológicos individuais influenciam eventos históricos significativos. A análise revela padrões comportamentais que transcendem épocas específicas.\n\nA abordagem interdisciplinar oferece perspectivas únicas sobre como personalidade, motivação e contexto social se combinam para moldar a história.`
      },
      {
        id: 'chapter2',
        title: 'Padrões Comportamentais e Motivações',
        content: `Esta seção explora os padrões psicológicos subjacentes aos eventos históricos. ${bookInfo.author} identifica motivações e dinâmicas que explicam comportamentos aparentemente inexplicáveis.\n\nO autor demonstra como compreender psicologia individual pode iluminar fenômenos históricos complexos. Cada análise é baseada em evidências históricas e teorias psicológicas estabelecidas.\n\nA ênfase está em extrair lições universais sobre natureza humana a partir de casos históricos específicos.`
      },
      {
        id: 'chapter3',
        title: 'Lições para o Presente',
        content: `A parte final conecta insights histórico-psicológicos com questões contemporâneas. ${bookInfo.author} demonstra como compreender o passado pode informar decisões presentes.\n\nO livro conclui com reflexões sobre como padrões históricos podem se repetir e como reconhecê-los. A análise oferece ferramentas para compreender eventos atuais através de lentes históricas.\n\nOs leitores são equipados com frameworks para analisar criticamente tanto eventos históricos quanto contemporâneos.`
      }
    ],
    key_points: [
      'História e psicologia se iluminam mutuamente',
      'Padrões comportamentais transcendem épocas específicas',
      'Compreender motivações individuais explica eventos coletivos',
      'Análise histórico-psicológica oferece insights únicos',
      'Lições do passado informam compreensão do presente'
    ],
    practical_exercises: [
      'Analise eventos atuais através de perspectivas históricas',
      'Identifique padrões comportamentais em diferentes contextos',
      'Explore como fatores psicológicos influenciam decisões coletivas',
      'Desenvolva pensamento crítico sobre narrativas históricas'
    ]
  };
}

function generateGenericContent(bookInfo) {
  return {
    chapters: [
      {
        id: 'chapter1',
        title: 'Conceitos Fundamentais',
        content: `${bookInfo.title} apresenta ideias importantes sobre ${bookInfo.category.toLowerCase()}. ${bookInfo.author} oferece perspectivas valiosas que enriquecem nossa compreensão do tema.\n\nEsta obra explora conceitos centrais de forma clara e acessível, fornecendo uma base sólida para compreensão mais profunda. Os princípios apresentados são tanto teoricamente fundamentados quanto praticamente aplicáveis.\n\nO autor demonstra expertise no assunto, oferecendo insights únicos baseados em pesquisa e experiência prática.`
      },
      {
        id: 'chapter2',
        title: 'Desenvolvimento e Aplicação',
        content: `Esta seção desenvolve os conceitos introduzidos, oferecendo exemplos práticos e estudos de caso. ${bookInfo.author} demonstra como aplicar os princípios em situações reais.\n\nO autor apresenta estratégias e técnicas específicas, explicando quando e como utilizá-las efetivamente. Cada abordagem é contextualizada dentro do framework teórico maior.\n\nA ênfase está em fornecer ferramentas práticas que os leitores podem implementar imediatamente.`
      },
      {
        id: 'chapter3',
        title: 'Síntese e Perspectivas Futuras',
        content: `A parte final sintetiza os principais aprendizados e oferece perspectivas sobre desenvolvimentos futuros. ${bookInfo.author} conecta os conceitos apresentados com tendências emergentes.\n\nO livro conclui com reflexões sobre como os insights podem ser aplicados em diferentes contextos. A obra é posicionada dentro do panorama mais amplo do conhecimento na área.\n\nOs leitores são encorajados a continuar explorando e aplicando os conceitos aprendidos.`
      }
    ],
    key_points: [
      'Conceitos fundamentais fornecem base sólida para compreensão',
      'Aplicação prática maximiza o valor dos insights teóricos',
      'Exemplos e estudos de caso facilitam compreensão',
      'Integração de teoria e prática oferece valor máximo',
      'Aprendizado contínuo é essencial para domínio do assunto'
    ],
    practical_exercises: [
      'Identifique como os conceitos se aplicam à sua situação',
      'Pratique as técnicas apresentadas em contextos reais',
      'Reflita sobre como integrar os aprendizados ao seu dia a dia',
      'Explore recursos adicionais para aprofundar conhecimento'
    ]
  };
}

// Função para completar o mapeamento de livros não mapeados
function completeBookMapping(filename) {
  const nameWithoutExt = filename.replace('.pdf', '');

  if (COMPLETE_BOOK_MAPPINGS[nameWithoutExt]) {
    return COMPLETE_BOOK_MAPPINGS[nameWithoutExt];
  }

  // Para livros não mapeados, criar informações baseadas no nome do arquivo
  const title = nameWithoutExt
    .replace(/_/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase())
    .replace(/ \d+$/, ''); // Remove números no final

  return {
    title,
    author: 'Autor Desconhecido',
    category: 'Geral',
    description: `Resumo profissional de "${title}" - uma obra importante que oferece insights valiosos sobre seu tema.`,
    duration: 30,
    difficulty: 'Intermediário'
  };
}

// Função principal para processar todos os livros
async function processAllBooks() {
  try {
    console.log('🚀 Iniciando processamento completo de todos os livros...\n');

    const files = fs.readdirSync(PDF_FOLDER).filter(file => file.endsWith('.pdf'));
    console.log(`📚 Total de ${files.length} livros para processar\n`);

    let processedCount = 0;
    let errorCount = 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`📖 [${i + 1}/${files.length}] Processando: ${file}`);

      try {
        // Obter informações do livro
        const bookInfo = completeBookMapping(file);
        console.log(`   📝 Título: ${bookInfo.title}`);
        console.log(`   ✍️  Autor: ${bookInfo.author}`);
        console.log(`   📂 Categoria: ${bookInfo.category}`);

        // Gerar conteúdo estruturado
        const structuredContent = generateStructuredContent(bookInfo);

        // Verificar se o livro já existe no banco
        const { data: existingBook, error: searchError } = await supabase
          .from('books')
          .select('id')
          .eq('title', bookInfo.title)
          .single();

        let bookId;

        if (existingBook) {
          bookId = existingBook.id;
          console.log(`   ✅ Livro encontrado no banco (ID: ${bookId})`);
        } else {
          // Inserir novo livro
          const { data: newBook, error: insertError } = await supabase
            .from('books')
            .insert({
              title: bookInfo.title,
              author: bookInfo.author,
              category: bookInfo.category,
              description: bookInfo.description,
              duration: bookInfo.duration,
              difficulty: bookInfo.difficulty,
              is_featured: false,
              is_free: true,
              pdf_key: file
            })
            .select('id')
            .single();

          if (insertError) {
            console.error(`   ❌ Erro ao inserir livro: ${insertError.message}`);
            errorCount++;
            continue;
          }

          bookId = newBook.id;
          console.log(`   ✅ Novo livro criado (ID: ${bookId})`);
        }

        // Inserir ou atualizar conteúdo
        const { error: contentError } = await supabase
          .from('book_contents')
          .upsert({
            book_id: bookId,
            content: structuredContent
          });

        if (contentError) {
          console.error(`   ❌ Erro ao inserir conteúdo: ${contentError.message}`);
          errorCount++;
        } else {
          console.log(`   ✅ Conteúdo padronizado inserido com sucesso!`);
          processedCount++;
        }

        console.log(''); // Linha em branco para separar

      } catch (error) {
        console.error(`   ❌ Erro ao processar ${file}: ${error.message}`);
        errorCount++;
        console.log('');
      }
    }

    console.log('🎉 PROCESSAMENTO CONCLUÍDO!');
    console.log(`✅ Livros processados com sucesso: ${processedCount}`);
    console.log(`❌ Erros encontrados: ${errorCount}`);
    console.log(`📊 Taxa de sucesso: ${((processedCount / files.length) * 100).toFixed(1)}%`);

  } catch (error) {
    console.error('💥 Erro geral no processamento:', error);
  }
}

// Executar o processamento
processAllBooks();
