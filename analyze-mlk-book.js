import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function analyzeMLKBook() {
  try {
    console.log('🔍 ANALYZING MARTIN LUTHER KING JR. BOOK\n');

    // Find the MLK book
    const { data: books, error: booksError } = await supabase
      .from('books')
      .select('*')
      .ilike('title', '%apelo%consciência%')
      .or('author.ilike.%martin%,author.ilike.%king%');

    if (booksError) {
      console.log('❌ Error fetching books:', booksError.message);
      return;
    }

    if (!books || books.length === 0) {
      console.log('❌ MLK book not found. Searching all books...');
      
      // Search all books to find MLK
      const { data: allBooks, error: allBooksError } = await supabase
        .from('books')
        .select('*');
        
      if (allBooksError) {
        console.log('❌ Error fetching all books:', allBooksError.message);
        return;
      }
      
      console.log('📚 All books in database:');
      allBooks.forEach(book => {
        console.log(`   ID: ${book.id} - "${book.title}" by ${book.author}`);
      });
      return;
    }

    const mlkBook = books[0];
    console.log('✅ Found MLK book:');
    console.log(`   ID: ${mlkBook.id}`);
    console.log(`   Current Title: "${mlkBook.title}"`);
    console.log(`   Current Author: "${mlkBook.author}"`);
    console.log(`   Description: "${mlkBook.description?.substring(0, 100)}..."`);
    console.log(`   Created: ${mlkBook.created_at}`);

    // Get book content
    const { data: contentData, error: contentError } = await supabase
      .from('book_contents')
      .select('content')
      .eq('book_id', mlkBook.id)
      .single();

    if (contentError || !contentData) {
      console.log('❌ Error fetching content:', contentError?.message);
      return;
    }

    console.log('\n📖 CONTENT ANALYSIS:');
    const content = contentData.content;
    
    if (content.chapters && content.chapters.length > 0) {
      console.log(`   Chapters: ${content.chapters.length}`);
      
      content.chapters.forEach((chapter, index) => {
        console.log(`\n   Chapter ${index + 1}:`);
        console.log(`     Title: "${chapter.title}"`);
        console.log(`     Content length: ${chapter.content?.length || 0} characters`);
        
        if (chapter.content) {
          // Look for specific content patterns
          const hasIntroduction = chapter.content.includes('INTRODUÇÃO E CONTEXTUALIZAÇÃO');
          const has1955Content = chapter.content.includes('1955');
          const hasMLKSpeeches = chapter.content.includes('discurso') || chapter.content.includes('speech');
          
          console.log(`     Has introduction section: ${hasIntroduction}`);
          console.log(`     Has 1955 content: ${has1955Content}`);
          console.log(`     Has speech content: ${hasMLKSpeeches}`);
          
          // Show first 300 characters
          console.log(`     Preview: "${chapter.content.substring(0, 300)}..."`);
          
          // Check for structure issues
          const lines = chapter.content.split('\n');
          const longLines = lines.filter(line => line.length > 200);
          const potentialTitles = lines.filter(line => 
            line.includes('INTRODUÇÃO') || 
            line.includes('1955') || 
            line.includes('DISCURSO') ||
            /^[A-Z\s]{10,50}$/.test(line.trim())
          );
          
          console.log(`     Total lines: ${lines.length}`);
          console.log(`     Long lines (>200 chars): ${longLines.length}`);
          console.log(`     Potential titles found: ${potentialTitles.length}`);
          
          if (potentialTitles.length > 0) {
            console.log(`     Potential titles:`);
            potentialTitles.slice(0, 5).forEach(title => {
              console.log(`       - "${title.trim()}"`);
            });
          }
        }
      });
    }

    // Check for key points and other content
    if (content.key_points) {
      console.log(`\n   Key points: ${content.key_points.length}`);
    }
    
    if (content.practical_exercises) {
      console.log(`   Practical exercises: ${content.practical_exercises.length}`);
    }

    console.log(`\n   Total characters: ${content.total_characters || 'unknown'}`);
    console.log(`   Total pages: ${content.total_pages || 'unknown'}`);

    // Recommendations for fixes
    console.log('\n💡 RECOMMENDATIONS FOR FIXES:');
    
    // Title and author fixes
    const needsTitleFix = !mlkBook.title.includes('Um Apelo à Consciência: Os Melhores Discursos');
    const needsAuthorFix = mlkBook.author !== 'Martin Luther King Jr.';
    
    console.log(`${needsTitleFix ? '⚠️' : '✅'} Title: ${needsTitleFix ? 'NEEDS UPDATE' : 'CORRECT'}`);
    console.log(`${needsAuthorFix ? '⚠️' : '✅'} Author: ${needsAuthorFix ? 'NEEDS UPDATE' : 'CORRECT'}`);
    
    if (needsTitleFix) {
      console.log(`   Current: "${mlkBook.title}"`);
      console.log(`   Should be: "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King"`);
    }
    
    if (needsAuthorFix) {
      console.log(`   Current: "${mlkBook.author}"`);
      console.log(`   Should be: "Martin Luther King Jr."`);
    }

    // Content structure fixes
    if (content.chapters && content.chapters.length > 0) {
      const chapter = content.chapters[0];
      const needsStructureFix = chapter.content && (
        !chapter.content.includes('## ') && 
        chapter.content.includes('INTRODUÇÃO E CONTEXTUALIZAÇÃO')
      );
      
      console.log(`${needsStructureFix ? '⚠️' : '✅'} Content structure: ${needsStructureFix ? 'NEEDS FORMATTING' : 'GOOD'}`);
      
      if (needsStructureFix) {
        console.log('   Issues found:');
        console.log('   - Missing proper chapter/section headers');
        console.log('   - Content appears to be in one large block');
        console.log('   - "INTRODUÇÃO E CONTEXTUALIZAÇÃO" should be a section header');
        console.log('   - 1955 content should be properly separated');
      }
    }

    return {
      bookId: mlkBook.id,
      currentTitle: mlkBook.title,
      currentAuthor: mlkBook.author,
      needsTitleFix,
      needsAuthorFix,
      content: content
    };

  } catch (error) {
    console.error('💥 Error during analysis:', error);
  }
}

analyzeMLKBook();
