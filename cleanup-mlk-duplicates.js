import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function cleanupMLKDuplicates() {
  try {
    console.log('🧹 LIMPANDO DUPLICATAS DO MLK\n');

    // IDs dos livros MLK
    const mlkBookIds = [36, 54, 85];
    
    console.log('📊 Analisando conteúdo de cada livro MLK...\n');

    for (const bookId of mlkBookIds) {
      console.log(`📚 Livro ID ${bookId}:`);
      
      // Buscar informações do livro
      const { data: book, error: bookError } = await supabase
        .from('books')
        .select('*')
        .eq('id', bookId)
        .single();

      if (bookError) {
        console.log(`   ❌ Erro ao buscar livro: ${bookError.message}`);
        continue;
      }

      console.log(`   Título: "${book.title}"`);
      console.log(`   Autor: "${book.author}"`);
      console.log(`   Descrição: "${book.description?.substring(0, 80)}..."`);

      // Buscar conteúdo
      const { data: content, error: contentError } = await supabase
        .from('book_contents')
        .select('*')
        .eq('book_id', bookId);

      if (contentError) {
        console.log(`   ❌ Erro ao buscar conteúdo: ${contentError.message}`);
      } else {
        console.log(`   Conteúdo: ${content.length} entrada(s)`);
        
        if (content.length > 0) {
          const contentEntry = content[0];
          const contentType = typeof contentEntry.content;
          
          if (contentType === 'object' && contentEntry.content.chapters) {
            console.log(`   Capítulos: ${contentEntry.content.chapters.length}`);
            console.log(`   Pontos-chave: ${contentEntry.content.key_points?.length || 0}`);
            console.log(`   Qualidade: ESTRUTURADO ✅`);
          } else if (contentType === 'string') {
            console.log(`   Texto: ${contentEntry.content.length} caracteres`);
            console.log(`   Qualidade: TEXTO SIMPLES`);
          } else {
            console.log(`   Tipo: ${contentType}`);
            console.log(`   Qualidade: DESCONHECIDA`);
          }
        } else {
          console.log(`   Qualidade: SEM CONTEÚDO ❌`);
        }
      }
      console.log('');
    }

    // Recomendação baseada na análise
    console.log('💡 RECOMENDAÇÃO DE LIMPEZA:');
    console.log('');
    console.log('📚 MANTER: Livro ID 85');
    console.log('   ✅ Título completo: "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King"');
    console.log('   ✅ Autor correto: "Martin Luther King Jr."');
    console.log('   ✅ Descrição completa e informativa');
    console.log('   ✅ Tem conteúdo estruturado (se disponível)');
    console.log('');
    console.log('🗑️ REMOVER: Livros ID 36 e 54 (duplicatas)');
    console.log('   • Títulos incompletos ou menos descritivos');
    console.log('   • Informações redundantes');
    console.log('');

    // Perguntar confirmação (simulada)
    console.log('⚠️ ATENÇÃO: Esta operação irá remover livros permanentemente!');
    console.log('');
    console.log('🎯 AÇÕES RECOMENDADAS:');
    console.log('1. Manter apenas o livro ID 85 (título mais completo)');
    console.log('2. Remover livros ID 36 e 54 para evitar confusão');
    console.log('3. Verificar se o livro ID 85 tem todo o conteúdo necessário');
    console.log('');
    console.log('✅ RESULTADO FINAL:');
    console.log('   • Apenas um livro do MLK na biblioteca');
    console.log('   • Título correto e completo');
    console.log('   • Autor correto: "Martin Luther King Jr."');
    console.log('   • Sem duplicatas confusas');
    console.log('');
    console.log('📚 O livro agora aparecerá na biblioteca como:');
    console.log('   "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King"');
    console.log('   por Martin Luther King Jr.');

    // Verificar se o livro ID 85 tem conteúdo adequado
    console.log('\n🔍 Verificando conteúdo do livro principal (ID 85)...');
    
    const { data: mainContent, error: mainError } = await supabase
      .from('book_contents')
      .select('*')
      .eq('book_id', 85);

    if (mainError) {
      console.log('❌ Erro ao verificar conteúdo principal:', mainError.message);
    } else if (mainContent.length === 0) {
      console.log('⚠️ O livro ID 85 não tem conteúdo estruturado');
      console.log('💡 Recomendação: Copiar conteúdo do livro ID 36 para o ID 85 antes de remover duplicatas');
    } else {
      console.log('✅ O livro ID 85 tem conteúdo adequado');
      console.log('   Pode prosseguir com a remoção das duplicatas');
    }

  } catch (error) {
    console.error('💥 Erro durante limpeza:', error);
  }
}

cleanupMLKDuplicates();
