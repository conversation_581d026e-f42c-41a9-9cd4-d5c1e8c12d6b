import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testSupabaseConnection() {
  try {
    console.log('🚀 Testando conexão com Supabase...\n');

    // Teste 1: Consulta básica de livros
    console.log('📚 Teste 1: Consultando todos os livros...');
    const { data: allBooks, error: allBooksError } = await supabase
      .from('books')
      .select('*')
      .order('is_featured', { ascending: false });

    if (allBooksError) {
      console.error('❌ Erro ao consultar livros:', allBooksError);
    } else {
      console.log(`✅ Total de livros encontrados: ${allBooks.length}`);
      console.log('📋 Primeiros 5 livros:');
      allBooks.slice(0, 5).forEach((book, index) => {
        console.log(`   ${index + 1}. ${book.title} - ${book.author} (Featured: ${book.is_featured})`);
      });
    }

    // Teste 2: Consulta de livros em destaque
    console.log('\n⭐ Teste 2: Consultando livros em destaque...');
    const { data: featuredBooks, error: featuredError } = await supabase
      .from('books')
      .select('*')
      .eq('is_featured', true)
      .order('id');

    if (featuredError) {
      console.error('❌ Erro ao consultar livros em destaque:', featuredError);
    } else {
      console.log(`✅ Livros em destaque encontrados: ${featuredBooks.length}`);
      featuredBooks.slice(0, 5).forEach((book, index) => {
        console.log(`   ${index + 1}. ${book.title} - ${book.author}`);
      });
    }

    // Teste 3: Consulta de conteúdo de livros
    console.log('\n📖 Teste 3: Verificando conteúdo dos livros...');
    const { data: booksWithContent, error: contentError } = await supabase
      .from('books')
      .select(`
        id,
        title,
        author,
        book_contents (
          content
        )
      `)
      .limit(5);

    if (contentError) {
      console.error('❌ Erro ao consultar conteúdo:', contentError);
    } else {
      console.log(`✅ Livros com dados de conteúdo: ${booksWithContent.length}`);
      booksWithContent.forEach((book, index) => {
        const hasContent = book.book_contents && book.book_contents.length > 0;
        console.log(`   ${index + 1}. ${book.title} - Tem conteúdo: ${hasContent}`);
      });
    }

    // Teste 4: Testar consulta específica de um livro
    console.log('\n🔍 Teste 4: Consultando livro específico (ID: 1)...');
    const { data: specificBook, error: specificError } = await supabase
      .from('books')
      .select('*')
      .eq('id', 1)
      .single();

    if (specificError) {
      console.error('❌ Erro ao consultar livro específico:', specificError);
    } else {
      console.log('✅ Livro específico encontrado:');
      console.log(`   📝 Título: ${specificBook.title}`);
      console.log(`   ✍️ Autor: ${specificBook.author}`);
      console.log(`   📂 Categoria: ${specificBook.category}`);
      console.log(`   ⭐ Featured: ${specificBook.is_featured}`);
      console.log(`   🆓 Free: ${specificBook.is_free}`);
    }

    // Teste 5: Verificar conteúdo específico
    console.log('\n📄 Teste 5: Verificando conteúdo do livro ID 1...');
    const { data: bookContent, error: bookContentError } = await supabase
      .from('book_contents')
      .select('content')
      .eq('book_id', 1)
      .single();

    if (bookContentError) {
      console.error('❌ Erro ao consultar conteúdo do livro:', bookContentError);
    } else {
      console.log('✅ Conteúdo encontrado:');
      const content = bookContent.content;
      console.log(`   📚 Capítulos: ${content.chapters?.length || 0}`);
      console.log(`   🔑 Pontos-chave: ${content.key_points?.length || 0}`);
      console.log(`   💡 Exercícios: ${content.practical_exercises?.length || 0}`);
      
      if (content.chapters && content.chapters.length > 0) {
        console.log('   📖 Primeiro capítulo:');
        console.log(`      Título: ${content.chapters[0].title}`);
        console.log(`      Conteúdo: ${content.chapters[0].content.substring(0, 100)}...`);
      }
    }

    console.log('\n🎉 Todos os testes concluídos!');

  } catch (error) {
    console.error('💥 Erro geral:', error);
  }
}

testSupabaseConnection();
