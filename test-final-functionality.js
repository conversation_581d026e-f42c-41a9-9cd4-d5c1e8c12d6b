import puppeteer from 'puppeteer';

async function testFinalFunctionality() {
  let browser;
  try {
    console.log('🎯 TESTE FINAL: Verificando se o conteúdo real está sendo exibido\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Capturar logs do console
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('Loading book') || text.includes('BookLoader') || text.includes('content')) {
        console.log(`🔵 Console: ${text}`);
      }
    });

    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Fazer login
    console.log('🔐 Fazendo login...');
    const loginClicked = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button, a'));
      const loginButton = buttons.find(btn => {
        const text = btn.textContent?.toLowerCase() || '';
        return text.includes('entrar');
      });
      
      if (loginButton) {
        loginButton.click();
        return true;
      }
      return false;
    });
    
    if (loginClicked) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      try {
        await page.type('input[type="email"]', '<EMAIL>', { delay: 50 });
        await page.type('input[type="password"]', 'password123', { delay: 50 });
        
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
          await submitButton.click();
          await new Promise(resolve => setTimeout(resolve, 5000));
          console.log('✅ Login realizado');
        }
      } catch (error) {
        console.log('⚠️ Erro no login, continuando...');
      }
    }

    // Navegar para biblioteca
    console.log('📚 Navegando para biblioteca...');
    const libraryClicked = await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a, button'));
      const libraryLink = links.find(link => {
        const text = link.textContent?.toLowerCase() || '';
        return text.includes('biblioteca');
      });
      
      if (libraryLink) {
        libraryLink.click();
        return true;
      }
      return false;
    });
    
    if (libraryClicked) {
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log('✅ Navegou para biblioteca');
    }

    // Procurar por livros específicos que sabemos que têm conteúdo real
    console.log('🔍 Procurando por livros com conteúdo real...');
    
    const booksFound = await page.evaluate(() => {
      const allText = document.body.innerText;
      const books = [];
      
      // Livros que sabemos que têm conteúdo real
      const targetBooks = [
        'Pai Rico, Pai Pobre',
        'A Sutil Arte',
        'Interpretação dos Sonhos',
        'Martin Luther King'
      ];
      
      for (const bookTitle of targetBooks) {
        if (allText.includes(bookTitle)) {
          books.push(bookTitle);
        }
      }
      
      return books;
    });

    console.log(`📖 Livros encontrados: ${booksFound.join(', ')}`);

    if (booksFound.length > 0) {
      // Tentar clicar em um livro específico
      console.log('🖱️ Tentando clicar em "Pai Rico, Pai Pobre"...');
      
      const clickResult = await page.evaluate(() => {
        // Procurar especificamente por "Pai Rico, Pai Pobre"
        const allElements = Array.from(document.querySelectorAll('*'));
        const paiRicoElements = allElements.filter(el => {
          const text = el.textContent || '';
          return text.includes('Pai Rico') && text.length < 500;
        });

        for (const element of paiRicoElements) {
          // Procurar por botão "Ler Agora" dentro ou próximo deste elemento
          const button = element.querySelector('button') || 
                        element.closest('[class*="card"]')?.querySelector('button') ||
                        element.parentElement?.querySelector('button');
          
          if (button) {
            const buttonText = button.textContent?.toLowerCase() || '';
            if (buttonText.includes('ler')) {
              button.click();
              return { 
                success: true, 
                buttonText: button.textContent?.trim(),
                bookTitle: 'Pai Rico, Pai Pobre'
              };
            }
          }
        }
        
        return { success: false };
      });

      if (clickResult.success) {
        console.log(`✅ Clicou em: ${clickResult.bookTitle}`);
        console.log(`   Botão: "${clickResult.buttonText}"`);
        
        // Aguardar carregamento da página de leitura
        console.log('⏳ Aguardando carregamento da página de leitura...');
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        // Verificar se o conteúdo real está sendo exibido
        console.log('🔍 Analisando conteúdo da página de leitura...');
        
        const contentAnalysis = await page.evaluate(() => {
          const bodyText = document.body.innerText;
          
          // Verificar por conteúdo específico do Pai Rico, Pai Pobre
          const hasRealContent = bodyText.includes('Robert Kiyosaki') ||
                                 bodyText.includes('ativo') ||
                                 bodyText.includes('passivo') ||
                                 bodyText.includes('investimento') ||
                                 bodyText.includes('educação financeira');

          // Verificar por conteúdo genérico (que NÃO deveria estar lá)
          const hasGenericContent = bodyText.includes('Este é um resumo profissional') ||
                                   bodyText.includes('Nossa metodologia garante') ||
                                   bodyText.includes('Livro Não Encontrado') ||
                                   bodyText.includes('Conteúdo não disponível');

          // Verificar tamanho do conteúdo
          const hasSubstantialContent = bodyText.length > 5000;

          return {
            hasRealContent,
            hasGenericContent,
            hasSubstantialContent,
            contentLength: bodyText.length,
            contentPreview: bodyText.substring(0, 800),
            title: document.title,
            url: window.location.href
          };
        });

        console.log('\n📊 ANÁLISE DO CONTEÚDO:');
        console.log(`📝 Título da página: ${contentAnalysis.title}`);
        console.log(`🌐 URL: ${contentAnalysis.url}`);
        console.log(`📏 Tamanho do conteúdo: ${contentAnalysis.contentLength.toLocaleString()} caracteres`);
        console.log(`✅ Conteúdo real detectado: ${contentAnalysis.hasRealContent ? 'SIM' : 'NÃO'}`);
        console.log(`❌ Conteúdo genérico detectado: ${contentAnalysis.hasGenericContent ? 'SIM' : 'NÃO'}`);
        console.log(`📚 Conteúdo substancial: ${contentAnalysis.hasSubstantialContent ? 'SIM' : 'NÃO'}`);
        
        console.log('\n📄 Preview do conteúdo:');
        console.log(contentAnalysis.contentPreview + '...');

        // Resultado final
        console.log('\n🎯 RESULTADO FINAL:');
        if (contentAnalysis.hasRealContent && !contentAnalysis.hasGenericContent && contentAnalysis.hasSubstantialContent) {
          console.log('🎉 SUCESSO TOTAL! O problema foi RESOLVIDO!');
          console.log('✅ O livro agora mostra conteúdo REAL extraído do PDF');
          console.log('✅ Não há mais conteúdo genérico ou placeholder');
          console.log('✅ Conteúdo substancial está sendo exibido');
          console.log('\n🏆 MISSÃO CUMPRIDA: Usuários agora veem conteúdo autêntico dos PDFs!');
        } else if (contentAnalysis.hasRealContent && contentAnalysis.hasSubstantialContent) {
          console.log('⚠️ SUCESSO PARCIAL: Tem conteúdo real, mas pode ter alguns elementos genéricos');
        } else if (contentAnalysis.hasGenericContent) {
          console.log('❌ PROBLEMA PERSISTE: Ainda mostra conteúdo genérico');
          console.log('   Verifique se o BookLoader está carregando o conteúdo correto');
        } else {
          console.log('❓ RESULTADO INCERTO: Não foi possível determinar o tipo de conteúdo');
        }

      } else {
        console.log('❌ Não foi possível clicar em nenhum livro');
      }
    } else {
      console.log('❌ Nenhum livro conhecido encontrado na biblioteca');
    }

    // Screenshot final
    await page.screenshot({ 
      path: 'final-functionality-test.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot salvo como final-functionality-test.png');

    await new Promise(resolve => setTimeout(resolve, 5000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testFinalFunctionality();
