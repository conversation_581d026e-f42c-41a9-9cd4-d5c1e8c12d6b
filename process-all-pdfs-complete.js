import fs from 'fs';
import { processCompletePDF } from './extract-complete-pdf-content.js';

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

async function processAllPDFsCompletely() {
  try {
    console.log('🚀 INICIANDO PROCESSAMENTO COMPLETO DE TODOS OS PDFs');
    console.log('=' * 80);
    console.log('📋 OBJETIVO: Extrair TODO o conteúdo de cada PDF (todas as páginas)');
    console.log('🎯 META: Preservar conteúdo integral sem limitações\n');
    
    // Listar todos os PDFs
    const files = fs.readdirSync(PDF_FOLDER).filter(file => file.endsWith('.pdf'));
    console.log(`📚 Total de PDFs encontrados: ${files.length}\n`);
    
    // Estatísticas
    let processedCount = 0;
    let errorCount = 0;
    let totalPages = 0;
    let totalCharacters = 0;
    const startTime = Date.now();
    
    // Processar cada PDF completamente
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`\n📖 [${i + 1}/${files.length}] PROCESSANDO: ${file}`);
      console.log(`⏱️ Tempo decorrido: ${Math.round((Date.now() - startTime) / 1000)}s`);
      console.log(`📊 Progresso: ${((i / files.length) * 100).toFixed(1)}%`);
      
      try {
        const success = await processCompletePDF(file);
        
        if (success) {
          processedCount++;
          console.log(`✅ [${i + 1}/${files.length}] SUCESSO: ${file}`);
        } else {
          errorCount++;
          console.log(`❌ [${i + 1}/${files.length}] FALHA: ${file}`);
        }
        
        // Pequena pausa para não sobrecarregar o sistema
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        errorCount++;
        console.error(`💥 [${i + 1}/${files.length}] ERRO: ${file} - ${error.message}`);
      }
      
      // Log de progresso a cada 5 arquivos
      if ((i + 1) % 5 === 0) {
        console.log(`\n📊 PROGRESSO INTERMEDIÁRIO:`);
        console.log(`   ✅ Processados com sucesso: ${processedCount}`);
        console.log(`   ❌ Erros: ${errorCount}`);
        console.log(`   📈 Taxa de sucesso: ${((processedCount / (i + 1)) * 100).toFixed(1)}%`);
        console.log(`   ⏱️ Tempo médio por PDF: ${Math.round((Date.now() - startTime) / (i + 1) / 1000)}s`);
      }
    }
    
    const endTime = Date.now();
    const totalTime = Math.round((endTime - startTime) / 1000);
    
    console.log('\n' + '=' * 80);
    console.log('🎉 PROCESSAMENTO COMPLETO FINALIZADO!');
    console.log('=' * 80);
    
    console.log(`\n📊 ESTATÍSTICAS FINAIS:`);
    console.log(`   📚 Total de PDFs: ${files.length}`);
    console.log(`   ✅ Processados com sucesso: ${processedCount}`);
    console.log(`   ❌ Erros: ${errorCount}`);
    console.log(`   📈 Taxa de sucesso: ${((processedCount / files.length) * 100).toFixed(1)}%`);
    console.log(`   ⏱️ Tempo total: ${Math.floor(totalTime / 60)}m ${totalTime % 60}s`);
    console.log(`   ⚡ Tempo médio por PDF: ${Math.round(totalTime / files.length)}s`);
    
    if (processedCount > 0) {
      console.log(`\n🎯 RESULTADO:`);
      console.log(`   📖 ${processedCount} livros agora têm conteúdo COMPLETO extraído dos PDFs`);
      console.log(`   📄 TODO o conteúdo de cada PDF foi preservado (todas as páginas)`);
      console.log(`   🏗️ Estrutura original mantida (capítulos, seções, formatação)`);
      console.log(`   🔍 Conteúdo integral disponível para leitura`);
    }
    
    if (errorCount > 0) {
      console.log(`\n⚠️ ATENÇÃO:`);
      console.log(`   ${errorCount} PDFs tiveram problemas na extração`);
      console.log(`   Verifique os logs acima para detalhes dos erros`);
    }
    
    console.log(`\n🚀 PRÓXIMOS PASSOS:`);
    console.log(`   1. Teste a funcionalidade no navegador`);
    console.log(`   2. Clique em "Ler Agora" em qualquer livro`);
    console.log(`   3. Verifique se o conteúdo completo está sendo exibido`);
    console.log(`   4. Confirme que não há limitações de páginas`);
    
    console.log(`\n✨ MISSÃO CUMPRIDA: Conteúdo integral dos PDFs extraído!`);
    
  } catch (error) {
    console.error('💥 Erro geral no processamento:', error);
  }
}

// Executar o processamento completo
processAllPDFsCompletely();
