import puppeteer from 'puppeteer';

async function finalContentVerification() {
  let browser;
  try {
    console.log('🎯 VERIFICAÇÃO FINAL: Testando conte<PERSON>do real vs gen<PERSON><PERSON><PERSON>\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Testar BookLoader diretamente no console do navegador
    console.log('🧪 Testando BookLoader diretamente no console...');
    
    const testResults = await page.evaluate(async () => {
      try {
        // Importar BookLoader
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        
        const results = {};
        
        // Testar livros específicos
        const testBooks = [
          { id: '7', name: 'Freud - A Interpretação dos Sonhos' },
          { id: '4', name: '<PERSON> - A Sutil Arte' },
          { id: '2', name: '<PERSON> Duhigg - O Poder do Hábito' }
        ];

        for (const testBook of testBooks) {
          console.log(`Testing book ${testBook.id}: ${testBook.name}`);
          
          try {
            // Carregar livro
            const book = await BookLoader.loadBook(testBook.id);
            
            if (book) {
              const firstChapter = book.content?.chapters?.[0];
              const content = firstChapter?.content || '';
              
              // Verificar se é conteúdo específico ou genérico
              const isSpecific = checkIfSpecificContent(testBook.name, content);
              const isGeneric = checkIfGenericContent(content);
              
              results[testBook.id] = {
                name: testBook.name,
                title: book.title,
                author: book.author,
                hasContent: !!firstChapter,
                chapterTitle: firstChapter?.title || 'N/A',
                contentLength: content.length,
                contentPreview: content.substring(0, 200),
                isSpecific,
                isGeneric,
                status: isSpecific && !isGeneric ? 'REAL' : 
                       isGeneric ? 'GENERIC' : 'UNKNOWN'
              };
            } else {
              results[testBook.id] = {
                name: testBook.name,
                error: 'Book not found'
              };
            }
          } catch (error) {
            results[testBook.id] = {
              name: testBook.name,
              error: error.message
            };
          }
        }
        
        return results;
        
      } catch (error) {
        return { error: error.message };
      }
    });

    // Função auxiliar para verificar conteúdo específico
    await page.addScriptTag({
      content: `
        function checkIfSpecificContent(bookName, content) {
          const lowerContent = content.toLowerCase();
          
          if (bookName.includes('Freud')) {
            return lowerContent.includes('freud') || 
                   lowerContent.includes('sonho') || 
                   lowerContent.includes('inconsciente') ||
                   lowerContent.includes('psicanálise') ||
                   lowerContent.includes('via régia');
          } else if (bookName.includes('Mark Manson')) {
            return lowerContent.includes('mark manson') || 
                   lowerContent.includes('não tente') || 
                   lowerContent.includes('positivo o tempo todo') ||
                   lowerContent.includes('f*da-se');
          } else if (bookName.includes('Charles Duhigg')) {
            return lowerContent.includes('charles duhigg') || 
                   lowerContent.includes('loop') || 
                   lowerContent.includes('deixa') ||
                   lowerContent.includes('rotina') ||
                   lowerContent.includes('recompensa');
          }
          
          return false;
        }
        
        function checkIfGenericContent(content) {
          const lowerContent = content.toLowerCase();
          const genericPhrases = [
            'esta obra examina como pensamentos, emoções e comportamentos se interconectam',
            'apresenta conceitos fundamentais que podem transformar',
            'oferece perspectivas valiosas sobre',
            'explora conceitos centrais',
            'esta seção foca na aplicação prática'
          ];
          
          return genericPhrases.some(phrase => lowerContent.includes(phrase));
        }
      `
    });

    console.log('\n📊 RESULTADOS DOS TESTES:');
    console.log('=' * 50);

    let successCount = 0;
    let totalCount = 0;

    for (const [bookId, result] of Object.entries(testResults)) {
      if (result.error) {
        console.log(`❌ ${result.name}: ERRO - ${result.error}`);
        continue;
      }

      totalCount++;
      console.log(`\n📖 ${result.name}:`);
      console.log(`   📝 Título: ${result.title}`);
      console.log(`   ✍️ Autor: ${result.author}`);
      console.log(`   📄 Capítulo: ${result.chapterTitle}`);
      console.log(`   📏 Tamanho: ${result.contentLength} caracteres`);
      console.log(`   🎯 Status: ${result.status}`);
      
      if (result.status === 'REAL') {
        console.log(`   ✅ SUCESSO: Conteúdo específico detectado!`);
        successCount++;
      } else if (result.status === 'GENERIC') {
        console.log(`   ❌ PROBLEMA: Ainda mostra conteúdo genérico`);
      } else {
        console.log(`   ⚠️ INCERTO: Não foi possível determinar o tipo`);
      }
      
      console.log(`   📋 Preview: ${result.contentPreview}...`);
    }

    console.log('\n🎯 RESUMO FINAL:');
    console.log(`✅ Livros com conteúdo real: ${successCount}/${totalCount}`);
    console.log(`📊 Taxa de sucesso: ${totalCount > 0 ? ((successCount / totalCount) * 100).toFixed(1) : 0}%`);

    if (successCount === totalCount && totalCount > 0) {
      console.log('\n🎉 PROBLEMA RESOLVIDO COMPLETAMENTE!');
      console.log('Todos os livros testados agora mostram conteúdo real e específico');
      console.log('em vez de templates genéricos baseados em categoria.');
    } else if (successCount > 0) {
      console.log('\n⚠️ PROBLEMA PARCIALMENTE RESOLVIDO');
      console.log('Alguns livros ainda mostram conteúdo genérico.');
    } else {
      console.log('\n❌ PROBLEMA PERSISTE');
      console.log('Os livros ainda mostram conteúdo genérico.');
    }

    // Testar também o getBookText
    console.log('\n📄 Testando conversão para texto de leitura...');
    
    const textTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        const bookText = await BookLoader.getBookText('7'); // Freud
        
        return {
          success: true,
          length: bookText.length,
          hasFreudContent: bookText.toLowerCase().includes('freud') || 
                          bookText.toLowerCase().includes('sonho') ||
                          bookText.toLowerCase().includes('inconsciente'),
          preview: bookText.substring(0, 300)
        };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    if (textTest.success) {
      console.log(`✅ Texto gerado: ${textTest.length} caracteres`);
      console.log(`🎯 Conteúdo específico do Freud: ${textTest.hasFreudContent ? 'SIM' : 'NÃO'}`);
      console.log(`📋 Preview: ${textTest.preview}...`);
    } else {
      console.log(`❌ Erro na conversão: ${textTest.error}`);
    }

    await page.screenshot({ 
      path: 'final-verification.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot salvo como final-verification.png');

    await new Promise(resolve => setTimeout(resolve, 5000));

  } catch (error) {
    console.error('💥 Erro durante verificação:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

finalContentVerification();
