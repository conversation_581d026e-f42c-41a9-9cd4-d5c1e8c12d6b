import puppeteer from 'puppeteer';

async function testErrorFix() {
  let browser;
  try {
    console.log('🔧 TESTANDO CORREÇÃO DO ERRO DE CARREGAMENTO\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Capturar logs do console para ver se o erro foi corrigido
    page.on('console', msg => {
      const text = msg.text();
      const type = msg.type();
      
      if (type === 'error') {
        console.log(`🔴 Console Error: ${text}`);
      } else if (text.includes('BookLoader') || text.includes('Loading') || text.includes('Converting')) {
        console.log(`🔵 Console: ${text}`);
      }
    });

    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Testar BookLoader diretamente no console
    console.log('🧪 Testando BookLoader no console do navegador...');
    
    const testResult = await page.evaluate(async () => {
      try {
        // Importar BookLoader
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        
        console.log('Testing BookLoader.getBookText for book ID 85...');
        
        // Testar especificamente o livro que estava dando erro
        const bookText = await BookLoader.getBookText('85');
        
        return {
          success: true,
          textLength: bookText.length,
          hasContent: bookText.length > 100,
          preview: bookText.substring(0, 300),
          hasError: bookText.includes('Erro') || bookText.includes('não encontrado')
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message,
          stack: error.stack
        };
      }
    });

    console.log('\n📊 RESULTADO DO TESTE:');
    
    if (testResult.success) {
      console.log('✅ BookLoader funcionou sem erros!');
      console.log(`📏 Tamanho do texto: ${testResult.textLength} caracteres`);
      console.log(`📚 Tem conteúdo: ${testResult.hasContent ? 'SIM' : 'NÃO'}`);
      console.log(`❌ Tem erro: ${testResult.hasError ? 'SIM' : 'NÃO'}`);
      console.log(`📋 Preview: ${testResult.preview}...`);
      
      if (testResult.hasContent && !testResult.hasError) {
        console.log('\n🎉 ERRO CORRIGIDO COM SUCESSO!');
        console.log('O BookLoader agora funciona corretamente no navegador.');
      } else {
        console.log('\n⚠️ Ainda há problemas no conteúdo.');
      }
    } else {
      console.log('❌ BookLoader ainda tem erros:');
      console.log(`   Erro: ${testResult.error}`);
      console.log(`   Stack: ${testResult.stack}`);
    }

    // Aguardar um pouco para análise manual
    console.log('\n⏳ Aguardando 10 segundos para teste manual...');
    console.log('Agora você pode testar manualmente:');
    console.log('1. Faça login na aplicação');
    console.log('2. Vá para a biblioteca');
    console.log('3. Clique em "Ler Agora" em qualquer livro');
    console.log('4. Verifique se o erro "Erro ao carregar" foi resolvido');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testErrorFix();
