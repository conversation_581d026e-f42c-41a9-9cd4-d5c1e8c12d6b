import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

// For now, let's create real content manually for the specific book mentioned
// "A Interpretação dos Sonhos" by <PERSON><PERSON><PERSON>

const FREUD_REAL_CONTENT = {
  title: 'A Interpretação dos Sonhos',
  author: 'Sig<PERSON>',
  category: 'Psicologia',
  description: 'Obra fundamental de Freud que estabelece as bases da psicanálise através da análise dos sonhos como via régia para o inconsciente.',
  duration: 45,
  difficulty: 'Avançado',
  content: {
    chapters: [
      {
        id: 'chapter1',
        title: 'O Método de Interpretação dos Sonhos',
        content: `Freud inicia sua obra revolucionária estabelecendo que os sonhos não são fenômenos aleatórios, mas sim manifestações significativas do inconsciente. Ele argumenta que "o sonho é a via régia para o inconsciente" e desenvolve um método sistemático para sua interpretação.

O método freudiano baseia-se na associação livre, onde o sonhador relata todas as ideias que lhe ocorrem em relação aos elementos do sonho, sem censura. Freud distingue entre o conteúdo manifesto (o que é lembrado do sonho) e o conteúdo latente (o significado inconsciente oculto).

A interpretação dos sonhos revela desejos reprimidos, conflitos não resolvidos e material psíquico que foi banido da consciência. Freud demonstra que os sonhos seguem uma lógica própria, diferente do pensamento consciente, operando através de mecanismos como condensação e deslocamento.

Este método representa uma ruptura com as interpretações tradicionais dos sonhos, estabelecendo uma abordagem científica e sistemática para compreender os processos mentais inconscientes.`
      },
      {
        id: 'chapter2',
        title: 'Os Mecanismos do Trabalho do Sonho',
        content: `Freud identifica quatro mecanismos fundamentais através dos quais o inconsciente transforma os pensamentos latentes em conteúdo manifesto do sonho: condensação, deslocamento, consideração pela representabilidade e elaboração secundária.

A condensação permite que múltiplas ideias, pessoas ou situações sejam representadas por um único elemento no sonho. Uma pessoa no sonho pode representar várias pessoas da vida real, ou uma situação pode condensar múltiplas experiências.

O deslocamento transfere a intensidade emocional de elementos importantes para elementos aparentemente insignificantes. Assim, aspectos centrais do conflito inconsciente podem aparecer de forma periférica no sonho manifesto.

A consideração pela representabilidade transforma pensamentos abstratos em imagens concretas, já que o sonho opera principalmente através de representações visuais. Conceitos complexos são traduzidos em símbolos e metáforas visuais.

A elaboração secundária organiza o material onírico em uma narrativa mais coerente, tentando dar sentido lógico ao conteúdo aparentemente caótico do sonho.`
      },
      {
        id: 'chapter3',
        title: 'O Simbolismo dos Sonhos e sua Interpretação',
        content: `Freud desenvolve uma teoria abrangente sobre o simbolismo onírico, identificando símbolos universais que aparecem consistentemente nos sonhos de diferentes pessoas e culturas. Estes símbolos frequentemente representam aspectos da sexualidade e das relações familiares primitivas.

Os símbolos fálicos incluem objetos alongados como bastões, árvores, guarda-chuvas, facas, armas de fogo, e atividades como subir escadas ou voar. Os símbolos femininos incluem recipientes, cavernas, caixas, quartos, e paisagens como vales e jardins.

Freud enfatiza que a interpretação simbólica deve sempre ser contextualizada com as associações pessoais do sonhador. Um símbolo pode ter significados diferentes para pessoas diferentes, dependendo de suas experiências e conflitos específicos.

A análise dos sonhos revela não apenas desejos sexuais reprimidos, mas também conflitos edípicos, ansiedades de castração, e outros complexos fundamentais da psique humana. Freud demonstra como os sonhos servem como uma forma de realização alucinatória de desejos que não podem ser satisfeitos na realidade.

Esta obra estabelece os fundamentos da técnica psicanalítica e oferece insights profundos sobre o funcionamento da mente inconsciente.`
      }
    ],
    key_points: [
      'O sonho é a via régia para o inconsciente e revela desejos reprimidos',
      'Distinção fundamental entre conteúdo manifesto e conteúdo latente dos sonhos',
      'Os mecanismos do trabalho do sonho: condensação, deslocamento, representabilidade e elaboração secundária',
      'O simbolismo onírico segue padrões universais, especialmente relacionados à sexualidade',
      'A associação livre é o método fundamental para interpretar os sonhos',
      'Os sonhos representam realizações alucinatórias de desejos inconscientes',
      'A interpretação dos sonhos estabelece as bases da técnica psicanalítica'
    ],
    practical_exercises: [
      'Mantenha um diário de sonhos, registrando todos os detalhes que conseguir lembrar',
      'Pratique a associação livre com elementos específicos de seus sonhos',
      'Identifique padrões recorrentes em seus sonhos e explore seus possíveis significados',
      'Analise como eventos do dia anterior (restos diurnos) aparecem transformados em seus sonhos',
      'Explore as emoções presentes nos sonhos e suas conexões com conflitos conscientes'
    ]
  }
};

// Real content for other key books
const REAL_BOOK_CONTENTS = {
  'a_interpretacao_dos_sonhos_sigmund_freud_3': FREUD_REAL_CONTENT,
  'a_sutil_arte_de_ligar_o_foda_se_mark_manson_3': {
    title: 'A Sutil Arte de Ligar o F*da-se',
    author: 'Mark Manson',
    category: 'Autoajuda',
    description: 'Uma abordagem contraintuitiva para viver uma vida boa, focando no que realmente importa.',
    duration: 25,
    difficulty: 'Fácil',
    content: {
      chapters: [
        {
          id: 'chapter1',
          title: 'Não Tente',
          content: `Mark Manson começa com uma premissa radical: pare de tentar ser positivo o tempo todo. A obsessão cultural com o pensamento positivo é, na verdade, uma forma de evitar os problemas reais da vida.

A verdade desconfortável é que a vida é inerentemente difícil e cheia de sofrimento. Tentar evitar isso através do pensamento positivo constante apenas cria mais ansiedade e frustração. Em vez disso, devemos aceitar que problemas são inevitáveis e focar em escolher quais problemas vale a pena ter.

O conceito de "não tentar" não significa ser preguiçoso ou apático. Significa parar de se esforçar desesperadamente para evitar desconforto e, em vez disso, aceitar que o desconforto é parte natural da vida. Quando paramos de lutar contra a realidade, podemos começar a trabalhar com ela de forma mais eficaz.

A felicidade não é algo que você alcança e mantém - é um subproduto de resolver problemas. Quanto mais tentamos ser felizes, mais infelizes nos tornamos. A chave é encontrar problemas que valham a pena resolver e que estejam alinhados com nossos valores mais profundos.`
        },
        {
          id: 'chapter2',
          title: 'A Felicidade é um Problema',
          content: `Manson argumenta que nossa busca constante pela felicidade é exatamente o que nos torna infelizes. A felicidade não é um estado permanente que podemos alcançar, mas sim uma emoção temporária que surge quando resolvemos problemas.

Todos nós temos problemas. A questão não é como evitá-los (isso é impossível), mas sim como escolher os melhores problemas para ter. Problemas que são significativos, que nos desafiam a crescer, que estão alinhados com nossos valores fundamentais.

A sociedade moderna nos vendeu a ideia de que merecemos ser felizes apenas por existir. Mas a verdadeira satisfação vem de enfrentar e superar desafios, não de evitá-los. O sofrimento é inevitável, mas podemos escolher pelo que vale a pena sofrer.

Quando aceitamos que o sofrimento é parte da condição humana, podemos parar de desperdiçar energia tentando evitá-lo e começar a canalizá-la para coisas que realmente importam. A vida melhora não quando nossos problemas desaparecem, mas quando encontramos problemas melhores para resolver.`
        },
        {
          id: 'chapter3',
          title: 'Você Não é Especial',
          content: `Uma das mensagens mais liberadoras do livro é que você não é especial - e isso é uma coisa boa. A cultura moderna nos ensina que todos somos únicos e especiais, mas isso cria expectativas irreais e narcisismo.

Aceitar que você é uma pessoa comum com problemas comuns é incrivelmente libertador. Isso significa que você não precisa ser extraordinário em tudo. Você pode escolher algumas áreas para se destacar e aceitar mediocridade em outras.

Esta aceitação da ordinariedade permite que você pare de desperdiçar energia tentando impressionar outros ou provar seu valor. Em vez disso, você pode focar no que realmente importa para você, independentemente do que outros pensam.

A verdadeira confiança não vem de acreditar que você é especial, mas de aceitar suas limitações e trabalhar dentro delas. Quando paramos de tentar ser especiais, podemos começar a ser autênticos. E a autenticidade é muito mais valiosa que a especialidade fabricada.`
        }
      ],
      key_points: [
        'Pare de tentar ser positivo o tempo todo - aceite que a vida é difícil',
        'A felicidade é um subproduto de resolver problemas, não um objetivo em si',
        'Escolha conscientemente quais problemas vale a pena ter',
        'Você não é especial - e isso é libertador',
        'Foque apenas no que realmente importa para você',
        'Autenticidade é mais valiosa que tentar impressionar outros',
        'O sofrimento é inevitável, mas você pode escolher pelo que sofrer'
      ],
      practical_exercises: [
        'Liste os problemas em sua vida e identifique quais vale a pena resolver',
        'Pratique dizer "não" para coisas que não se alinham com seus valores',
        'Identifique áreas onde você pode aceitar mediocridade',
        'Reflita sobre o que realmente importa para você, independente da opinião dos outros',
        'Pare de tentar impressionar outros e foque em ser autêntico'
      ]
    }
  },
  
  'o_poder_do_habito_charles_duhigg_3': {
    title: 'O Poder do Hábito',
    author: 'Charles Duhigg',
    category: 'Produtividade',
    description: 'Como os hábitos funcionam e como você pode transformá-los para melhorar sua vida pessoal e profissional.',
    duration: 30,
    difficulty: 'Intermediário',
    content: {
      chapters: [
        {
          id: 'chapter1',
          title: 'O Loop do Hábito',
          content: `Charles Duhigg revela como os hábitos funcionam através de um loop neurológico simples: deixa, rotina e recompensa. Compreender este ciclo é fundamental para transformar qualquer comportamento.

Todo hábito começa com uma deixa - um gatilho que informa ao cérebro para entrar no modo automático. Em seguida, há a rotina, que pode ser física, mental ou emocional. Finalmente, há uma recompensa, que ajuda o cérebro a descobrir se vale a pena lembrar deste loop particular para o futuro.

Quando um hábito emerge, o cérebro para de participar completamente na tomada de decisões. Ele para de trabalhar tão intensamente ou desvia o foco para outras tarefas. A menos que você deliberadamente lute contra um hábito - a menos que você encontre novas rotinas - o padrão se desenrolará automaticamente.

Este processo de três etapas - deixa, rotina, recompensa - é a base de todos os hábitos. Compreender este loop é o primeiro passo para controlar seus hábitos e criar mudanças duradouras em sua vida.`
        },
        {
          id: 'chapter2',
          title: 'O Cérebro Ansioso',
          content: `Os hábitos emergem porque o cérebro está constantemente procurando maneiras de economizar esforço. Deixado por conta própria, o cérebro tentará transformar quase qualquer rotina em um hábito, porque os hábitos permitem que nossas mentes descansem mais frequentemente.

Esta instintiva economia de esforço é uma grande vantagem. Um cérebro eficiente requer menos espaço, o que torna a cabeça menor, o que torna o parto mais fácil e, portanto, causa menos mortes infantis. Um cérebro eficiente também nos permite parar de pensar constantemente sobre comportamentos básicos, como caminhar e escolher o que comer.

Mas essa eficiência mental vem com um custo. Quando nossos hábitos emergem, nossos cérebros param de participar completamente nas decisões. O cérebro pode quase completamente desligar-se, e isso é uma vantagem real: um cérebro eficiente permite-nos fazer mais com menos esforço mental.

O problema surge quando hábitos ruins se formam. Uma vez estabelecidos, eles operam automaticamente, muitas vezes contra nossos melhores interesses. A chave é aprender a identificar e modificar estes loops automáticos.`
        },
        {
          id: 'chapter3',
          title: 'A Regra de Ouro da Mudança de Hábito',
          content: `Para mudar um hábito, você deve manter a antiga deixa e entregar a antiga recompensa, mas inserir uma nova rotina. Esta é a Regra de Ouro da mudança de hábito, e é a base de tudo o que sabemos sobre como os hábitos mudam.

Quase qualquer comportamento pode ser transformado se a deixa e a recompensa permanecerem as mesmas. A regra de ouro tem influenciado tratamentos para alcoolismo, obesidade, transtorno obsessivo-compulsivo e centenas de outros comportamentos destrutivos.

Para usar esta regra, você precisa primeiro identificar a rotina. Em seguida, você precisa experimentar com recompensas para descobrir quais desejos estão impulsionando seus hábitos. Depois, você precisa isolar a deixa. Uma vez que você descobriu o loop do hábito de um comportamento particular, você pode procurar maneiras de substituir velhas rotinas por outras mais saudáveis.

A mudança de hábito não é necessariamente fácil ou rápida. Não é sempre simples. Mas é possível. E agora entendemos como fazer isso funcionar.`
        }
      ],
      key_points: [
        'Todo hábito funciona através de um loop: deixa, rotina e recompensa',
        'O cérebro automatiza comportamentos para economizar energia mental',
        'Para mudar um hábito, mantenha a deixa e recompensa, mas mude a rotina',
        'Identificar gatilhos é fundamental para transformar comportamentos',
        'Pequenas mudanças podem gerar grandes transformações ao longo do tempo',
        'Hábitos-chave podem desencadear mudanças em cascata em outras áreas',
        'A mudança de hábito requer prática deliberada e paciência'
      ],
      practical_exercises: [
        'Identifique um hábito que você quer mudar e mapeie seu loop (deixa-rotina-recompensa)',
        'Experimente diferentes rotinas mantendo a mesma deixa e recompensa',
        'Mantenha um diário de hábitos por uma semana para identificar padrões',
        'Escolha um keystone habit (hábito-chave) para focar primeiro',
        'Crie um plano específico para substituir uma rotina prejudicial por uma benéfica'
      ]
    }
  }
};

async function insertRealContent() {
  try {
    console.log('🚀 Inserindo conteúdo real dos livros...\n');

    for (const [filename, bookData] of Object.entries(REAL_BOOK_CONTENTS)) {
      console.log(`📖 Processando: ${bookData.title}`);
      
      // Verificar se o livro já existe
      const { data: existingBook, error: searchError } = await supabase
        .from('books')
        .select('id')
        .eq('title', bookData.title)
        .single();

      let bookId;
      
      if (existingBook) {
        bookId = existingBook.id;
        console.log(`   ✅ Livro encontrado (ID: ${bookId})`);
        
        // Atualizar informações do livro
        await supabase
          .from('books')
          .update({
            author: bookData.author,
            category: bookData.category,
            description: bookData.description,
            duration: bookData.duration,
            difficulty: bookData.difficulty,
            is_featured: true,
            pdf_key: filename
          })
          .eq('id', bookId);
      } else {
        // Inserir novo livro
        const { data: newBook, error: insertError } = await supabase
          .from('books')
          .insert({
            title: bookData.title,
            author: bookData.author,
            category: bookData.category,
            description: bookData.description,
            duration: bookData.duration,
            difficulty: bookData.difficulty,
            is_featured: true,
            is_free: true,
            pdf_key: filename
          })
          .select('id')
          .single();

        if (insertError) {
          console.error(`   ❌ Erro ao inserir: ${insertError.message}`);
          continue;
        }
        
        bookId = newBook.id;
        console.log(`   ✅ Novo livro criado (ID: ${bookId})`);
      }

      // Atualizar conteúdo REAL (substituir o genérico)
      const { error: contentError } = await supabase
        .from('book_contents')
        .update({
          content: bookData.content
        })
        .eq('book_id', bookId);

      if (contentError) {
        console.error(`   ❌ Erro no conteúdo: ${contentError.message}`);
      } else {
        console.log(`   ✅ CONTEÚDO REAL inserido!`);
        console.log(`   📚 Capítulos: ${bookData.content.chapters.length}`);
        console.log(`   🔑 Pontos-chave: ${bookData.content.key_points.length}`);
        console.log(`   💡 Exercícios: ${bookData.content.practical_exercises.length}`);
      }
      
      console.log('');
    }

    console.log('🎉 CONTEÚDO REAL INSERIDO COM SUCESSO!');
    console.log('Agora os livros mostrarão conteúdo autêntico em vez de templates genéricos.');
    
  } catch (error) {
    console.error('💥 Erro:', error);
  }
}

insertRealContent();
