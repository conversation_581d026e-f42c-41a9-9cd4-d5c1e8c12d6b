import puppeteer from 'puppeteer';

async function testStandardization() {
  let browser;
  try {
    console.log('📐 TESTANDO PADRONIZAÇÃO DOS RESUMOS\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Testar formatação no console
    console.log('🧪 Testando formatação padronizada...');
    
    const testResult = await page.evaluate(async () => {
      try {
        // Importar BookLoader
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        
        // Texto de teste com diferentes elementos
        const testText = `# CAPÍTULO UM
        
Este é o primeiro parágrafo do capítulo. Deve ter recuo na primeira linha e estar justificado.

Este é o segundo parágrafo. Não deve ter espaço extra entre parágrafos.

## SEÇÃO IMPORTANTE

Primeiro parágrafo após seção não deve ter recuo.

Segundo parágrafo após seção deve ter recuo normal.

# CAPÍTULO DOIS

Novo capítulo com formatação correta.`;

        // Testar formatação
        const formattedText = BookLoader.formatTextForReader(testText, 'light');
        
        // Verificar elementos específicos
        const hasChapterTitle = formattedText.includes('chapter-title');
        const hasSectionTitle = formattedText.includes('section-title');
        const hasBodyText = formattedText.includes('body-text');
        const hasRobotoFont = formattedText.includes('Roboto');
        const hasCorrectSizes = formattedText.includes('1.375em') && formattedText.includes('1.25em');
        const hasJustification = formattedText.includes('text-align: justify');
        const hasIndentation = formattedText.includes('text-indent: 1cm');
        
        // Verificar limpeza de caracteres especiais
        const cleanText = 'Texto com #$%¨&* caracteres especiais';
        const cleanedResult = BookLoader.formatTextForReader(cleanText, 'light');
        const isClean = !cleanedResult.includes('#') && 
                       !cleanedResult.includes('$') && 
                       !cleanedResult.includes('%') && 
                       !cleanedResult.includes('¨') && 
                       !cleanedResult.includes('&') && 
                       !cleanedResult.includes('*');
        
        return {
          success: true,
          formattedLength: formattedText.length,
          hasChapterTitle,
          hasSectionTitle,
          hasBodyText,
          hasRobotoFont,
          hasCorrectSizes,
          hasJustification,
          hasIndentation,
          isClean,
          preview: formattedText.substring(0, 800),
          cleanPreview: cleanedResult
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message,
          stack: error.stack
        };
      }
    });

    console.log('\n📊 RESULTADOS DA PADRONIZAÇÃO:');
    
    if (testResult.success) {
      const checks = [
        { name: 'Títulos de Capítulo', value: testResult.hasChapterTitle, description: 'Formatação CAPÍTULO UM' },
        { name: 'Títulos de Seção', value: testResult.hasSectionTitle, description: 'Formatação de seções' },
        { name: 'Corpo do Texto', value: testResult.hasBodyText, description: 'Parágrafos formatados' },
        { name: 'Fonte Roboto', value: testResult.hasRobotoFont, description: 'Google Roboto aplicada' },
        { name: 'Tamanhos Corretos', value: testResult.hasCorrectSizes, description: '22pt títulos, 16pt corpo' },
        { name: 'Texto Justificado', value: testResult.hasJustification, description: 'Alinhamento justificado' },
        { name: 'Recuo Primeira Linha', value: testResult.hasIndentation, description: 'Recuo de 1cm' },
        { name: 'Caracteres Limpos', value: testResult.isClean, description: 'Sem #$%¨&*' }
      ];
      
      let passedChecks = 0;
      
      checks.forEach(check => {
        const status = check.value ? '✅' : '❌';
        console.log(`${status} ${check.name}: ${check.description}`);
        if (check.value) passedChecks++;
      });
      
      console.log(`\n📈 Taxa de sucesso: ${passedChecks}/${checks.length} (${((passedChecks/checks.length)*100).toFixed(1)}%)`);
      
      console.log('\n📋 Preview da formatação:');
      console.log(testResult.preview + '...');
      
      console.log('\n🧹 Preview da limpeza:');
      console.log(testResult.cleanPreview);
      
      if (passedChecks === checks.length) {
        console.log('\n🎉 PADRONIZAÇÃO PERFEITA!');
        console.log('✅ Todos os elementos estão formatados corretamente');
        console.log('✅ Seguindo especificações profissionais');
        console.log('✅ Roboto, tamanhos corretos, espaçamentos adequados');
        console.log('✅ Texto limpo sem caracteres especiais');
      } else if (passedChecks >= checks.length * 0.8) {
        console.log('\n✅ PADRONIZAÇÃO BOA!');
        console.log('A maioria dos elementos está correta.');
      } else {
        console.log('\n⚠️ PADRONIZAÇÃO PRECISA DE AJUSTES');
        console.log('Vários elementos não estão seguindo as especificações.');
      }
      
    } else {
      console.log('❌ Erro no teste de padronização:');
      console.log(`   ${testResult.error}`);
    }

    // Testar controles de zoom
    console.log('\n🔍 TESTANDO CONTROLES DE ZOOM...');
    
    const zoomTest = await page.evaluate(() => {
      // Verificar se há controles de zoom visíveis
      const zoomControls = document.querySelectorAll('[title*="zoom"], [title*="fonte"]');
      const minusButtons = document.querySelectorAll('[title*="Diminuir"], [title*="zoom out"]');
      const plusButtons = document.querySelectorAll('[title*="Aumentar"], [title*="zoom in"]');
      const fontSizeDisplays = document.querySelectorAll('span:contains("pt")');
      
      return {
        hasZoomControls: zoomControls.length > 0,
        hasMinusButton: minusButtons.length > 0,
        hasPlusButton: plusButtons.length > 0,
        hasFontDisplay: fontSizeDisplays.length > 0 || document.body.innerText.includes('pt')
      };
    });

    console.log(`✅ Controles de zoom: ${zoomTest.hasZoomControls ? 'PRESENTES' : 'AUSENTES'}`);
    console.log(`✅ Botão diminuir: ${zoomTest.hasMinusButton ? 'PRESENTE' : 'AUSENTE'}`);
    console.log(`✅ Botão aumentar: ${zoomTest.hasPlusButton ? 'PRESENTE' : 'AUSENTE'}`);
    console.log(`✅ Display de tamanho: ${zoomTest.hasFontDisplay ? 'PRESENTE' : 'AUSENTE'}`);

    console.log('\n🚀 TESTE MANUAL RECOMENDADO:');
    console.log('1. Faça login na aplicação');
    console.log('2. Vá para a biblioteca');
    console.log('3. Clique em "Ler Agora" em qualquer livro');
    console.log('4. Verifique a formatação:');
    console.log('   • Títulos centralizados em caixa alta');
    console.log('   • Texto justificado com recuo na primeira linha');
    console.log('   • Fonte Roboto em tamanhos corretos');
    console.log('   • Sem caracteres especiais #$%¨&*');
    console.log('5. Teste os controles de zoom (+/-) na barra superior');
    console.log('6. Verifique se o texto fica maior/menor');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testStandardization();
