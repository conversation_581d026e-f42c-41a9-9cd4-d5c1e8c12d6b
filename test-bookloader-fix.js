import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testBookLoaderFix() {
  try {
    console.log('🧪 TESTANDO CORREÇÃO DO BOOKLOADER\n');

    // Testar alguns livros específicos que sabemos que têm conteúdo
    const testBooks = [
      { id: 3, expectedTitle: 'Pai Rico, Pai Pobre' },
      { id: 4, expectedTitle: 'A Sutil Arte de Ligar o F*da-se' },
      { id: 7, expectedTitle: 'A Interpretação dos Sonhos' },
      { id: 85, expectedTitle: 'Um Apelo A Consciencia Os Melhores Discursos Martin Luther <PERSON> 3' }
    ];

    for (const testBook of testBooks) {
      console.log(`📖 Testando livro ID ${testBook.id}: ${testBook.expectedTitle}`);
      
      // Simular o que o BookLoader faz
      console.log('   1️⃣ Buscando dados básicos do livro...');
      const { data: bookData, error: bookError } = await supabase
        .from('books')
        .select('*')
        .eq('id', testBook.id)
        .single();

      if (bookError || !bookData) {
        console.log(`   ❌ Erro ao buscar livro: ${bookError?.message}`);
        continue;
      }

      console.log(`   ✅ Livro encontrado: ${bookData.title}`);

      console.log('   2️⃣ Buscando conteúdo do livro...');
      const { data: contentData, error: contentError } = await supabase
        .from('book_contents')
        .select('content')
        .eq('book_id', testBook.id)
        .single();

      if (contentError || !contentData) {
        console.log(`   ⚠️ Conteúdo não encontrado: ${contentError?.message}`);
        console.log(`   📝 BookLoader usará createContentFromBookData`);
      } else {
        console.log(`   ✅ Conteúdo encontrado no banco!`);
        const content = contentData.content;
        
        if (content.chapters && content.chapters.length > 0) {
          console.log(`   📚 Capítulos: ${content.chapters.length}`);
          console.log(`   📄 Primeiro capítulo: "${content.chapters[0].title}"`);
          
          const firstChapterContent = content.chapters[0].content;
          const isRealContent = firstChapterContent.length > 1000 && 
                               !firstChapterContent.includes('Este é um resumo profissional') &&
                               !firstChapterContent.includes('Nossa metodologia garante');
          
          console.log(`   🎯 Conteúdo é real (não genérico): ${isRealContent ? 'SIM' : 'NÃO'}`);
          console.log(`   📏 Tamanho do conteúdo: ${firstChapterContent.length} caracteres`);
          console.log(`   📋 Preview: ${firstChapterContent.substring(0, 150)}...`);
        }
        
        if (content.total_characters) {
          console.log(`   📊 Total de caracteres extraídos: ${content.total_characters.toLocaleString()}`);
        }
        
        if (content.source_file) {
          console.log(`   📁 Arquivo fonte: ${content.source_file}`);
        }
      }
      
      console.log('');
    }

    // Testar a função getBookText simulada
    console.log('🔍 TESTANDO CONVERSÃO PARA TEXTO DE LEITURA...\n');
    
    const testBookId = 3; // Pai Rico, Pai Pobre
    console.log(`📖 Testando conversão de texto para livro ID ${testBookId}`);
    
    const { data: bookData, error: bookError } = await supabase
      .from('books')
      .select('*')
      .eq('id', testBookId)
      .single();

    const { data: contentData, error: contentError } = await supabase
      .from('book_contents')
      .select('content')
      .eq('book_id', testBookId)
      .single();

    if (bookData && contentData) {
      // Simular a conversão para texto
      let text = `# ${bookData.title}\n\n**Por ${bookData.author}**\n\n`;
      text += `${bookData.description}\n\n`;
      
      if (contentData.content.chapters) {
        contentData.content.chapters.forEach(chapter => {
          text += `## ${chapter.title}\n\n`;
          text += `${chapter.content}\n\n`;
        });
      }
      
      if (contentData.content.key_points && contentData.content.key_points.length > 0) {
        text += `## Pontos-Chave\n\n`;
        contentData.content.key_points.forEach(point => {
          text += `- ${point}\n`;
        });
        text += '\n';
      }

      console.log(`✅ Texto gerado com ${text.length} caracteres`);
      console.log(`📋 Preview do texto final:`);
      console.log(text.substring(0, 500) + '...');
      
      const hasRealContent = text.length > 2000 && 
                            !text.includes('Este é um resumo profissional') &&
                            !text.includes('Nossa metodologia garante');
      
      console.log(`🎯 Texto contém conteúdo real: ${hasRealContent ? 'SIM' : 'NÃO'}`);
    }

    console.log('\n🎉 TESTE CONCLUÍDO!');
    console.log('Agora teste no navegador clicando em "Ler Agora" em qualquer livro.');
    
  } catch (error) {
    console.error('💥 Erro no teste:', error);
  }
}

testBookLoaderFix();
