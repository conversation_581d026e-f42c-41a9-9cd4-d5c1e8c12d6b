import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function searchOriginalPDFContent() {
  try {
    console.log('🔍 SEARCHING FOR ORIGINAL MLK PDF CONTENT\n');

    // The user mentioned the original content was "Conteúdo extraído do PDF (125.765 caracteres)"
    // Let's search more thoroughly for any content that might be the original

    console.log('1. Searching for books with large content that might contain original PDF...');
    
    // Get all books and check their content sizes
    const { data: allBooks, error: booksError } = await supabase
      .from('books')
      .select('id, title, author, description');

    if (booksError) {
      console.log('❌ Error fetching books:', booksError.message);
      return;
    }

    console.log(`📚 Checking ${allBooks.length} books for large content...`);

    // Check each book's content
    const booksWithContent = [];
    
    for (const book of allBooks) {
      const { data: content, error: contentError } = await supabase
        .from('book_contents')
        .select('content')
        .eq('book_id', book.id);

      if (contentError || !content || content.length === 0) {
        continue;
      }

      let totalSize = 0;
      let rawContent = '';
      
      content.forEach(entry => {
        if (typeof entry.content === 'string') {
          totalSize += entry.content.length;
          rawContent = entry.content;
        } else if (typeof entry.content === 'object') {
          const jsonStr = JSON.stringify(entry.content);
          totalSize += jsonStr.length;
          
          // Try to extract raw text from structured content
          if (entry.content.chapters) {
            rawContent = entry.content.chapters.map(ch => ch.content).join('\n\n');
          }
        }
      });

      if (totalSize > 50000) { // Only books with substantial content
        booksWithContent.push({
          ...book,
          totalSize,
          rawContent: rawContent.substring(0, 1000) // First 1000 chars for preview
        });
      }
    }

    // Sort by content size
    booksWithContent.sort((a, b) => b.totalSize - a.totalSize);

    console.log('\n📊 Books with substantial content (>50k characters):');
    booksWithContent.forEach(book => {
      console.log(`\n📖 ID: ${book.id} - "${book.title}"`);
      console.log(`   Author: ${book.author}`);
      console.log(`   Size: ${book.totalSize.toLocaleString()} characters`);
      console.log(`   Description: "${book.description}"`);
      console.log(`   Content preview: "${book.rawContent.substring(0, 200)}..."`);
      
      // Check if this might be MLK-related original content
      const isMLKRelated = 
        book.title.toLowerCase().includes('martin') ||
        book.title.toLowerCase().includes('king') ||
        book.title.toLowerCase().includes('apelo') ||
        book.rawContent.toLowerCase().includes('martin luther king') ||
        book.rawContent.toLowerCase().includes('direitos civis') ||
        book.rawContent.toLowerCase().includes('segregação');
      
      const hasLargeContent = book.totalSize > 100000;
      
      console.log(`   MLK-related: ${isMLKRelated ? 'YES' : 'NO'}`);
      console.log(`   Large content: ${hasLargeContent ? 'YES' : 'NO'}`);
      
      if (isMLKRelated && hasLargeContent) {
        console.log('   🎯 POTENTIAL ORIGINAL MLK PDF CONTENT!');
      }
    });

    // Look specifically for content that might be around 125,765 characters
    console.log('\n2. Looking for content around 125,765 characters...');
    
    const targetSize = 125765;
    const tolerance = 10000; // Allow 10k characters difference
    
    const nearTargetSize = booksWithContent.filter(book => 
      Math.abs(book.totalSize - targetSize) < tolerance
    );

    if (nearTargetSize.length > 0) {
      console.log(`\n🎯 Found ${nearTargetSize.length} books with content near target size:`);
      
      for (const book of nearTargetSize) {
        console.log(`\n📖 CANDIDATE: ID ${book.id} - "${book.title}"`);
        console.log(`   Size: ${book.totalSize.toLocaleString()} characters (target: ${targetSize.toLocaleString()})`);
        console.log(`   Difference: ${Math.abs(book.totalSize - targetSize).toLocaleString()} characters`);
        
        // Get the full content for analysis
        const { data: fullContent, error: fullError } = await supabase
          .from('book_contents')
          .select('content')
          .eq('book_id', book.id);

        if (fullError || !fullContent || fullContent.length === 0) {
          console.log('   ❌ Could not fetch full content');
          continue;
        }

        const content = fullContent[0].content;
        let fullText = '';
        
        if (typeof content === 'string') {
          fullText = content;
        } else if (typeof content === 'object' && content.chapters) {
          fullText = content.chapters.map(ch => ch.content).join('\n\n');
        }

        // Analyze content characteristics
        const hasMLKKeywords = 
          fullText.includes('Martin Luther King') ||
          fullText.includes('direitos civis') ||
          fullText.includes('segregação') ||
          fullText.includes('Montgomery') ||
          fullText.includes('Birmingham') ||
          fullText.includes('I Have a Dream');

        const hasPortugueseContent = 
          fullText.includes('Estados Unidos') ||
          fullText.includes('movimento') ||
          fullText.includes('liderança') ||
          fullText.includes('não-violenta');

        const looksLikePDFExtraction = 
          fullText.length > 100000 &&
          !fullText.includes('## ') && // Not structured markdown
          fullText.includes('.') && // Has sentences
          fullText.split('\n').length > 100; // Many paragraphs

        console.log(`   MLK Keywords: ${hasMLKKeywords ? 'YES' : 'NO'}`);
        console.log(`   Portuguese Content: ${hasPortugueseContent ? 'YES' : 'NO'}`);
        console.log(`   Looks like PDF: ${looksLikePDFExtraction ? 'YES' : 'NO'}`);
        
        if (hasMLKKeywords && hasPortugueseContent && looksLikePDFExtraction) {
          console.log('   🎉 THIS IS LIKELY THE ORIGINAL MLK PDF CONTENT!');
          console.log(`   Preview: "${fullText.substring(0, 500)}..."`);
          
          // This is probably our original content
          return {
            bookId: book.id,
            content: fullText,
            size: fullText.length
          };
        }
      }
    } else {
      console.log('❌ No books found with content near target size');
    }

    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. If original PDF content was found above, use it to restore the MLK book');
    console.log('2. If not found, the original content may have been permanently lost');
    console.log('3. In that case, we should create high-quality content that represents');
    console.log('   what users would expect from a Martin Luther King Jr. PDF summary');

    return null;

  } catch (error) {
    console.error('💥 Error during search:', error);
    return null;
  }
}

// Run the search
searchOriginalPDFContent().then(result => {
  if (result) {
    console.log('\n✅ ORIGINAL CONTENT FOUND!');
    console.log(`Book ID: ${result.bookId}`);
    console.log(`Content Size: ${result.size.toLocaleString()} characters`);
  } else {
    console.log('\n❌ Original content not found in database');
  }
});
