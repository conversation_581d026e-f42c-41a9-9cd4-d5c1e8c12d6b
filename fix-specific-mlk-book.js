import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixSpecificMLKBook() {
  try {
    console.log('🔧 CORRIGINDO LIVRO ESPECÍFICO DO MLK (ID 85)\n');

    const bookId = 85; // ID específico do livro problemático
    
    // Verificar o livro atual
    console.log('1. Verificando livro atual...');
    
    const { data: currentBook, error: fetchError } = await supabase
      .from('books')
      .select('*')
      .eq('id', bookId)
      .single();

    if (fetchError) {
      console.log('❌ Erro ao buscar livro:', fetchError.message);
      return;
    }

    console.log('📚 Livro atual:');
    console.log(`   ID: ${currentBook.id}`);
    console.log(`   Título: "${currentBook.title}"`);
    console.log(`   Autor: "${currentBook.author}"`);
    console.log(`   Descrição: "${currentBook.description}"`);

    // Definir informações corretas
    const correctTitle = "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King";
    const correctAuthor = "Martin Luther King Jr.";
    const correctDescription = "Os melhores discursos sobre direitos civis e justiça social de Martin Luther King Jr., incluindo 'I Have a Dream' e outros discursos históricos que mudaram o mundo.";

    // Atualizar título
    console.log('\n2. Atualizando título...');
    const { error: titleError } = await supabase
      .from('books')
      .update({ title: correctTitle })
      .eq('id', bookId);

    if (titleError) {
      console.log('❌ Erro ao atualizar título:', titleError.message);
    } else {
      console.log('✅ Título atualizado com sucesso');
    }

    // Atualizar autor
    console.log('\n3. Atualizando autor...');
    const { error: authorError } = await supabase
      .from('books')
      .update({ author: correctAuthor })
      .eq('id', bookId);

    if (authorError) {
      console.log('❌ Erro ao atualizar autor:', authorError.message);
    } else {
      console.log('✅ Autor atualizado com sucesso');
    }

    // Atualizar descrição
    console.log('\n4. Atualizando descrição...');
    const { error: descError } = await supabase
      .from('books')
      .update({ description: correctDescription })
      .eq('id', bookId);

    if (descError) {
      console.log('❌ Erro ao atualizar descrição:', descError.message);
    } else {
      console.log('✅ Descrição atualizada com sucesso');
    }

    // Verificar resultado final
    console.log('\n5. Verificando resultado final...');
    
    const { data: updatedBook, error: verifyError } = await supabase
      .from('books')
      .select('*')
      .eq('id', bookId)
      .single();

    if (verifyError) {
      console.log('❌ Erro ao verificar resultado:', verifyError.message);
    } else {
      console.log('✅ Livro após atualizações:');
      console.log(`   ID: ${updatedBook.id}`);
      console.log(`   Título: "${updatedBook.title}"`);
      console.log(`   Autor: "${updatedBook.author}"`);
      console.log(`   Descrição: "${updatedBook.description}"`);

      // Verificar se as correções foram aplicadas
      const titleCorrect = updatedBook.title === correctTitle;
      const authorCorrect = updatedBook.author === correctAuthor;
      const descCorrect = updatedBook.description === correctDescription;

      console.log('\n📊 Status das correções:');
      console.log(`   ${titleCorrect ? '✅' : '❌'} Título: ${titleCorrect ? 'CORRETO' : 'AINDA INCORRETO'}`);
      console.log(`   ${authorCorrect ? '✅' : '❌'} Autor: ${authorCorrect ? 'CORRETO' : 'AINDA INCORRETO'}`);
      console.log(`   ${descCorrect ? '✅' : '❌'} Descrição: ${descCorrect ? 'CORRETA' : 'AINDA INCORRETA'}`);

      if (titleCorrect && authorCorrect && descCorrect) {
        console.log('\n🎉 TODAS AS CORREÇÕES APLICADAS COM SUCESSO!');
        console.log('✅ O livro agora aparecerá corretamente na biblioteca');
      } else {
        console.log('\n⚠️ Algumas correções não foram aplicadas');
        console.log('   Pode haver restrições no banco de dados');
      }
    }

    // Verificar conteúdo
    console.log('\n6. Verificando conteúdo...');
    
    const { data: content, error: contentError } = await supabase
      .from('book_contents')
      .select('*')
      .eq('book_id', bookId);

    if (contentError) {
      console.log('❌ Erro ao verificar conteúdo:', contentError.message);
    } else {
      console.log(`📖 Conteúdo: ${content.length} entrada(s) encontrada(s)`);
      
      if (content.length === 0) {
        console.log('⚠️ Este livro não tem conteúdo estruturado');
        console.log('   Recomenda-se adicionar conteúdo formatado dos discursos do MLK');
      } else {
        content.forEach((entry, index) => {
          const contentType = typeof entry.content;
          const contentSize = contentType === 'object' ? 
            JSON.stringify(entry.content).length : 
            entry.content?.length || 0;
          console.log(`   Entrada ${index + 1}: ${contentType} com ${contentSize} caracteres`);
        });
      }
    }

    console.log('\n🎯 RESUMO FINAL:');
    console.log('📚 Livro do Martin Luther King Jr. corrigido:');
    console.log(`   • Título: "${correctTitle}"`);
    console.log(`   • Autor: "${correctAuthor}"`);
    console.log(`   • Descrição melhorada com informações históricas`);
    console.log('   • Agora aparecerá corretamente na biblioteca');

  } catch (error) {
    console.error('💥 Erro durante correção:', error);
  }
}

fixSpecificMLKBook();
