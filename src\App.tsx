import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BookOpen, LogOut, User, Sparkles } from 'lucide-react';
import HeroSection from './components/landing/HeroSection';
import { AuthForm } from './components/auth/AuthForm';
import Dashboard from './components/dashboard/Dashboard';
import { Button } from './components/ui/button';
import { auth } from './lib/supabase';

function App() {
  const [user, setUser] = useState(null);
  const [showAuth, setShowAuth] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    auth.getUser().then(({ data: { user } }) => {
      setUser(user);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = auth.onAuthStateChange((event, session) => {
      setUser(session?.user || null);
      if (session?.user) {
        setShowAuth(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const handleSignOut = async () => {
    await auth.signOut();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="w-12 h-12 border-4 border-gray-200 rounded-full"></div>
            <div className="w-12 h-12 border-4 border-gray-900 border-t-transparent rounded-full animate-spin absolute top-0 left-0"></div>
          </div>
          <span className="text-gray-900 text-lg font-medium">Carregando...</span>
        </div>
      </div>
    );
  }

  if (user) {
    return <Dashboard user={user} onSignOut={handleSignOut} />;
  }

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Navigation */}
      <nav className="relative z-20 bg-white/90 backdrop-blur-xl border-b border-gray-100">
        <div className="flex items-center justify-between max-w-7xl mx-auto px-6 py-4">
          <motion.div 
            className="flex items-center space-x-3"
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-gray-900 to-gray-700 flex items-center justify-center shadow-elegant">
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900 text-elegant">Paretto Estudos</span>
          </motion.div>
          <Button
            onClick={() => setShowAuth(true)}
            className="interactive-button bg-gray-900 text-white hover:bg-gray-800 px-6 py-2 shadow-elegant"
          >
            Entrar
          </Button>
        </div>
      </nav>

      {/* Hero Section */}
      <AnimatePresence>
        {!showAuth && <HeroSection />}
      </AnimatePresence>

      {/* Auth Modal */}
      <AnimatePresence>
        {showAuth && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-6"
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowAuth(false);
              }
            }}
          >
            <div className="absolute inset-0 bg-black/30 backdrop-blur-md" />
            <div className="relative z-10">
              <AuthForm onSuccess={() => setShowAuth(false)} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default App;