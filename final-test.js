import puppeteer from 'puppeteer';

async function finalTest() {
  let browser;
  try {
    console.log('🎯 TESTE FINAL: Verificando funcionalidade completa de leitura de livros\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Capturar todos os logs do console
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (type === 'error') {
        console.log(`🔴 Console Error: ${text}`);
      } else if (type === 'warn') {
        console.log(`🟡 Console Warning: ${text}`);
      } else if (text.includes('book') || text.includes('loading') || text.includes('error')) {
        console.log(`🔵 Console Info: ${text}`);
      }
    });

    page.on('pageerror', error => {
      console.log(`💥 Page Error: ${error.message}`);
    });

    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Passo 1: Fazer login
    console.log('\n🔐 PASSO 1: Fazendo login...');
    
    const loginClicked = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button, a'));
      const loginButton = buttons.find(btn => {
        const text = btn.textContent?.toLowerCase() || '';
        return text.includes('entrar') || text.includes('login');
      });

      if (loginButton) {
        loginButton.click();
        return true;
      }
      return false;
    });

    if (loginClicked) {
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Preencher formulário de login
      try {
        await page.type('input[type="email"]', '<EMAIL>', { delay: 50 });
        await page.type('input[type="password"]', 'password123', { delay: 50 });

        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
          await submitButton.click();
          await new Promise(resolve => setTimeout(resolve, 5000));
          console.log('✅ Login realizado');
        }
      } catch (error) {
        console.log('⚠️ Erro no login, continuando...');
      }
    }

    // Passo 2: Navegar para biblioteca
    console.log('\n📚 PASSO 2: Navegando para biblioteca...');
    
    try {
      // Tentar clicar no link da biblioteca na sidebar
      const libraryClicked = await page.evaluate(() => {
        const links = Array.from(document.querySelectorAll('a, button'));
        const libraryLink = links.find(link => {
          const text = link.textContent?.toLowerCase() || '';
          const href = link.href || '';
          return text.includes('biblioteca') || href.includes('library');
        });

        if (libraryLink) {
          libraryLink.click();
          return true;
        }
        return false;
      });

      if (libraryClicked) {
        await new Promise(resolve => setTimeout(resolve, 3000));
        console.log('✅ Navegou para biblioteca');
      } else {
        console.log('⚠️ Link da biblioteca não encontrado, verificando se já estamos na biblioteca...');
      }
    } catch (error) {
      console.log('⚠️ Erro ao navegar para biblioteca:', error.message);
    }

    // Passo 3: Verificar se há livros na página
    console.log('\n📖 PASSO 3: Verificando livros disponíveis...');
    
    const booksInfo = await page.evaluate(() => {
      // Procurar por cards de livros
      const bookCards = Array.from(document.querySelectorAll('[class*="card"], [class*="book"], .grid > div, [class*="Book"]'));
      const validBooks = bookCards.filter(card => {
        const text = card.textContent || '';
        return text.length > 50 && (
          text.includes('Ler') || 
          text.includes('autor') || 
          text.includes('min') ||
          card.querySelector('button, a')
        );
      });

      return {
        totalCards: bookCards.length,
        validBooks: validBooks.length,
        books: validBooks.slice(0, 5).map(card => ({
          text: (card.textContent || '').substring(0, 150) + '...',
          hasButton: !!card.querySelector('button, a'),
          buttonText: card.querySelector('button, a')?.textContent?.trim() || 'N/A'
        }))
      };
    });

    console.log(`📊 Total de cards encontrados: ${booksInfo.totalCards}`);
    console.log(`📚 Livros válidos: ${booksInfo.validBooks}`);
    
    if (booksInfo.books.length > 0) {
      console.log('📋 Livros encontrados:');
      booksInfo.books.forEach((book, index) => {
        console.log(`   ${index + 1}. ${book.text}`);
        console.log(`      Botão: ${book.hasButton ? book.buttonText : 'Não encontrado'}`);
      });
    }

    // Passo 4: Tentar clicar em um livro
    if (booksInfo.validBooks > 0) {
      console.log('\n🖱️ PASSO 4: Tentando clicar em um livro...');
      
      const clickResult = await page.evaluate(() => {
        const bookCards = Array.from(document.querySelectorAll('[class*="card"], [class*="book"], .grid > div, [class*="Book"]'));
        const validBooks = bookCards.filter(card => {
          const text = card.textContent || '';
          return text.length > 50 && card.querySelector('button, a');
        });

        if (validBooks.length > 0) {
          const firstBook = validBooks[0];
          const button = firstBook.querySelector('button, a');
          const bookTitle = firstBook.textContent?.match(/([A-Z][^.]*)/)?.[0] || 'Livro desconhecido';
          
          if (button) {
            button.click();
            return { 
              success: true, 
              bookTitle: bookTitle.substring(0, 50),
              buttonText: button.textContent?.trim() 
            };
          }
        }
        return { success: false };
      });

      if (clickResult.success) {
        console.log(`✅ Clicou no livro: "${clickResult.bookTitle}"`);
        console.log(`   Botão clicado: "${clickResult.buttonText}"`);
        
        // Aguardar carregamento da página de leitura
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        // Passo 5: Verificar página de leitura
        console.log('\n📄 PASSO 5: Analisando página de leitura...');
        
        const readingPageInfo = await page.evaluate(() => {
          const text = document.body.innerText;
          const hasError = text.toLowerCase().includes('não encontrado') || 
                          text.toLowerCase().includes('erro') ||
                          text.toLowerCase().includes('placeholder') ||
                          text.toLowerCase().includes('conteúdo não disponível');
          
          const hasRealContent = text.length > 1000 && (
            text.includes('capítulo') ||
            text.includes('Capítulo') ||
            text.includes('resumo') ||
            text.includes('conceito') ||
            text.includes('estratégia') ||
            text.includes('aplicação')
          );

          const hasBookStructure = !!(
            document.querySelector('h1, h2, h3') &&
            (text.includes('Pontos-chave') || 
             text.includes('Exercícios') ||
             text.includes('Principais') ||
             text.includes('Fundamentos'))
          );

          return {
            url: window.location.href,
            title: document.title,
            textLength: text.length,
            hasError,
            hasRealContent,
            hasBookStructure,
            contentPreview: text.substring(0, 400) + '...',
            headings: Array.from(document.querySelectorAll('h1, h2, h3')).slice(0, 5).map(h => h.textContent?.trim())
          };
        });

        console.log(`🌐 URL atual: ${readingPageInfo.url}`);
        console.log(`📝 Título: ${readingPageInfo.title}`);
        console.log(`📏 Tamanho do texto: ${readingPageInfo.textLength} caracteres`);
        console.log(`❌ Tem erro: ${readingPageInfo.hasError}`);
        console.log(`✅ Tem conteúdo real: ${readingPageInfo.hasRealContent}`);
        console.log(`📚 Tem estrutura de livro: ${readingPageInfo.hasBookStructure}`);
        
        if (readingPageInfo.headings.length > 0) {
          console.log('📋 Títulos encontrados:');
          readingPageInfo.headings.forEach((heading, index) => {
            console.log(`   ${index + 1}. ${heading}`);
          });
        }

        console.log(`📄 Preview do conteúdo:\n${readingPageInfo.contentPreview}`);

        // Resultado final
        console.log('\n🎯 RESULTADO FINAL:');
        if (readingPageInfo.hasError) {
          console.log('❌ BUG CONFIRMADO: A página mostra conteúdo de erro ou placeholder!');
          console.log('   O sistema não está carregando o conteúdo real dos livros.');
        } else if (readingPageInfo.hasRealContent && readingPageInfo.hasBookStructure) {
          console.log('✅ SUCESSO TOTAL: A funcionalidade está funcionando perfeitamente!');
          console.log('   O sistema está carregando e exibindo conteúdo real dos livros.');
        } else if (readingPageInfo.hasRealContent) {
          console.log('⚠️ SUCESSO PARCIAL: Há conteúdo, mas pode não estar bem estruturado.');
        } else {
          console.log('❓ RESULTADO INCERTO: Não foi possível determinar se o conteúdo é real.');
        }

      } else {
        console.log('❌ Não foi possível clicar em nenhum livro');
      }
    } else {
      console.log('❌ Nenhum livro encontrado na interface');
    }

    // Screenshot final
    await page.screenshot({ 
      path: 'final-test-result.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot final salvo como final-test-result.png');

    await new Promise(resolve => setTimeout(resolve, 5000));

  } catch (error) {
    console.error('💥 Erro durante o teste final:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

finalTest();
