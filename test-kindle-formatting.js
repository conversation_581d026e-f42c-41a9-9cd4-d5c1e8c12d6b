import puppeteer from 'puppeteer';

async function testKindleFormatting() {
  let browser;
  try {
    console.log('📚 TESTING KINDLE-STYLE TEXT FORMATTING WITH ROBOTO FONT\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test BookLoader formatting with comprehensive content
    console.log('🧪 Testing BookLoader with Kindle-style formatting...');
    
    const formattingTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${Date.now()}`);
        
        // Comprehensive test text with all elements
        const testText = `# Capítulo Um

Este é o primeiro parágrafo após o título do capítulo. Não deve ter recuo na primeira linha.

Este é o segundo parágrafo. Deve ter recuo de 1cm na primeira linha e estar justificado.

## Seção Importante

Primeiro parágrafo após seção não deve ter recuo.

Segundo parágrafo deve ter recuo normal.

**Texto enfatizado em itálico**

1. Primeiro item da lista numerada
2. Segundo item da lista numerada
3. Terceiro item da lista numerada

- Item com marcador
- Outro item com marcador

Este é um parágrafo normal após a lista.`;

        const formatted = BookLoader.formatTextForReader(testText, 'light');
        
        // Check for proper HTML structure
        const hasChapterTitle = formatted.includes('class="chapter-title"');
        const hasSectionTitle = formatted.includes('class="section-title"');
        const hasBodyText = formatted.includes('class="body-text"');
        const hasNumberedList = formatted.includes('class="numbered-list"');
        const hasEmphasizedText = formatted.includes('class="emphasized-text"');
        
        // Check for proper content
        const hasUppercaseChapter = formatted.includes('CAPÍTULO UM');
        const hasUppercaseSection = formatted.includes('SEÇÃO IMPORTANTE');
        
        return {
          success: true,
          formatted,
          hasChapterTitle,
          hasSectionTitle,
          hasBodyText,
          hasNumberedList,
          hasEmphasizedText,
          hasUppercaseChapter,
          hasUppercaseSection,
          formattedLength: formatted.length
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (formattingTest.success) {
      console.log('✅ BookLoader formatting test passed');
      console.log(`📏 Formatted content length: ${formattingTest.formattedLength} characters`);
      
      const checks = [
        { name: 'Chapter titles', value: formattingTest.hasChapterTitle, description: 'class="chapter-title" present' },
        { name: 'Section titles', value: formattingTest.hasSectionTitle, description: 'class="section-title" present' },
        { name: 'Body text', value: formattingTest.hasBodyText, description: 'class="body-text" present' },
        { name: 'Numbered lists', value: formattingTest.hasNumberedList, description: 'class="numbered-list" present' },
        { name: 'Emphasized text', value: formattingTest.hasEmphasizedText, description: 'class="emphasized-text" present' },
        { name: 'Uppercase chapters', value: formattingTest.hasUppercaseChapter, description: 'Chapter titles in uppercase' },
        { name: 'Uppercase sections', value: formattingTest.hasUppercaseSection, description: 'Section titles in uppercase' }
      ];
      
      checks.forEach(check => {
        console.log(`${check.value ? '✅' : '❌'} ${check.name}: ${check.description}`);
      });
      
      console.log('\n📋 Sample formatted output:');
      console.log(formattingTest.formatted.substring(0, 400) + '...');
      
    } else {
      console.log('❌ BookLoader formatting test failed:');
      console.log(formattingTest.error);
    }

    // Test CSS specifications
    console.log('\n🎨 Testing CSS Kindle-style specifications...');
    
    const cssTest = await page.evaluate(() => {
      // Create test container with all elements
      const testContainer = document.createElement('div');
      testContainer.className = 'formatted-content light';
      testContainer.style.fontSize = '16pt'; // Base font size
      testContainer.innerHTML = `
        <div class="chapter-title">CAPÍTULO UM</div>
        <p class="body-text">Este é um parágrafo de teste.</p>
        <div class="section-title">SEÇÃO TESTE</div>
        <p class="body-text">Outro parágrafo.</p>
        <div class="numbered-list">1. Item de lista</div>
        <div class="emphasized-text">Texto enfatizado</div>
      `;
      
      document.body.appendChild(testContainer);
      
      // Get computed styles
      const containerStyle = window.getComputedStyle(testContainer);
      const chapterStyle = window.getComputedStyle(testContainer.querySelector('.chapter-title'));
      const bodyStyle = window.getComputedStyle(testContainer.querySelector('.body-text'));
      const sectionStyle = window.getComputedStyle(testContainer.querySelector('.section-title'));
      const listStyle = window.getComputedStyle(testContainer.querySelector('.numbered-list'));
      
      const results = {
        // Container specifications
        containerFontFamily: containerStyle.fontFamily,
        containerFontSize: containerStyle.fontSize,
        containerTextAlign: containerStyle.textAlign,
        containerColor: containerStyle.color,
        containerPadding: containerStyle.padding,
        
        // Chapter title specifications (should be 22pt = 1.375em * 16pt)
        chapterFontSize: chapterStyle.fontSize,
        chapterFontWeight: chapterStyle.fontWeight,
        chapterTextAlign: chapterStyle.textAlign,
        chapterTextTransform: chapterStyle.textTransform,
        chapterMarginTop: chapterStyle.marginTop,
        chapterMarginBottom: chapterStyle.marginBottom,
        
        // Body text specifications
        bodyFontSize: bodyStyle.fontSize,
        bodyFontWeight: bodyStyle.fontWeight,
        bodyTextAlign: bodyStyle.textAlign,
        bodyTextIndent: bodyStyle.textIndent,
        bodyLineHeight: bodyStyle.lineHeight,
        bodyMargin: bodyStyle.margin,
        
        // Section title specifications
        sectionFontSize: sectionStyle.fontSize,
        sectionTextAlign: sectionStyle.textAlign,
        sectionTextTransform: sectionStyle.textTransform,
        
        // List specifications
        listTextIndent: listStyle.textIndent,
        listPaddingLeft: listStyle.paddingLeft
      };
      
      // Test theme changes
      testContainer.className = 'formatted-content dark';
      const darkChapterStyle = window.getComputedStyle(testContainer.querySelector('.chapter-title'));
      results.darkChapterColor = darkChapterStyle.color;
      
      testContainer.className = 'formatted-content sepia';
      const sepiaChapterStyle = window.getComputedStyle(testContainer.querySelector('.chapter-title'));
      results.sepiaChapterColor = sepiaChapterStyle.color;
      
      // Clean up
      document.body.removeChild(testContainer);
      
      return results;
    });

    console.log('📊 CSS SPECIFICATIONS VERIFICATION:');
    console.log('\n🔤 FONT SPECIFICATIONS:');
    console.log(`   Font Family: ${cssTest.containerFontFamily}`);
    console.log(`   Base Font Size: ${cssTest.containerFontSize} (should be 16pt)`);
    console.log(`   Chapter Font Size: ${cssTest.chapterFontSize} (should be ~22pt)`);
    console.log(`   Body Font Size: ${cssTest.bodyFontSize} (should be 16pt)`);
    
    console.log('\n📐 LAYOUT SPECIFICATIONS:');
    console.log(`   Container Text Align: ${cssTest.containerTextAlign} (should be justify)`);
    console.log(`   Chapter Text Align: ${cssTest.chapterTextAlign} (should be center)`);
    console.log(`   Body Text Indent: ${cssTest.bodyTextIndent} (should be 1cm)`);
    console.log(`   Body Line Height: ${cssTest.bodyLineHeight} (should be 1.5)`);
    console.log(`   Container Padding: ${cssTest.containerPadding} (should include margins)`);
    
    console.log('\n🎨 TYPOGRAPHY SPECIFICATIONS:');
    console.log(`   Chapter Font Weight: ${cssTest.chapterFontWeight} (should be 700/bold)`);
    console.log(`   Chapter Text Transform: ${cssTest.chapterTextTransform} (should be uppercase)`);
    console.log(`   Body Font Weight: ${cssTest.bodyFontWeight} (should be 400/normal)`);
    
    console.log('\n🌈 THEME COLOR VERIFICATION:');
    console.log(`   Light Theme: ${cssTest.containerColor} (should be black)`);
    console.log(`   Dark Theme: ${cssTest.darkChapterColor} (should be light)`);
    console.log(`   Sepia Theme: ${cssTest.sepiaChapterColor} (should be brown)`);

    // Analyze compliance with specifications
    const specifications = [
      { name: 'Roboto Font', check: cssTest.containerFontFamily.includes('Roboto'), expected: 'Roboto font family' },
      { name: 'Justified Text', check: cssTest.containerTextAlign === 'justify', expected: 'justify alignment' },
      { name: 'Centered Chapters', check: cssTest.chapterTextAlign === 'center', expected: 'center alignment' },
      { name: 'Uppercase Chapters', check: cssTest.chapterTextTransform === 'uppercase', expected: 'uppercase transform' },
      { name: 'Bold Chapters', check: cssTest.chapterFontWeight === '700' || cssTest.chapterFontWeight === 'bold', expected: 'bold weight' },
      { name: 'Body Indent', check: cssTest.bodyTextIndent.includes('cm'), expected: '1cm text indent' },
      { name: 'Line Height 1.5', check: cssTest.bodyLineHeight === '1.5', expected: '1.5 line height' },
      { name: 'Container Margins', check: cssTest.containerPadding !== '0px', expected: 'proper margins/padding' }
    ];

    console.log('\n🎯 SPECIFICATION COMPLIANCE:');
    let passedSpecs = 0;
    specifications.forEach(spec => {
      const status = spec.check ? '✅' : '❌';
      console.log(`${status} ${spec.name}: ${spec.expected}`);
      if (spec.check) passedSpecs++;
    });

    const complianceRate = (passedSpecs / specifications.length) * 100;
    console.log(`\n📈 Overall Compliance: ${passedSpecs}/${specifications.length} (${complianceRate.toFixed(1)}%)`);

    if (complianceRate === 100) {
      console.log('\n🎉 PERFECT KINDLE-STYLE FORMATTING ACHIEVED!');
      console.log('✅ All specifications met exactly as requested');
    } else if (complianceRate >= 80) {
      console.log('\n✅ EXCELLENT KINDLE-STYLE FORMATTING!');
      console.log('Most specifications are correctly implemented');
    } else {
      console.log('\n⚠️ FORMATTING NEEDS IMPROVEMENT');
      console.log('Several specifications are not met');
    }

    console.log('\n🚀 MANUAL TESTING INSTRUCTIONS:');
    console.log('1. Open a book in the PDF reader');
    console.log('2. Verify visual elements:');
    console.log('   • Chapter titles: Centered, bold, uppercase, proper spacing');
    console.log('   • Body text: Justified, 1cm first-line indent, no extra spacing');
    console.log('   • Lists: Visually distinct from main text');
    console.log('   • Font: Roboto throughout');
    console.log('3. Test font size controls (+/-) - should scale properly');
    console.log('4. Test themes - colors should be appropriate for each theme');
    console.log('5. Check margins - should look like Kindle/professional layout');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Error during test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testKindleFormatting();
