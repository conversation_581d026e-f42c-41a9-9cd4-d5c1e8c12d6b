import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function searchAllBooksForOriginal() {
  try {
    console.log('🔍 SEARCHING ALL BOOKS FOR ORIGINAL PDF CONTENT\n');

    // Search for books that might contain the original PDF content
    // Look for descriptions mentioning PDF extraction or large character counts
    
    console.log('1. Searching for books with PDF-related descriptions...');
    
    const { data: pdfBooks, error: pdfError } = await supabase
      .from('books')
      .select('*')
      .or('description.ilike.%pdf%,description.ilike.%extraído%,description.ilike.%caracteres%');

    if (pdfError) {
      console.log('❌ Error searching PDF books:', pdfError.message);
    } else {
      console.log(`📚 Found ${pdfBooks.length} books with PDF-related descriptions:`);
      pdfBooks.forEach(book => {
        console.log(`   ID: ${book.id} - "${book.title}" by ${book.author}`);
        console.log(`       Description: "${book.description}"`);
      });
    }

    // Search for books with large content (>100k characters)
    console.log('\n2. Searching for books with large content...');
    
    const { data: allBooks, error: allError } = await supabase
      .from('books')
      .select('id, title, author, description');

    if (allError) {
      console.log('❌ Error fetching all books:', allError.message);
      return;
    }

    console.log(`📖 Checking content size for ${allBooks.length} books...`);
    
    const largeBooksPromises = allBooks.map(async (book) => {
      const { data: content, error: contentError } = await supabase
        .from('book_contents')
        .select('content')
        .eq('book_id', book.id);

      if (contentError || !content || content.length === 0) {
        return null;
      }

      let totalSize = 0;
      let contentPreview = '';
      
      content.forEach(entry => {
        if (typeof entry.content === 'string') {
          totalSize += entry.content.length;
          if (!contentPreview) contentPreview = entry.content.substring(0, 200);
        } else if (typeof entry.content === 'object') {
          const jsonSize = JSON.stringify(entry.content).length;
          totalSize += jsonSize;
          
          if (entry.content.chapters && !contentPreview) {
            const firstChapter = entry.content.chapters[0];
            if (firstChapter && firstChapter.content) {
              contentPreview = firstChapter.content.substring(0, 200);
            }
          }
        }
      });

      return {
        ...book,
        totalSize,
        contentPreview,
        hasLargeContent: totalSize > 50000
      };
    });

    const booksWithSize = (await Promise.all(largeBooksPromises)).filter(book => book !== null);
    
    // Sort by content size
    booksWithSize.sort((a, b) => b.totalSize - a.totalSize);
    
    console.log('\n📊 Books sorted by content size:');
    booksWithSize.slice(0, 10).forEach(book => {
      console.log(`   ID: ${book.id} - "${book.title}"`);
      console.log(`       Size: ${book.totalSize.toLocaleString()} characters`);
      console.log(`       Preview: "${book.contentPreview}..."`);
      console.log(`       Large content: ${book.hasLargeContent ? 'YES' : 'NO'}`);
      console.log('');
    });

    // Look specifically for content that might be the original MLK PDF
    console.log('3. Looking for potential original MLK content...');
    
    const potentialMLKBooks = booksWithSize.filter(book => 
      (book.title.toLowerCase().includes('apelo') || 
       book.title.toLowerCase().includes('martin') ||
       book.author.toLowerCase().includes('martin')) &&
      book.totalSize > 50000
    );

    if (potentialMLKBooks.length > 0) {
      console.log(`🎯 Found ${potentialMLKBooks.length} potential MLK books with large content:`);
      
      for (const book of potentialMLKBooks) {
        console.log(`\n📖 Analyzing Book ID ${book.id}: "${book.title}"`);
        console.log(`   Size: ${book.totalSize.toLocaleString()} characters`);
        
        // Get the actual content to analyze
        const { data: fullContent, error: fullError } = await supabase
          .from('book_contents')
          .select('content')
          .eq('book_id', book.id);

        if (fullError || !fullContent || fullContent.length === 0) {
          console.log('   ❌ Could not fetch full content');
          continue;
        }

        const content = fullContent[0].content;
        
        if (typeof content === 'string') {
          console.log('   📄 String content:');
          console.log(`       Length: ${content.length} characters`);
          console.log(`       Contains "1955": ${content.includes('1955')}`);
          console.log(`       Contains "segregação": ${content.includes('segregação')}`);
          console.log(`       Contains "direitos civis": ${content.includes('direitos civis')}`);
          console.log(`       Contains "Montgomery": ${content.includes('Montgomery')}`);
          console.log(`       Preview: "${content.substring(0, 300)}..."`);
          
          // This might be the original PDF content
          if (content.length > 100000) {
            console.log('   🎯 THIS MIGHT BE THE ORIGINAL PDF CONTENT!');
          }
          
        } else if (typeof content === 'object' && content.chapters) {
          console.log('   📚 Structured content:');
          console.log(`       Chapters: ${content.chapters.length}`);
          
          // Check if any chapter has very long content (original PDF)
          content.chapters.forEach((chapter, index) => {
            if (chapter.content && chapter.content.length > 50000) {
              console.log(`       Chapter ${index + 1} has ${chapter.content.length} chars - MIGHT BE ORIGINAL!`);
              console.log(`       Preview: "${chapter.content.substring(0, 300)}..."`);
            }
          });
        }
      }
    } else {
      console.log('❌ No potential MLK books with large content found');
    }

    console.log('\n💡 NEXT STEPS:');
    console.log('1. If original content found above, copy it to the main MLK book');
    console.log('2. If not found, the original PDF content may have been lost');
    console.log('3. In that case, we need to either:');
    console.log('   a) Restore from a backup if available');
    console.log('   b) Re-extract from the original PDF');
    console.log('   c) Use a representative summary of MLK speeches');

  } catch (error) {
    console.error('💥 Error during search:', error);
  }
}

searchAllBooksForOriginal();
