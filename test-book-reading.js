import puppeteer from 'puppeteer';

async function testBookReading() {
  let browser;
  try {
    console.log('🚀 Testando funcionalidade de leitura de livros...\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Capturar erros do console
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`❌ Console Error: ${msg.text()}`);
      } else if (msg.type() === 'warn') {
        console.log(`⚠️ Console Warning: ${msg.text()}`);
      }
    });

    page.on('pageerror', error => {
      console.log(`💥 Page Error: ${error.message}`);
    });

    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    // Aguardar carregamento
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('🔍 Procurando por botões de "Ler Agora" ou "Ler o livro"...');
    
    // Procurar por botões de leitura
    const readButtons = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button, a'));
      return buttons
        .filter(btn => {
          const text = btn.textContent?.toLowerCase() || '';
          return text.includes('ler agora') || 
                 text.includes('ler o livro') || 
                 text.includes('ler') ||
                 text.includes('começar leitura');
        })
        .map(btn => ({
          text: btn.textContent?.trim(),
          href: btn.href || null,
          clickable: true
        }));
    });

    console.log(`📚 Encontrados ${readButtons.length} botões de leitura:`);
    readButtons.forEach((btn, index) => {
      console.log(`   ${index + 1}. "${btn.text}"`);
    });

    if (readButtons.length === 0) {
      console.log('⚠️ Nenhum botão de leitura encontrado. Vamos verificar se há livros na biblioteca...');
      
      // Verificar se há livros listados
      const books = await page.evaluate(() => {
        const bookElements = Array.from(document.querySelectorAll('[class*="book"], [class*="card"], .grid > div'));
        return bookElements.map(el => ({
          text: el.textContent?.substring(0, 100) + '...',
          hasButton: !!el.querySelector('button, a')
        }));
      });

      console.log(`📖 Encontrados ${books.length} elementos que podem ser livros:`);
      books.slice(0, 5).forEach((book, index) => {
        console.log(`   ${index + 1}. ${book.text} (Tem botão: ${book.hasButton})`);
      });
    } else {
      // Tentar clicar no primeiro botão de leitura
      console.log('\n🖱️ Tentando clicar no primeiro botão de leitura...');
      
      try {
        await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button, a'));
          const readButton = buttons.find(btn => {
            const text = btn.textContent?.toLowerCase() || '';
            return text.includes('ler agora') || 
                   text.includes('ler o livro') || 
                   text.includes('ler') ||
                   text.includes('começar leitura');
          });
          
          if (readButton) {
            readButton.click();
            return true;
          }
          return false;
        });

        // Aguardar navegação ou mudança de conteúdo
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Verificar se chegamos na página de leitura
        const currentUrl = page.url();
        console.log(`📍 URL atual: ${currentUrl}`);

        const pageContent = await page.evaluate(() => {
          return {
            title: document.title,
            hasBookContent: !!document.querySelector('[class*="book-content"], [class*="reader"], .prose, article'),
            contentPreview: document.body.innerText.substring(0, 300) + '...',
            hasChapters: !!document.querySelector('[class*="chapter"], h1, h2, h3'),
            hasBookTitle: document.body.innerText.toLowerCase().includes('livro') ||
                         document.body.innerText.toLowerCase().includes('capítulo') ||
                         document.body.innerText.toLowerCase().includes('resumo')
          };
        });

        console.log('\n📄 Análise da página de leitura:');
        console.log(`   📝 Título: ${pageContent.title}`);
        console.log(`   📚 Tem conteúdo de livro: ${pageContent.hasBookContent}`);
        console.log(`   📖 Tem capítulos/títulos: ${pageContent.hasChapters}`);
        console.log(`   📋 Parece ser página de livro: ${pageContent.hasBookTitle}`);
        console.log(`   📄 Preview do conteúdo: ${pageContent.contentPreview}`);

        // Verificar se há conteúdo específico de erro
        const hasErrorContent = await page.evaluate(() => {
          const text = document.body.innerText.toLowerCase();
          return text.includes('livro não encontrado') ||
                 text.includes('conteúdo não disponível') ||
                 text.includes('erro') ||
                 text.includes('placeholder');
        });

        if (hasErrorContent) {
          console.log('❌ PROBLEMA DETECTADO: Página mostra conteúdo de erro ou placeholder!');
        } else if (pageContent.hasBookContent || pageContent.hasChapters) {
          console.log('✅ SUCESSO: Página parece mostrar conteúdo real do livro!');
        } else {
          console.log('⚠️ INCERTO: Não foi possível determinar se o conteúdo é real ou placeholder');
        }

      } catch (error) {
        console.log(`❌ Erro ao clicar no botão: ${error.message}`);
      }
    }

    // Tirar screenshot final
    await page.screenshot({ 
      path: 'book-reading-test.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot salvo como book-reading-test.png');

    // Aguardar antes de fechar
    await new Promise(resolve => setTimeout(resolve, 3000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testBookReading();
