import puppeteer from 'puppeteer';

async function testBookDirect() {
  let browser;
  try {
    console.log('🎯 TESTE DIRETO: Testando funcionalidade de leitura diretamente\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Capturar logs do console
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (type === 'error') {
        console.log(`🔴 Console Error: ${text}`);
      } else if (text.includes('Loading book') || text.includes('Book') || text.includes('content')) {
        console.log(`🔵 Console: ${text}`);
      }
    });

    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Teste 1: Verificar se podemos acessar o BookLoader diretamente
    console.log('\n📚 TESTE 1: Testando BookLoader diretamente no console...');
    
    const bookLoaderTest = await page.evaluate(async () => {
      try {
        // Tentar importar e usar o BookLoader
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        
        console.log('BookLoader imported successfully');
        
        // Testar carregamento do livro ID 1
        const book = await BookLoader.loadBook('1');
        
        return {
          success: true,
          bookFound: !!book,
          bookTitle: book?.title || 'N/A',
          bookAuthor: book?.author || 'N/A',
          hasChapters: book?.content?.chapters?.length || 0,
          hasKeyPoints: book?.content?.key_points?.length || 0,
          firstChapterTitle: book?.content?.chapters?.[0]?.title || 'N/A',
          contentPreview: book?.content?.chapters?.[0]?.content?.substring(0, 200) || 'N/A'
        };
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (bookLoaderTest.success) {
      console.log('✅ BookLoader funcionando:');
      console.log(`   📖 Livro encontrado: ${bookLoaderTest.bookFound}`);
      console.log(`   📝 Título: ${bookLoaderTest.bookTitle}`);
      console.log(`   ✍️ Autor: ${bookLoaderTest.bookAuthor}`);
      console.log(`   📚 Capítulos: ${bookLoaderTest.hasChapters}`);
      console.log(`   🔑 Pontos-chave: ${bookLoaderTest.hasKeyPoints}`);
      console.log(`   📄 Primeiro capítulo: ${bookLoaderTest.firstChapterTitle}`);
      console.log(`   📋 Preview: ${bookLoaderTest.contentPreview}...`);
    } else {
      console.log(`❌ Erro no BookLoader: ${bookLoaderTest.error}`);
    }

    // Teste 2: Simular abertura do PDFReader
    console.log('\n📖 TESTE 2: Simulando abertura do PDFReader...');
    
    const readerTest = await page.evaluate(async () => {
      try {
        // Simular o que acontece quando clicamos em "Ler Agora"
        const bookId = '1';
        const bookTitle = 'Hábitos Atômicos';
        const bookAuthor = 'James Clear';
        
        // Verificar se o componente PDFReader pode ser renderizado
        const readerContainer = document.createElement('div');
        readerContainer.id = 'pdf-reader-test';
        readerContainer.innerHTML = `
          <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: white; z-index: 9999; padding: 20px;">
            <h1>Testando PDFReader</h1>
            <p>Book ID: ${bookId}</p>
            <p>Title: ${bookTitle}</p>
            <p>Author: ${bookAuthor}</p>
            <div id="content-area">Carregando conteúdo...</div>
          </div>
        `;
        
        document.body.appendChild(readerContainer);
        
        // Tentar carregar conteúdo usando BookLoader
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        const bookText = await BookLoader.getBookText(bookId);
        
        const contentArea = document.getElementById('content-area');
        if (contentArea) {
          contentArea.innerHTML = `<pre style="white-space: pre-wrap; font-family: serif; line-height: 1.6;">${bookText.substring(0, 1000)}...</pre>`;
        }
        
        return {
          success: true,
          contentLength: bookText.length,
          hasContent: bookText.length > 100,
          contentPreview: bookText.substring(0, 300)
        };
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (readerTest.success) {
      console.log('✅ PDFReader simulado com sucesso:');
      console.log(`   📏 Tamanho do conteúdo: ${readerTest.contentLength} caracteres`);
      console.log(`   📚 Tem conteúdo: ${readerTest.hasContent}`);
      console.log(`   📄 Preview:\n${readerTest.contentPreview}...`);
      
      // Aguardar para ver o resultado
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Verificar se o conteúdo foi renderizado
      const renderedContent = await page.evaluate(() => {
        const contentArea = document.getElementById('content-area');
        return {
          hasContentArea: !!contentArea,
          contentText: contentArea?.textContent?.substring(0, 200) || 'N/A'
        };
      });
      
      console.log(`   🖥️ Conteúdo renderizado: ${renderedContent.hasContentArea}`);
      console.log(`   📋 Texto renderizado: ${renderedContent.contentText}...`);
      
    } else {
      console.log(`❌ Erro no PDFReader: ${readerTest.error}`);
    }

    // Teste 3: Verificar se há erros de "Livro Não Encontrado"
    console.log('\n🔍 TESTE 3: Verificando se há conteúdo de erro...');
    
    const errorCheck = await page.evaluate(() => {
      const bodyText = document.body.innerText.toLowerCase();
      return {
        hasNotFoundError: bodyText.includes('livro não encontrado'),
        hasPlaceholderError: bodyText.includes('placeholder'),
        hasGenericError: bodyText.includes('conteúdo não disponível'),
        hasAutorDesconhecido: bodyText.includes('autor desconhecido'),
        bodyLength: bodyText.length
      };
    });

    console.log(`❌ Erro "Livro Não Encontrado": ${errorCheck.hasNotFoundError}`);
    console.log(`❌ Erro "Placeholder": ${errorCheck.hasPlaceholderError}`);
    console.log(`❌ Erro "Conteúdo Não Disponível": ${errorCheck.hasGenericError}`);
    console.log(`❌ "Autor Desconhecido": ${errorCheck.hasAutorDesconhecido}`);

    // Resultado final
    console.log('\n🎯 RESULTADO FINAL:');
    
    if (bookLoaderTest.success && bookLoaderTest.bookFound && readerTest.success && readerTest.hasContent) {
      if (!errorCheck.hasNotFoundError && !errorCheck.hasPlaceholderError) {
        console.log('✅ SUCESSO COMPLETO: A funcionalidade de leitura está funcionando perfeitamente!');
        console.log('   - BookLoader carrega dados reais do Supabase');
        console.log('   - PDFReader exibe conteúdo estruturado');
        console.log('   - Não há mensagens de erro ou placeholder');
        console.log('   - O bug foi CORRIGIDO com sucesso! 🎉');
      } else {
        console.log('⚠️ SUCESSO PARCIAL: Funcionalidade funciona, mas ainda há algumas mensagens de erro');
      }
    } else {
      console.log('❌ PROBLEMA DETECTADO: A funcionalidade ainda não está funcionando completamente');
      if (!bookLoaderTest.success) {
        console.log('   - BookLoader não está funcionando');
      }
      if (!readerTest.success) {
        console.log('   - PDFReader não está funcionando');
      }
    }

    // Screenshot final
    await page.screenshot({ 
      path: 'direct-test-result.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot salvo como direct-test-result.png');

    await new Promise(resolve => setTimeout(resolve, 3000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testBookDirect();
