import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixBookContentRelationship() {
  try {
    console.log('🔧 CORRIGINDO RELACIONAMENTO BOOK-CONTENT\n');

    const bookId = 85;

    console.log('1. Verificando dados atuais...');
    
    // Verificar livro
    const { data: book, error: bookError } = await supabase
      .from('books')
      .select('*')
      .eq('id', bookId)
      .single();

    if (bookError) {
      console.log('❌ Erro ao buscar livro:', bookError.message);
      return;
    }

    console.log(`✅ Livro encontrado: "${book.title}"`);

    // Verificar conteúdo
    const { data: contents, error: contentError } = await supabase
      .from('book_contents')
      .select('*')
      .eq('book_id', bookId);

    if (contentError) {
      console.log('❌ Erro ao buscar conteúdo:', contentError.message);
      return;
    }

    console.log(`✅ Conteúdo encontrado: ${contents.length} entrada(s)`);

    if (contents.length === 0) {
      console.log('❌ Nenhum conteúdo encontrado para este livro');
      return;
    }

    const content = contents[0];
    console.log(`   ID do conteúdo: ${content.id}`);
    console.log(`   Book ID: ${content.book_id}`);
    console.log(`   Tipo: ${typeof content.content}`);

    console.log('\n2. Testando diferentes abordagens de consulta...');

    // Abordagem 1: Consulta manual com LEFT JOIN
    console.log('   2a. Testando LEFT JOIN manual...');
    try {
      const { data: manualJoin, error: manualError } = await supabase
        .from('books')
        .select(`
          id,
          title,
          author,
          category,
          description,
          duration,
          book_contents!left (
            id,
            content
          )
        `)
        .eq('id', bookId)
        .single();

      if (manualError) {
        console.log('   ❌ LEFT JOIN manual falhou:', manualError.message);
      } else {
        console.log('   ✅ LEFT JOIN manual bem-sucedido');
        console.log(`   ✅ book_contents: ${manualJoin.book_contents ? 'presente' : 'ausente'}`);
        if (manualJoin.book_contents) {
          console.log(`   ✅ Tipo: ${typeof manualJoin.book_contents}`);
          console.log(`   ✅ É array: ${Array.isArray(manualJoin.book_contents)}`);
          console.log(`   ✅ Comprimento: ${manualJoin.book_contents.length}`);
        }
      }
    } catch (error) {
      console.log('   ❌ Erro no LEFT JOIN manual:', error.message);
    }

    // Abordagem 2: Consulta com inner join explícito
    console.log('   2b. Testando INNER JOIN explícito...');
    try {
      const { data: innerJoin, error: innerError } = await supabase
        .from('books')
        .select(`
          *,
          book_contents!inner (
            content
          )
        `)
        .eq('id', bookId)
        .single();

      if (innerError) {
        console.log('   ❌ INNER JOIN falhou:', innerError.message);
      } else {
        console.log('   ✅ INNER JOIN bem-sucedido');
        console.log(`   ✅ book_contents presente: ${!!innerJoin.book_contents}`);
      }
    } catch (error) {
      console.log('   ❌ Erro no INNER JOIN:', error.message);
    }

    // Abordagem 3: Consulta separada e combinação manual
    console.log('   2c. Testando consulta separada...');
    try {
      const bookData = await supabase
        .from('books')
        .select('*')
        .eq('id', bookId)
        .single();

      const contentData = await supabase
        .from('book_contents')
        .select('content')
        .eq('book_id', bookId);

      if (bookData.error || contentData.error) {
        console.log('   ❌ Consulta separada falhou');
      } else {
        console.log('   ✅ Consulta separada bem-sucedida');
        
        // Combinar manualmente
        const combinedData = {
          ...bookData.data,
          book_contents: contentData.data
        };

        console.log(`   ✅ Dados combinados: book_contents tem ${combinedData.book_contents.length} entrada(s)`);

        // Testar se esta estrutura funciona
        if (combinedData.book_contents && combinedData.book_contents.length > 0) {
          const content = combinedData.book_contents[0].content;
          console.log('   ✅ Estrutura de conteúdo válida');
          console.log(`   ✅ Tipo: ${typeof content}`);
          console.log(`   ✅ Tem chapters: ${!!(content && content.chapters)}`);
          
          if (content && content.chapters && content.chapters.length > 0) {
            console.log('   ✅ Capítulos válidos');
            console.log(`   ✅ Primeiro capítulo: "${content.chapters[0].title}"`);
            console.log(`   ✅ Tamanho: ${content.chapters[0].content?.length || 0} caracteres`);
          }
        }
      }
    } catch (error) {
      console.log('   ❌ Erro na consulta separada:', error.message);
    }

    console.log('\n3. Verificando permissões e políticas RLS...');
    
    // Verificar se RLS está causando problemas
    try {
      const { data: rlsTest, error: rlsError } = await supabase
        .rpc('get_book_with_content', { book_id: bookId });

      if (rlsError) {
        console.log('   ❌ RPC não disponível (normal):', rlsError.message);
      } else {
        console.log('   ✅ RPC funcionou');
      }
    } catch (error) {
      console.log('   ❌ RPC não disponível (esperado)');
    }

    console.log('\n4. Criando solução alternativa...');
    
    // Como a consulta JOIN não está funcionando, vou criar uma função
    // que simula o comportamento esperado
    async function getBookWithContent(bookId) {
      try {
        // Buscar livro
        const { data: book, error: bookError } = await supabase
          .from('books')
          .select('*')
          .eq('id', bookId)
          .single();

        if (bookError) throw bookError;

        // Buscar conteúdo
        const { data: contents, error: contentError } = await supabase
          .from('book_contents')
          .select('content')
          .eq('book_id', bookId);

        if (contentError) throw contentError;

        // Combinar
        return {
          ...book,
          book_contents: contents
        };
      } catch (error) {
        throw error;
      }
    }

    console.log('   Testando função alternativa...');
    const alternativeResult = await getBookWithContent(bookId);
    
    console.log('   ✅ Função alternativa funcionou');
    console.log(`   ✅ Livro: "${alternativeResult.title}"`);
    console.log(`   ✅ Conteúdo: ${alternativeResult.book_contents.length} entrada(s)`);
    
    if (alternativeResult.book_contents.length > 0) {
      const content = alternativeResult.book_contents[0].content;
      console.log(`   ✅ Estrutura válida: ${!!(content && content.chapters)}`);
    }

    console.log('\n🎯 DIAGNÓSTICO E SOLUÇÃO:');
    console.log('❌ PROBLEMA: A consulta JOIN do Supabase não está funcionando corretamente');
    console.log('✅ SOLUÇÃO: Usar consultas separadas e combinar manualmente');
    console.log('');
    console.log('🔧 PRÓXIMOS PASSOS:');
    console.log('   1. Modificar o BookLoader para usar consultas separadas');
    console.log('   2. Ou criar uma função RPC no Supabase');
    console.log('   3. Ou usar uma abordagem de cache local');
    console.log('');
    console.log('💡 RECOMENDAÇÃO IMEDIATA:');
    console.log('   Modificar o BookLoader.js para usar a abordagem de consulta separada');

  } catch (error) {
    console.error('💥 Erro durante correção:', error.message);
  }
}

fixBookContentRelationship();
