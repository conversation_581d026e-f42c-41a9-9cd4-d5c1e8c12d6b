import fs from 'fs';
import pdf from 'pdf-parse';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function extractMLKPDFReal() {
  try {
    console.log('📄 EXTRAINDO TEXTO REAL DO PDF DO MLK\n');

    const pdfPath = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final/um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf';

    console.log('1. Verificando se o arquivo PDF existe...');
    if (!fs.existsSync(pdfPath)) {
      console.log('❌ Arquivo PDF não encontrado:', pdfPath);
      return;
    }

    console.log('✅ Arquivo PDF encontrado');
    const stats = fs.statSync(pdfPath);
    console.log(`   Tamanho: ${stats.size.toLocaleString()} bytes`);

    console.log('\n2. Lendo arquivo PDF...');
    const dataBuffer = fs.readFileSync(pdfPath);
    console.log(`✅ PDF lido: ${dataBuffer.length.toLocaleString()} bytes`);

    console.log('\n3. Extraindo texto com pdf-parse...');
    const data = await pdf(dataBuffer);

    console.log('✅ Texto extraído com sucesso!');
    console.log(`   📄 Páginas: ${data.numpages}`);
    console.log(`   📝 Caracteres: ${data.text.length.toLocaleString()}`);
    console.log(`   ℹ️ Info: ${JSON.stringify(data.info)}`);

    if (data.text.length === 0) {
      console.log('❌ Nenhum texto foi extraído do PDF');
      console.log('💡 O PDF pode estar baseado em imagem ou protegido');
      return;
    }

    // Verificar se o texto contém conteúdo do MLK
    const mlkKeywords = [
      'Martin Luther King',
      'direitos civis',
      'discurso',
      'Montgomery',
      'Birmingham',
      'dream',
      'não-violência',
      'segregação',
      'I have a dream',
      'boicote',
      'marcha',
      'Washington',
      'apelo',
      'consciência'
    ];

    const foundKeywords = mlkKeywords.filter(keyword => 
      data.text.toLowerCase().includes(keyword.toLowerCase())
    );

    console.log('\n4. Verificando autenticidade do conteúdo...');
    console.log(`   🔍 Palavras-chave encontradas: ${foundKeywords.length}/${mlkKeywords.length}`);
    
    if (foundKeywords.length > 0) {
      console.log(`   ✅ Palavras encontradas: ${foundKeywords.join(', ')}`);
    }

    const isAuthenticMLK = foundKeywords.length >= 3;
    console.log(`   🎯 Conteúdo autêntico do MLK: ${isAuthenticMLK ? '✅ SIM' : '❌ NÃO'}`);

    console.log('\n5. Preview do texto extraído (primeiros 500 caracteres):');
    console.log(`"${data.text.substring(0, 500)}..."`);

    if (!isAuthenticMLK) {
      console.log('\n⚠️ ATENÇÃO: O texto extraído não parece ser sobre MLK');
      console.log('💡 Pode ser um PDF diferente ou com problemas na extração');
    }

    // Limpar e formatar o texto
    console.log('\n6. Limpando e formatando texto...');
    
    let cleanText = data.text
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Múltiplas quebras de linha
      .replace(/\s+/g, ' ') // Múltiplos espaços
      .replace(/\n /g, '\n') // Espaços no início de linhas
      .trim();

    console.log(`✅ Texto limpo: ${cleanText.length.toLocaleString()} caracteres`);

    // Criar estrutura de conteúdo
    console.log('\n7. Criando estrutura de conteúdo...');
    
    const contentStructure = {
      chapters: [
        {
          title: "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King",
          content: cleanText
        }
      ],
      key_points: [],
      practical_exercises: [],
      total_characters: cleanText.length,
      total_pages: data.numpages,
      source: `Extraído do PDF: ${pdfPath.split('/').pop()}`,
      extraction_method: 'pdf-parse',
      extraction_date: new Date().toISOString(),
      pdf_info: data.info
    };

    console.log('✅ Estrutura criada');

    // Atualizar banco de dados
    console.log('\n8. Atualizando banco de dados...');
    
    const bookId = 85;

    // Remover conteúdo anterior
    const { error: deleteError } = await supabase
      .from('book_contents')
      .delete()
      .eq('book_id', bookId);

    if (deleteError) {
      console.log('❌ Erro ao deletar conteúdo anterior:', deleteError.message);
      return;
    }

    console.log('✅ Conteúdo anterior removido');

    // Inserir novo conteúdo
    const { error: insertError } = await supabase
      .from('book_contents')
      .insert({
        book_id: bookId,
        content: contentStructure
      });

    if (insertError) {
      console.log('❌ Erro ao inserir conteúdo:', insertError.message);
      return;
    }

    console.log('✅ Conteúdo real inserido no banco');

    // Atualizar descrição do livro
    const { error: updateError } = await supabase
      .from('books')
      .update({
        description: `Conteúdo extraído do PDF original (${cleanText.length.toLocaleString()} caracteres, ${data.numpages} páginas).`
      })
      .eq('id', bookId);

    if (updateError) {
      console.log('❌ Erro ao atualizar descrição:', updateError.message);
    } else {
      console.log('✅ Descrição do livro atualizada');
    }

    console.log('\n🎉 EXTRAÇÃO CONCLUÍDA COM SUCESSO!');
    console.log('');
    console.log('📊 ESTATÍSTICAS FINAIS:');
    console.log(`   📄 Arquivo: ${pdfPath.split('/').pop()}`);
    console.log(`   📏 Tamanho original: ${stats.size.toLocaleString()} bytes`);
    console.log(`   📄 Páginas do PDF: ${data.numpages}`);
    console.log(`   📝 Caracteres extraídos: ${cleanText.length.toLocaleString()}`);
    console.log(`   🔍 Palavras-chave MLK: ${foundKeywords.length}/${mlkKeywords.length}`);
    console.log(`   🎯 Conteúdo autêntico: ${isAuthenticMLK ? 'SIM' : 'NÃO'}`);
    console.log('');
    console.log('✅ RESULTADO:');
    console.log('   ✓ Texto extraído diretamente do PDF original');
    console.log('   ✓ Conteúdo armazenado no banco de dados');
    console.log('   ✓ Pronto para exibição na aplicação');
    console.log('   ✓ Formatação Kindle será aplicada automaticamente');
    console.log('');
    console.log('🎯 PRÓXIMOS PASSOS:');
    console.log('   1. Testar a aplicação para ver o conteúdo real');
    console.log('   2. Verificar se os controles de fonte funcionam');
    console.log('   3. Confirmar que o texto está legível e bem formatado');

    // Salvar uma cópia do texto extraído para debug
    console.log('\n9. Salvando cópia para debug...');
    fs.writeFileSync('mlk-extracted-text.txt', cleanText, 'utf8');
    console.log('✅ Texto salvo em: mlk-extracted-text.txt');

  } catch (error) {
    console.error('💥 Erro durante extração:', error.message);
    console.error('Stack:', error.stack);
  }
}

extractMLKPDFReal();
