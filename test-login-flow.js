import puppeteer from 'puppeteer';

async function testLoginFlow() {
  let browser;
  try {
    console.log('🔐 TESTANDO FLUXO DE LOGIN COMPLETO\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 1. Navegando para aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('🔍 2. Verificando estado inicial...');
    
    const initialState = await page.evaluate(() => {
      return {
        url: window.location.href,
        title: document.title,
        hasEntrarButton: !!Array.from(document.querySelectorAll('button')).find(btn => 
          btn.textContent?.includes('Entrar')
        ),
        bodyText: document.body.textContent.substring(0, 200)
      };
    });

    console.log(`   URL: ${initialState.url}`);
    console.log(`   Título: ${initialState.title}`);
    console.log(`   Tem botão Entrar: ${initialState.hasEntrarButton ? '✅' : '❌'}`);
    console.log(`   Conteúdo: "${initialState.bodyText}..."`);

    if (!initialState.hasEntrarButton) {
      console.log('❌ Botão Entrar não encontrado');
      return;
    }

    console.log('🖱️ 3. Clicando no botão Entrar...');
    
    const entrarClicked = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const entrarButton = buttons.find(btn => 
        btn.textContent?.includes('Entrar')
      );
      
      if (entrarButton) {
        entrarButton.click();
        return { success: true, buttonText: entrarButton.textContent };
      }
      
      return { success: false, error: 'Botão não encontrado' };
    });

    if (!entrarClicked.success) {
      console.log('❌ Não foi possível clicar no botão Entrar');
      return;
    }

    console.log(`✅ Botão clicado: "${entrarClicked.buttonText}"`);

    console.log('⏳ 4. Aguardando modal de login aparecer...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('🔍 5. Verificando se modal de login apareceu...');
    
    const modalState = await page.evaluate(() => {
      const emailInputs = document.querySelectorAll('input[type="email"]');
      const passwordInputs = document.querySelectorAll('input[type="password"]');
      const loginForms = document.querySelectorAll('form');
      const modals = document.querySelectorAll('[class*="modal"], [class*="dialog"], [class*="overlay"]');
      
      return {
        hasEmailInput: emailInputs.length > 0,
        hasPasswordInput: passwordInputs.length > 0,
        hasForm: loginForms.length > 0,
        hasModal: modals.length > 0,
        emailInputVisible: emailInputs.length > 0 ? window.getComputedStyle(emailInputs[0]).display !== 'none' : false,
        modalContent: modals.length > 0 ? modals[0].textContent.substring(0, 200) : 'Nenhum modal encontrado'
      };
    });

    console.log(`   Email input: ${modalState.hasEmailInput ? '✅' : '❌'}`);
    console.log(`   Password input: ${modalState.hasPasswordInput ? '✅' : '❌'}`);
    console.log(`   Form: ${modalState.hasForm ? '✅' : '❌'}`);
    console.log(`   Modal: ${modalState.hasModal ? '✅' : '❌'}`);
    console.log(`   Email visível: ${modalState.emailInputVisible ? '✅' : '❌'}`);
    console.log(`   Conteúdo do modal: "${modalState.modalContent}..."`);

    if (!modalState.hasEmailInput || !modalState.hasPasswordInput) {
      console.log('❌ Modal de login não apareceu corretamente');
      return;
    }

    console.log('📝 6. Preenchendo formulário de login...');
    
    // Usar credenciais de teste
    const testEmail = '<EMAIL>';
    const testPassword = 'password123';

    await page.type('input[type="email"]', testEmail);
    await page.type('input[type="password"]', testPassword);

    console.log(`✅ Email preenchido: ${testEmail}`);
    console.log(`✅ Senha preenchida: ${testPassword}`);

    console.log('🚀 7. Submetendo formulário...');
    
    // Procurar e clicar no botão de submit
    const submitResult = await page.evaluate(() => {
      const submitButtons = Array.from(document.querySelectorAll('button[type="submit"], button')).filter(btn => {
        const text = btn.textContent?.toLowerCase() || '';
        return text.includes('entrar') || text.includes('login') || text.includes('acessar');
      });
      
      if (submitButtons.length > 0) {
        submitButtons[0].click();
        return { success: true, buttonText: submitButtons[0].textContent };
      }
      
      return { success: false, error: 'Botão de submit não encontrado' };
    });

    if (!submitResult.success) {
      console.log('❌ Não foi possível submeter o formulário');
      return;
    }

    console.log(`✅ Formulário submetido: "${submitResult.buttonText}"`);

    console.log('⏳ 8. Aguardando resposta da autenticação...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('🔍 9. Verificando se login foi bem-sucedido...');
    
    const authResult = await page.evaluate(() => {
      // Verificar se ainda está na landing page ou se foi para o dashboard
      const hasEntrarButton = !!Array.from(document.querySelectorAll('button')).find(btn => 
        btn.textContent?.includes('Entrar')
      );
      
      const hasDashboard = !!document.querySelector('[class*="dashboard"], [class*="sidebar"], [class*="library"]');
      const hasBookGrid = !!document.querySelector('[class*="book"], [class*="grid"]');
      const hasUserInfo = !!document.querySelector('[class*="user"], [class*="profile"]');
      
      // Verificar se há mensagens de erro
      const errorElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const text = el.textContent?.toLowerCase() || '';
        return text.includes('erro') || text.includes('error') || text.includes('inválido') || text.includes('incorreto');
      });
      
      return {
        stillHasEntrarButton: hasEntrarButton,
        hasDashboard,
        hasBookGrid,
        hasUserInfo,
        hasErrors: errorElements.length > 0,
        errorMessages: errorElements.map(el => el.textContent.substring(0, 100)),
        currentUrl: window.location.href,
        bodyText: document.body.textContent.substring(0, 500)
      };
    });

    console.log('\n📊 RESULTADO DA AUTENTICAÇÃO:');
    console.log(`   URL atual: ${authResult.currentUrl}`);
    console.log(`   Ainda tem botão Entrar: ${authResult.stillHasEntrarButton ? '❌' : '✅'}`);
    console.log(`   Tem dashboard: ${authResult.hasDashboard ? '✅' : '❌'}`);
    console.log(`   Tem grid de livros: ${authResult.hasBookGrid ? '✅' : '❌'}`);
    console.log(`   Tem info do usuário: ${authResult.hasUserInfo ? '✅' : '❌'}`);
    console.log(`   Tem erros: ${authResult.hasErrors ? '❌' : '✅'}`);

    if (authResult.hasErrors) {
      console.log('\n❌ ERROS ENCONTRADOS:');
      authResult.errorMessages.forEach((error, index) => {
        console.log(`   ${index + 1}. "${error}..."`);
      });
    }

    console.log('\n📋 CONTEÚDO DA PÁGINA:');
    console.log(`"${authResult.bodyText}..."`);

    // Determinar se o login foi bem-sucedido
    const loginSuccess = !authResult.stillHasEntrarButton && 
                        (authResult.hasDashboard || authResult.hasBookGrid || authResult.hasUserInfo);

    console.log('\n🎯 AVALIAÇÃO FINAL:');
    console.log(`   ${loginSuccess ? '✅' : '❌'} Login bem-sucedido`);
    console.log(`   ${!authResult.hasErrors ? '✅' : '❌'} Sem erros de autenticação`);
    console.log(`   ${authResult.hasDashboard ? '✅' : '❌'} Dashboard carregado`);

    if (loginSuccess) {
      console.log('\n🎉 SUCESSO! LOGIN FUNCIONANDO:');
      console.log('   ✓ Modal de login aparece');
      console.log('   ✓ Formulário pode ser preenchido');
      console.log('   ✓ Autenticação funciona');
      console.log('   ✓ Dashboard carrega após login');
      
      console.log('\n📚 10. Procurando livro do MLK no dashboard...');
      
      // Agora que estamos no dashboard, procurar pelo livro do MLK
      const mlkBookSearch = await page.evaluate(() => {
        const searchTerms = [
          'Um Apelo à Consciência',
          'Martin Luther King',
          'Melhores Discursos',
          'Apelo à Consciência'
        ];
        
        const allElements = Array.from(document.querySelectorAll('*'));
        
        for (let term of searchTerms) {
          for (let element of allElements) {
            const text = element.textContent || '';
            if (text.includes(term)) {
              return {
                found: true,
                term,
                text: text.substring(0, 100),
                element: element.tagName + '.' + element.className
              };
            }
          }
        }
        
        return { found: false };
      });

      if (mlkBookSearch.found) {
        console.log(`✅ Livro MLK encontrado no dashboard!`);
        console.log(`   Termo: "${mlkBookSearch.term}"`);
        console.log(`   Texto: "${mlkBookSearch.text}..."`);
        console.log(`   Elemento: ${mlkBookSearch.element}`);
      } else {
        console.log('❌ Livro MLK não encontrado no dashboard');
        
        // Debug: mostrar livros disponíveis
        const availableBooks = await page.evaluate(() => {
          const elements = Array.from(document.querySelectorAll('*'));
          const bookLike = elements.filter(el => {
            const text = el.textContent || '';
            return text.length > 20 && text.length < 200 && 
                   (text.includes('Autor') || text.includes('por') || text.includes('de'));
          }).slice(0, 10);
          
          return bookLike.map(el => el.textContent.substring(0, 80));
        });
        
        console.log('📖 Livros disponíveis no dashboard:');
        availableBooks.forEach((book, index) => {
          console.log(`   ${index + 1}. "${book}..."`);
        });
      }
      
    } else {
      console.log('\n❌ PROBLEMAS COM O LOGIN:');
      if (authResult.stillHasEntrarButton) console.log('   • Ainda mostra botão Entrar (não autenticou)');
      if (!authResult.hasDashboard) console.log('   • Dashboard não carregou');
      if (authResult.hasErrors) console.log('   • Erros de autenticação detectados');
      
      console.log('\n💡 POSSÍVEIS CAUSAS:');
      console.log('   • Credenciais de teste inválidas');
      console.log('   • Problema com Supabase Auth');
      console.log('   • Erro na configuração de autenticação');
      console.log('   • Problema de rede ou CORS');
    }

    // Manter navegador aberto para verificação manual
    console.log('\n⏳ Mantendo navegador aberto por 30 segundos para verificação manual...');
    await new Promise(resolve => setTimeout(resolve, 30000));

  } catch (error) {
    console.error('💥 Erro durante teste de login:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testLoginFlow();
