import puppeteer from 'puppeteer';

async function testFontThemeFixes() {
  let browser;
  try {
    console.log('🔧 TESTING FONT SIZE AND THEME FIXES\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test the formatting function directly in console
    console.log('🧪 Testing BookLoader formatting...');
    
    const formattingTest = await page.evaluate(async () => {
      try {
        // Force reload the module
        const timestamp = Date.now();
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${timestamp}`);
        
        const testText = `# Chapter One

This is a test paragraph with body text.

## Section Title

Another paragraph here.`;

        // Test formatting for different themes
        const lightFormatted = BookLoader.formatTextForReader(testText, 'light');
        const darkFormatted = BookLoader.formatTextForReader(testText, 'dark');
        const sepiaFormatted = BookLoader.formatTextForReader(testText, 'sepia');
        
        return {
          success: true,
          lightFormatted,
          darkFormatted,
          sepiaFormatted,
          hasChapterTitle: lightFormatted.includes('chapter-title'),
          hasSectionTitle: lightFormatted.includes('section-title'),
          hasBodyText: lightFormatted.includes('body-text'),
          noInlineColors: !lightFormatted.includes('color:') && !lightFormatted.includes('text-black')
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (formattingTest.success) {
      console.log('✅ BookLoader formatting working');
      console.log(`📖 Chapter titles: ${formattingTest.hasChapterTitle ? 'PRESENT' : 'MISSING'}`);
      console.log(`📑 Section titles: ${formattingTest.hasSectionTitle ? 'PRESENT' : 'MISSING'}`);
      console.log(`📄 Body text: ${formattingTest.hasBodyText ? 'PRESENT' : 'MISSING'}`);
      console.log(`🎨 No inline colors: ${formattingTest.noInlineColors ? 'CORRECT' : 'STILL PRESENT'}`);
      
      console.log('\n📋 Sample formatted output:');
      console.log(formattingTest.lightFormatted.substring(0, 200) + '...');
      
    } else {
      console.log('❌ BookLoader formatting error:');
      console.log(formattingTest.error);
    }

    // Test CSS classes
    console.log('\n🎨 Testing CSS theme classes...');
    
    const cssTest = await page.evaluate(() => {
      // Create test elements to check CSS
      const testContainer = document.createElement('div');
      testContainer.className = 'formatted-content light';
      testContainer.innerHTML = `
        <div class="chapter-title">TEST CHAPTER</div>
        <div class="section-title">TEST SECTION</div>
        <p class="body-text">Test body text</p>
      `;
      
      document.body.appendChild(testContainer);
      
      // Get computed styles
      const chapterStyle = window.getComputedStyle(testContainer.querySelector('.chapter-title'));
      const bodyStyle = window.getComputedStyle(testContainer.querySelector('.body-text'));
      
      const results = {
        chapterFontSize: chapterStyle.fontSize,
        chapterColor: chapterStyle.color,
        bodyFontSize: bodyStyle.fontSize,
        bodyColor: bodyStyle.color,
        chapterFontFamily: chapterStyle.fontFamily,
        bodyFontFamily: bodyStyle.fontFamily
      };
      
      // Test dark theme
      testContainer.className = 'formatted-content dark';
      const darkChapterStyle = window.getComputedStyle(testContainer.querySelector('.chapter-title'));
      const darkBodyStyle = window.getComputedStyle(testContainer.querySelector('.body-text'));
      
      results.darkChapterColor = darkChapterStyle.color;
      results.darkBodyColor = darkBodyStyle.color;
      
      // Test sepia theme
      testContainer.className = 'formatted-content sepia';
      const sepiaChapterStyle = window.getComputedStyle(testContainer.querySelector('.chapter-title'));
      const sepiaBodyStyle = window.getComputedStyle(testContainer.querySelector('.body-text'));
      
      results.sepiaChapterColor = sepiaChapterStyle.color;
      results.sepiaBodyColor = sepiaBodyStyle.color;
      
      // Clean up
      document.body.removeChild(testContainer);
      
      return results;
    });

    console.log('📊 CSS Test Results:');
    console.log(`🔤 Chapter font size: ${cssTest.chapterFontSize}`);
    console.log(`📝 Body font size: ${cssTest.bodyFontSize}`);
    console.log(`🎨 Light theme colors: Chapter(${cssTest.chapterColor}) Body(${cssTest.bodyColor})`);
    console.log(`🌙 Dark theme colors: Chapter(${cssTest.darkChapterColor}) Body(${cssTest.darkBodyColor})`);
    console.log(`🟤 Sepia theme colors: Chapter(${cssTest.sepiaChapterColor}) Body(${cssTest.sepiaBodyColor})`);
    console.log(`📚 Font family: ${cssTest.chapterFontFamily}`);

    // Analyze color correctness
    const lightCorrect = cssTest.chapterColor.includes('0, 0, 0') || cssTest.chapterColor.includes('rgb(0, 0, 0)');
    const darkCorrect = cssTest.darkChapterColor.includes('249, 250, 251') || cssTest.darkChapterColor.includes('rgb(249, 250, 251)');
    const sepiaCorrect = cssTest.sepiaChapterColor.includes('146, 64, 14') || cssTest.sepiaChapterColor.includes('rgb(146, 64, 14)');

    console.log('\n🎯 Color Analysis:');
    console.log(`☀️ Light theme (should be black): ${lightCorrect ? 'CORRECT' : 'INCORRECT'}`);
    console.log(`🌙 Dark theme (should be light): ${darkCorrect ? 'CORRECT' : 'INCORRECT'}`);
    console.log(`🟤 Sepia theme (should be brown): ${sepiaCorrect ? 'CORRECT' : 'INCORRECT'}`);

    // Summary
    const allTests = [
      { name: 'BookLoader formatting', passed: formattingTest.success },
      { name: 'Chapter titles present', passed: formattingTest.hasChapterTitle },
      { name: 'No inline colors', passed: formattingTest.noInlineColors },
      { name: 'Light theme colors', passed: lightCorrect },
      { name: 'Dark theme colors', passed: darkCorrect },
      { name: 'Sepia theme colors', passed: sepiaCorrect }
    ];

    const passedTests = allTests.filter(test => test.passed).length;
    const totalTests = allTests.length;

    console.log('\n📊 SUMMARY:');
    allTests.forEach(test => {
      console.log(`${test.passed ? '✅' : '❌'} ${test.name}`);
    });

    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed (${((passedTests/totalTests)*100).toFixed(1)}%)`);

    if (passedTests === totalTests) {
      console.log('\n🎉 ALL FIXES WORKING PERFECTLY!');
      console.log('✅ Font size controls should now work');
      console.log('✅ Theme colors should be correct');
      console.log('✅ CSS inheritance is working properly');
    } else if (passedTests >= totalTests * 0.8) {
      console.log('\n✅ MOST FIXES WORKING!');
      console.log('Some minor issues may remain.');
    } else {
      console.log('\n⚠️ SEVERAL ISSUES STILL NEED FIXING');
    }

    console.log('\n🚀 MANUAL TEST INSTRUCTIONS:');
    console.log('1. Open a book in the reader');
    console.log('2. Open settings panel (⚙️ icon)');
    console.log('3. Test font size controls (+/-) - text should change size immediately');
    console.log('4. Test theme buttons:');
    console.log('   • Light: Black text on white background');
    console.log('   • Dark: Light text on dark background');
    console.log('   • Sepia: Brown text on sepia background');
    console.log('5. Verify changes apply immediately without page refresh');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Error during test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testFontThemeFixes();
