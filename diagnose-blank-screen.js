import puppeteer from 'puppeteer';

async function diagnoseBlankScreen() {
  let browser;
  try {
    console.log('🔍 DIAGNÓSTICO: Investigando problema de tela em branco\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-web-security']
    });

    const page = await browser.newPage();
    
    // Capturar TODOS os logs do console
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`📝 Console [${type.toUpperCase()}]: ${text}`);
    });

    // Capturar erros de página
    page.on('pageerror', error => {
      console.log(`💥 Page Error: ${error.message}`);
      console.log(`   Stack: ${error.stack}`);
    });

    // Capturar falhas de requisição
    page.on('requestfailed', request => {
      console.log(`🚫 Request Failed: ${request.url()}`);
      console.log(`   Error: ${request.failure()?.errorText}`);
    });

    // Capturar respostas de erro
    page.on('response', response => {
      if (!response.ok()) {
        console.log(`❌ HTTP Error: ${response.status()} - ${response.url()}`);
      }
    });

    console.log('🌐 Navegando para http://localhost:5173...');
    
    try {
      await page.goto('http://localhost:5173', { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });
    } catch (error) {
      console.log(`❌ Erro ao navegar: ${error.message}`);
      return;
    }

    console.log('✅ Página carregada, aguardando renderização...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Diagnóstico detalhado
    const diagnosis = await page.evaluate(() => {
      const body = document.body;
      const root = document.getElementById('root');
      
      return {
        // Informações básicas
        title: document.title,
        url: window.location.href,
        readyState: document.readyState,
        
        // Estrutura DOM
        hasBody: !!body,
        bodyChildren: body?.children.length || 0,
        hasRoot: !!root,
        rootChildren: root?.children.length || 0,
        rootHTML: root?.innerHTML?.substring(0, 500) || 'N/A',
        
        // Conteúdo visível
        bodyText: body?.innerText?.substring(0, 500) || 'N/A',
        bodyHTML: body?.innerHTML?.substring(0, 1000) || 'N/A',
        
        // Elementos React
        hasReactElements: !!document.querySelector('[data-reactroot]') || 
                         !!document.querySelector('#root > div') ||
                         !!document.querySelector('[class*="react"]'),
        
        // CSS e estilos
        hasStylesheets: document.styleSheets.length,
        computedStyles: window.getComputedStyle(body),
        rootStyles: root ? window.getComputedStyle(root) : null,
        
        // JavaScript
        hasScripts: document.scripts.length,
        scriptSources: Array.from(document.scripts).map(s => s.src).filter(s => s),
        
        // Erros visíveis
        hasErrorElements: document.querySelectorAll('[class*="error"], .error').length,
        hasLoadingElements: document.querySelectorAll('[class*="loading"], .loading').length,
        
        // Viewport e dimensões
        viewportWidth: window.innerWidth,
        viewportHeight: window.innerHeight,
        bodyWidth: body?.offsetWidth || 0,
        bodyHeight: body?.offsetHeight || 0,
        rootWidth: root?.offsetWidth || 0,
        rootHeight: root?.offsetHeight || 0
      };
    });

    console.log('\n📊 DIAGNÓSTICO DETALHADO:');
    console.log(`📄 Título: ${diagnosis.title}`);
    console.log(`🌐 URL: ${diagnosis.url}`);
    console.log(`📋 Ready State: ${diagnosis.readyState}`);
    
    console.log('\n🏗️ ESTRUTURA DOM:');
    console.log(`   Body existe: ${diagnosis.hasBody}`);
    console.log(`   Body children: ${diagnosis.bodyChildren}`);
    console.log(`   Root existe: ${diagnosis.hasRoot}`);
    console.log(`   Root children: ${diagnosis.rootChildren}`);
    
    console.log('\n⚛️ REACT:');
    console.log(`   Elementos React detectados: ${diagnosis.hasReactElements}`);
    
    console.log('\n🎨 RECURSOS:');
    console.log(`   Stylesheets: ${diagnosis.hasStylesheets}`);
    console.log(`   Scripts: ${diagnosis.hasScripts}`);
    console.log(`   Script sources: ${diagnosis.scriptSources.join(', ')}`);
    
    console.log('\n📐 DIMENSÕES:');
    console.log(`   Viewport: ${diagnosis.viewportWidth}x${diagnosis.viewportHeight}`);
    console.log(`   Body: ${diagnosis.bodyWidth}x${diagnosis.bodyHeight}`);
    console.log(`   Root: ${diagnosis.rootWidth}x${diagnosis.rootHeight}`);
    
    console.log('\n📝 CONTEÚDO:');
    console.log(`   Body text: ${diagnosis.bodyText}`);
    
    if (diagnosis.rootHTML !== 'N/A') {
      console.log(`   Root HTML: ${diagnosis.rootHTML}...`);
    }

    // Verificar se é realmente uma tela em branco
    const isBlankScreen = diagnosis.bodyText.trim().length < 50 || 
                         diagnosis.rootChildren === 0 ||
                         !diagnosis.hasReactElements;

    console.log('\n🎯 RESULTADO:');
    if (isBlankScreen) {
      console.log('❌ TELA EM BRANCO CONFIRMADA!');
      console.log('   Possíveis causas:');
      
      if (diagnosis.rootChildren === 0) {
        console.log('   - Root element está vazio');
      }
      if (!diagnosis.hasReactElements) {
        console.log('   - React não está renderizando');
      }
      if (diagnosis.hasStylesheets === 0) {
        console.log('   - CSS não está carregando');
      }
      if (diagnosis.scriptSources.length === 0) {
        console.log('   - JavaScript não está carregando');
      }
    } else {
      console.log('✅ APLICAÇÃO FUNCIONANDO NORMALMENTE');
      console.log('   A tela não está em branco, conteúdo detectado.');
    }

    // Screenshot para análise visual
    await page.screenshot({ 
      path: 'blank-screen-diagnosis.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot salvo como blank-screen-diagnosis.png');

    // Aguardar para análise manual
    console.log('\n⏳ Aguardando 10 segundos para análise manual...');
    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro durante diagnóstico:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

diagnoseBlankScreen();
