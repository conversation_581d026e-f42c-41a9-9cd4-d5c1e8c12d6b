import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowLeft, Settings, Bookmark, MoreVertical, Sun, Moon, Type, Minus, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { BookLoader } from '@/lib/bookLoader';
import './reader.css';

interface PDFReaderProps {
  bookId: string;
  bookTitle: string;
  bookAuthor: string;
  content: string;
  onClose: () => void;
}

export function PDFReader({ bookId, bookTitle, bookAuthor, content, onClose }: PDFReaderProps) {
  const [showSettings, setShowSettings] = useState(false);
  const [fontSize, setFontSize] = useState(16); // 16pt conforme especificação
  const [theme, setTheme] = useState<'light' | 'dark' | 'sepia'>('light');
  const [progress, setProgress] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const [bookContent, setBookContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load book content
  useEffect(() => {
    const loadContent = async () => {
      console.log('PDFReader: Loading content for book ID:', bookId);
      setLoading(true);
      setError(null);
      
      try {
        const text = await BookLoader.getBookText(bookId);
        console.log('PDFReader: Content loaded, length:', text.length);
        setBookContent(text);
      } catch (error) {
        console.error('PDFReader: Error loading book content:', error);
        setError('Erro ao carregar o conteúdo do livro.');
        setBookContent('Erro ao carregar o conteúdo do livro. Tente novamente mais tarde.');
      } finally {
        setLoading(false);
      }
    };

    if (bookId) {
      loadContent();
    }
  }, [bookId]);

  // Hide controls after 3 seconds of inactivity
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowControls(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, [showControls]);

  // Calculate reading progress based on scroll
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollPercent = docHeight > 0 ? (scrollTop / docHeight) * 100 : 0;
      setProgress(Math.min(100, Math.max(0, scrollPercent)));
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const scrollAmount = window.innerHeight * 0.8; // 80% da altura da tela

      switch (event.key) {
        case 'ArrowDown':
        case 'PageDown':
        case ' ': // Spacebar
          event.preventDefault();
          window.scrollBy({ top: scrollAmount, behavior: 'smooth' });
          setShowControls(true);
          break;
        case 'ArrowUp':
        case 'PageUp':
          event.preventDefault();
          window.scrollBy({ top: -scrollAmount, behavior: 'smooth' });
          setShowControls(true);
          break;
        case 'Home':
          event.preventDefault();
          window.scrollTo({ top: 0, behavior: 'smooth' });
          setShowControls(true);
          break;
        case 'End':
          event.preventDefault();
          window.scrollTo({ top: document.documentElement.scrollHeight, behavior: 'smooth' });
          setShowControls(true);
          break;
        case 'Escape':
          onClose();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  const getThemeClasses = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gray-900'; // Apenas o fundo escuro
      case 'sepia':
        return 'bg-amber-50';
      default:
        return 'bg-white';
    }
  };

  const getSettingsTheme = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gray-800 border-gray-700 text-white';
      case 'sepia':
        return 'bg-amber-100 border-amber-200 text-amber-900';
      default:
        return 'bg-white border-gray-200 text-gray-900';
    }
  };

  const getTextColor = () => {
    switch (theme) {
      case 'dark':
        return '#f9fafb'; // Texto claro no modo escuro
      case 'sepia':
        return '#92400e';
      default:
        return '#111827';
    }
  };

  const getControlsTheme = () => {
    switch (theme) {
      case 'dark':
        return 'hover:bg-gray-800 text-gray-100';
      case 'sepia':
        return 'hover:bg-amber-100 text-amber-900';
      default:
        return 'hover:bg-gray-100 text-gray-900';
    }
  };

  const getProgressBarTheme = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gray-800 text-gray-100';
      case 'sepia':
        return 'bg-amber-100 text-amber-900';
      default:
        return 'bg-white text-gray-900';
    }
  };

  if (loading) {
    return (
      <div className={`fixed inset-0 z-50 ${getThemeClasses()} flex items-center justify-center`}>
        <div className="text-center">
          <div className={`w-12 h-12 border-4 rounded-full animate-spin mb-4 ${
            theme === 'dark' 
              ? 'border-gray-600 border-t-gray-300' 
              : 'border-gray-300 border-t-gray-600'
          }`}></div>
          <p className={`text-lg font-medium ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}>
            Carregando livro...
          </p>
          <p className={`text-sm mt-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}>
            {bookTitle}
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`fixed inset-0 z-50 ${getThemeClasses()} flex items-center justify-center`}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-red-600 text-2xl">⚠️</span>
          </div>
          <h3 className={`text-lg font-semibold mb-2 ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}>
            Erro ao Carregar
          </h3>
          <p className={`mb-4 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
            {error}
          </p>
          <div className="space-y-2">
            <Button onClick={onClose} variant="outline" className="w-full">
              Voltar
            </Button>
            <Button 
              onClick={() => window.location.reload()} 
              className="w-full bg-gray-900 text-white hover:bg-gray-800"
            >
              Tentar Novamente
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`fixed inset-0 z-50 ${getThemeClasses()} transition-colors duration-300 overflow-y-auto`}
      onMouseMove={() => setShowControls(true)}
      onWheel={() => setShowControls(true)}
      tabIndex={0} // Permite foco para capturar eventos de teclado
    >
      {/* Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-black/10 z-50">
        <motion.div
          className="h-full bg-gray-900"
          style={{ width: `${progress}%` }}
          transition={{ duration: 0.1 }}
        />
      </div>

      {/* Top Controls */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="fixed top-0 left-0 right-0 z-40 p-6"
          >
            <div className="flex items-center justify-between">
              <Button
                onClick={onClose}
                variant="ghost"
                className={`p-3 rounded-full ${getControlsTheme()} transition-colors`}
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>

              <div className="flex items-center space-x-2">
                {/* Zoom Controls */}
                <div className="flex items-center space-x-1 mr-2">
                  <Button
                    onClick={() => setFontSize(Math.max(12, fontSize - 1))}
                    variant="ghost"
                    size="sm"
                    className={`p-2 rounded-full ${getControlsTheme()} transition-colors`}
                    title="Diminuir fonte (Zoom Out)"
                  >
                    <Minus className="w-4 h-4" />
                  </Button>

                  <span className={`text-sm font-medium min-w-[2.5rem] text-center px-2 py-1 rounded ${
                    theme === 'dark' ? 'text-gray-100' : 'text-gray-900'
                  }`}>
                    {fontSize}pt
                  </span>

                  <Button
                    onClick={() => setFontSize(Math.min(28, fontSize + 1))}
                    variant="ghost"
                    size="sm"
                    className={`p-2 rounded-full ${getControlsTheme()} transition-colors`}
                    title="Aumentar fonte (Zoom In)"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>

                <Button
                  onClick={() => setShowSettings(!showSettings)}
                  variant="ghost"
                  className={`p-3 rounded-full ${getControlsTheme()} transition-colors`}
                >
                  <Settings className="w-5 h-5" />
                </Button>

                <Button
                  variant="ghost"
                  className={`p-3 rounded-full ${getControlsTheme()} transition-colors`}
                >
                  <Bookmark className="w-5 h-5" />
                </Button>

                <Button
                  variant="ghost"
                  className={`p-3 rounded-full ${getControlsTheme()} transition-colors`}
                >
                  <MoreVertical className="w-5 h-5" />
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Settings Panel */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className={`fixed top-0 right-0 h-full w-80 ${getSettingsTheme()} border-l shadow-2xl z-50 p-6 overflow-y-auto`}
          >
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Configurações de Leitura</h3>
                <Button
                  onClick={() => setShowSettings(false)}
                  variant="ghost"
                  size="sm"
                  className="p-2"
                >
                  ×
                </Button>
              </div>

              {/* Theme Selection */}
              <div>
                <label className="text-sm font-medium mb-3 block">Tema</label>
                <div className="grid grid-cols-3 gap-2">
                  <button
                    onClick={() => setTheme('light')}
                    className={`p-3 rounded-lg border-2 transition-all ${
                      theme === 'light' 
                        ? 'border-gray-900 bg-gray-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Sun className="w-5 h-5 mx-auto mb-1" />
                    <span className="text-xs">Claro</span>
                  </button>
                  <button
                    onClick={() => setTheme('dark')}
                    className={`p-3 rounded-lg border-2 transition-all ${
                      theme === 'dark' 
                        ? 'border-gray-400 bg-gray-700' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Moon className="w-5 h-5 mx-auto mb-1" />
                    <span className="text-xs">Escuro</span>
                  </button>
                  <button
                    onClick={() => setTheme('sepia')}
                    className={`p-3 rounded-lg border-2 transition-all ${
                      theme === 'sepia' 
                        ? 'border-amber-600 bg-amber-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="w-5 h-5 bg-amber-200 rounded mx-auto mb-1" />
                    <span className="text-xs">Sépia</span>
                  </button>
                </div>
              </div>

              {/* Font Size */}
              <div>
                <label className="text-sm font-medium mb-3 block">Tamanho da Fonte</label>
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={() => setFontSize(Math.max(12, fontSize - 1))}
                    variant="outline"
                    size="sm"
                    className="p-2"
                  >
                    <Minus className="w-4 h-4" />
                  </Button>
                  <span className="text-sm font-medium min-w-[3rem] text-center">
                    {fontSize}pt
                  </span>
                  <Button
                    onClick={() => setFontSize(Math.min(28, fontSize + 1))}
                    variant="outline"
                    size="sm"
                    className="p-2"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>


            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Content - Kindle-style formatting handled by CSS */}
      <div className="pt-20 pb-20 min-h-screen">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full"
        >
          <div
            className={`formatted-content ${theme}`}
            dangerouslySetInnerHTML={{
              __html: BookLoader.formatTextForReader(bookContent, theme)
            }}
            style={{
              // Only apply dynamic font size - all other styling handled by CSS
              fontSize: `${fontSize}pt`
            }}
          />
        </motion.div>
      </div>

      {/* Bottom Progress Info */}
      <AnimatePresence>
        {showControls && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40"
          >
            <div className={`px-4 py-2 rounded-full ${getProgressBarTheme()} shadow-lg border`}>
              <span className="text-sm font-medium">
                {Math.round(progress)}% concluído
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}