import React from 'react';
import { motion } from 'framer-motion';
import { Flame, Calendar, Trophy } from 'lucide-react';

interface ReadingStreakProps {
  streak: {
    current: number;
    longest: number;
    this_week: number;
  };
}

export function ReadingStreak({ streak }: ReadingStreakProps) {
  const daysOfWeek = ['D', 'S', 'T', 'Q', 'Q', 'S', 'S'];
  const currentDay = new Date().getDay();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="bg-white rounded-2xl border border-gray-100 p-6 h-fit"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-bold text-gray-900">Sequência de Leitura</h3>
        <div className="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
          <Flame className="w-5 h-5 text-orange-500" />
        </div>
      </div>

      {/* Current Streak */}
      <div className="text-center mb-6">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
          className="text-4xl font-bold text-gray-900 mb-2"
        >
          {streak.current}
        </motion.div>
        <p className="text-gray-600 text-sm">
          {streak.current === 1 ? 'dia consecutivo' : 'dias consecutivos'}
        </p>
      </div>

      {/* Week Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <span className="text-sm font-medium text-gray-700">Esta semana</span>
          <span className="text-sm text-gray-500">{streak.this_week}/7</span>
        </div>
        
        <div className="flex justify-between space-x-1">
          {daysOfWeek.map((day, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4 + index * 0.1 }}
              className={`w-8 h-8 rounded-lg flex items-center justify-center text-xs font-medium transition-all ${
                index < streak.this_week
                  ? 'bg-gray-900 text-white'
                  : index === currentDay
                  ? 'bg-gray-200 text-gray-600 ring-2 ring-gray-300'
                  : 'bg-gray-100 text-gray-400'
              }`}
            >
              {day}
            </motion.div>
          ))}
        </div>
      </div>

      {/* Stats */}
      <div className="space-y-3">
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Trophy className="w-4 h-4 text-yellow-600" />
            </div>
            <span className="text-sm font-medium text-gray-700">Melhor sequência</span>
          </div>
          <span className="text-sm font-bold text-gray-900">{streak.longest} dias</span>
        </div>

        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-4 h-4 text-blue-600" />
            </div>
            <span className="text-sm font-medium text-gray-700">Esta semana</span>
          </div>
          <span className="text-sm font-bold text-gray-900">{streak.this_week} dias</span>
        </div>
      </div>

      {/* Motivation */}
      <div className="mt-6 p-4 bg-gradient-to-r from-gray-900 to-gray-800 rounded-xl text-white text-center">
        <p className="text-sm font-medium mb-1">
          {streak.current >= 7 ? '🔥 Você está em chamas!' : '💪 Continue assim!'}
        </p>
        <p className="text-xs text-gray-300">
          {streak.current >= 7 
            ? 'Sequência incrível! Mantenha o ritmo.'
            : 'Leia hoje para manter sua sequência.'}
        </p>
      </div>
    </motion.div>
  );
}