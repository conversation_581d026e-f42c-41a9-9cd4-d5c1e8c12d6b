import puppeteer from 'puppeteer';

async function testStructureFixes() {
  let browser;
  try {
    console.log('🔧 TESTING PDF READER STRUCTURE AND DUPLICATION FIXES\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test the improved BookLoader with real content
    console.log('🧪 Testing improved BookLoader structure...');
    
    const bookLoaderTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${Date.now()}`);
        
        // Test with a book that has real content
        const bookText = await BookLoader.getBookText('3'); // Pai <PERSON>, <PERSON>i <PERSON>
        
        // Analyze the structure
        const lines = bookText.split('\n');
        const analysis = {
          totalLines: lines.length,
          emptyLines: lines.filter(line => line.trim() === '').length,
          chapterTitles: (bookText.match(/^# /gm) || []).length,
          sectionTitles: (bookText.match(/^## /gm) || []).length,
          subsectionTitles: (bookText.match(/^### /gm) || []).length,
          bodyParagraphs: lines.filter(line => line.trim().length > 50 && !line.startsWith('#')).length,
          textLength: bookText.length,
          hasAuthor: bookText.includes('**Por '),
          hasDescription: bookText.includes('Pai Rico') || bookText.includes('Robert'),
          preview: bookText.substring(0, 800)
        };
        
        // Check for duplication patterns
        const paragraphs = bookText.split('\n\n').filter(p => p.trim().length > 20);
        const uniqueParagraphs = new Set(paragraphs.map(p => p.toLowerCase().trim()));
        analysis.duplicationRate = ((paragraphs.length - uniqueParagraphs.size) / paragraphs.length * 100).toFixed(1);
        
        return {
          success: true,
          analysis,
          bookText: bookText.substring(0, 1500) // First 1500 chars for inspection
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (bookLoaderTest.success) {
      console.log('✅ BookLoader test completed successfully');
      
      const analysis = bookLoaderTest.analysis;
      console.log('\n📊 CONTENT STRUCTURE ANALYSIS:');
      console.log(`   Total lines: ${analysis.totalLines}`);
      console.log(`   Empty lines: ${analysis.emptyLines} (${(analysis.emptyLines/analysis.totalLines*100).toFixed(1)}%)`);
      console.log(`   Chapter titles (# ): ${analysis.chapterTitles}`);
      console.log(`   Section titles (## ): ${analysis.sectionTitles}`);
      console.log(`   Subsection titles (### ): ${analysis.subsectionTitles}`);
      console.log(`   Body paragraphs: ${analysis.bodyParagraphs}`);
      console.log(`   Total text length: ${analysis.textLength.toLocaleString()} characters`);
      console.log(`   Duplication rate: ${analysis.duplicationRate}%`);
      console.log(`   Has author info: ${analysis.hasAuthor ? 'YES' : 'NO'}`);
      console.log(`   Has description: ${analysis.hasDescription ? 'YES' : 'NO'}`);
      
      console.log('\n📋 Content preview:');
      console.log(analysis.preview + '...');
      
      // Evaluate structure quality
      const structureScore = {
        duplication: parseFloat(analysis.duplicationRate) < 5 ? 1 : 0,
        hierarchy: (analysis.chapterTitles + analysis.sectionTitles + analysis.subsectionTitles) > 0 ? 1 : 0,
        content: analysis.bodyParagraphs > 10 ? 1 : 0,
        metadata: (analysis.hasAuthor && analysis.hasDescription) ? 1 : 0
      };
      
      const totalScore = Object.values(structureScore).reduce((a, b) => a + b, 0);
      console.log(`\n🎯 Structure Quality Score: ${totalScore}/4`);
      
      Object.entries(structureScore).forEach(([key, value]) => {
        console.log(`   ${value ? '✅' : '❌'} ${key}: ${value ? 'GOOD' : 'NEEDS IMPROVEMENT'}`);
      });
      
    } else {
      console.log('❌ BookLoader test failed:', bookLoaderTest.error);
    }

    // Test CSS formatting
    console.log('\n🎨 Testing CSS text hierarchy...');
    
    const cssTest = await page.evaluate(() => {
      // Create test content with all elements
      const testContainer = document.createElement('div');
      testContainer.className = 'formatted-content light';
      testContainer.style.fontSize = '16pt';
      testContainer.innerHTML = `
        <div class="chapter-title">PAI RICO, PAI POBRE</div>
        <p class="body-text">Este é o primeiro parágrafo após o título.</p>
        <p class="body-text">Este é o segundo parágrafo com recuo normal.</p>
        <div class="section-title">LIÇÃO UM</div>
        <p class="body-text">Primeiro parágrafo após seção.</p>
        <div class="subsection-title">Os Ricos Não Trabalham Por Dinheiro</div>
        <p class="body-text">Parágrafo após subseção.</p>
        <div class="numbered-list">1. Primeiro ponto importante</div>
        <div class="numbered-list">2. Segundo ponto importante</div>
        <p class="body-text">Parágrafo final.</p>
      `;
      
      document.body.appendChild(testContainer);
      
      // Get computed styles for each element type
      const styles = {
        chapter: window.getComputedStyle(testContainer.querySelector('.chapter-title')),
        section: window.getComputedStyle(testContainer.querySelector('.section-title')),
        subsection: window.getComputedStyle(testContainer.querySelector('.subsection-title')),
        body: window.getComputedStyle(testContainer.querySelector('.body-text')),
        list: window.getComputedStyle(testContainer.querySelector('.numbered-list'))
      };
      
      const results = {
        chapterSize: styles.chapter.fontSize,
        chapterAlign: styles.chapter.textAlign,
        chapterWeight: styles.chapter.fontWeight,
        chapterMarginTop: styles.chapter.marginTop,
        chapterMarginBottom: styles.chapter.marginBottom,
        
        sectionSize: styles.section.fontSize,
        sectionAlign: styles.section.textAlign,
        sectionMarginTop: styles.section.marginTop,
        sectionMarginBottom: styles.section.marginBottom,
        
        subsectionSize: styles.subsection.fontSize,
        subsectionAlign: styles.subsection.textAlign,
        subsectionStyle: styles.subsection.fontStyle,
        
        bodyIndent: styles.body.textIndent,
        bodyAlign: styles.body.textAlign,
        bodyLineHeight: styles.body.lineHeight,
        
        listIndent: styles.list.textIndent,
        listPadding: styles.list.paddingLeft
      };
      
      // Clean up
      document.body.removeChild(testContainer);
      
      return results;
    });

    console.log('📐 CSS HIERARCHY VERIFICATION:');
    console.log(`   Chapter title size: ${cssTest.chapterSize} (should be larger)`);
    console.log(`   Section title size: ${cssTest.sectionSize} (should be medium)`);
    console.log(`   Subsection title size: ${cssTest.subsectionSize} (should be smaller)`);
    console.log(`   Chapter alignment: ${cssTest.chapterAlign} (should be center)`);
    console.log(`   Section alignment: ${cssTest.sectionAlign} (should be center)`);
    console.log(`   Subsection alignment: ${cssTest.subsectionAlign} (should be left)`);
    console.log(`   Body text indent: ${cssTest.bodyIndent} (should be 1cm)`);
    console.log(`   Body text alignment: ${cssTest.bodyAlign} (should be justify)`);
    console.log(`   Chapter margins: ${cssTest.chapterMarginTop} / ${cssTest.chapterMarginBottom}`);

    // Verify hierarchy compliance
    const hierarchyChecks = [
      { name: 'Chapter centered', check: cssTest.chapterAlign === 'center' },
      { name: 'Section centered', check: cssTest.sectionAlign === 'center' },
      { name: 'Subsection left-aligned', check: cssTest.subsectionAlign === 'left' },
      { name: 'Body text justified', check: cssTest.bodyAlign === 'justify' },
      { name: 'Body text indented', check: cssTest.bodyIndent.includes('cm') },
      { name: 'Proper font sizes', check: cssTest.chapterSize !== cssTest.sectionSize },
      { name: 'Subsection italic', check: cssTest.subsectionStyle === 'italic' }
    ];

    console.log('\n🎯 HIERARCHY COMPLIANCE:');
    let passedChecks = 0;
    hierarchyChecks.forEach(check => {
      const status = check.check ? '✅' : '❌';
      console.log(`${status} ${check.name}`);
      if (check.check) passedChecks++;
    });

    const complianceRate = (passedChecks / hierarchyChecks.length) * 100;
    console.log(`\n📈 Overall compliance: ${passedChecks}/${hierarchyChecks.length} (${complianceRate.toFixed(1)}%)`);

    // Final assessment
    console.log('\n🏆 FINAL ASSESSMENT:');
    
    if (bookLoaderTest.success) {
      const duplicationFixed = parseFloat(bookLoaderTest.analysis.duplicationRate) < 10;
      const structureImproved = bookLoaderTest.analysis.sectionTitles + bookLoaderTest.analysis.subsectionTitles > 0;
      const hierarchyWorking = complianceRate >= 80;
      
      console.log(`${duplicationFixed ? '✅' : '❌'} Duplication issue: ${duplicationFixed ? 'FIXED' : 'STILL PRESENT'}`);
      console.log(`${structureImproved ? '✅' : '❌'} Text structure: ${structureImproved ? 'IMPROVED' : 'NEEDS WORK'}`);
      console.log(`${hierarchyWorking ? '✅' : '❌'} Visual hierarchy: ${hierarchyWorking ? 'WORKING' : 'NEEDS FIXES'}`);
      
      if (duplicationFixed && structureImproved && hierarchyWorking) {
        console.log('\n🎉 ALL ISSUES SUCCESSFULLY FIXED!');
        console.log('✅ No more duplicate content on scroll');
        console.log('✅ Proper text hierarchy implemented');
        console.log('✅ Beautiful, readable formatting achieved');
      } else {
        console.log('\n⚠️ Some issues may still need attention');
      }
    }

    console.log('\n🚀 MANUAL TESTING INSTRUCTIONS:');
    console.log('1. Open a book in the PDF reader');
    console.log('2. Scroll down with mouse wheel - verify no duplicate content');
    console.log('3. Check text hierarchy:');
    console.log('   • Book title: Large, centered');
    console.log('   • Chapter titles: Medium, centered, uppercase');
    console.log('   • Subsections: Smaller, left-aligned, italic');
    console.log('   • Body text: Justified, first-line indent');
    console.log('4. Verify visual separation between elements');
    console.log('5. Test different themes for proper contrast');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Error during test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testStructureFixes();
