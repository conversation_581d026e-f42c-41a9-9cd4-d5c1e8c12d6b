import { processCompletePDF } from './extract-complete-pdf-content.js';

async function testSingleCompletePDF() {
  console.log('🧪 TESTE: Processamento completo de um PDF específico\n');
  
  // Testar com o PDF do Freud (que sabemos que existe)
  const testFile = 'a_interpretacao_dos_sonhos_sigmund_freud_3.pdf';
  
  console.log(`📖 Testando: ${testFile}`);
  console.log('🎯 Objetivo: Extrair TODO o conteúdo (todas as páginas)\n');
  
  try {
    const success = await processCompletePDF(testFile);
    
    if (success) {
      console.log('\n🎉 TESTE CONCLUÍDO COM SUCESSO!');
      console.log('✅ PDF processado completamente');
      console.log('📄 Todo o conteúdo foi extraído e salvo');
      console.log('\n🔍 Agora você pode testar no navegador:');
      console.log('   1. Abra a aplicação');
      console.log('   2. Faça login');
      console.log('   3. Procure por "A Interpretação dos Sonhos"');
      console.log('   4. Clique em "Ler Agora"');
      console.log('   5. Verifique se TODO o conteúdo está disponível');
    } else {
      console.log('\n❌ TESTE FALHOU');
      console.log('Verifique os logs acima para identificar o problema');
    }
    
  } catch (error) {
    console.error('\n💥 ERRO NO TESTE:', error.message);
  }
}

testSingleCompletePDF();
