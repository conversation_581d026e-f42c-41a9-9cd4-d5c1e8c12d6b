import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function findOriginalMLKContent() {
  try {
    console.log('🔍 FINDING ORIGINAL MLK PDF CONTENT\n');

    // The user mentioned the original content was "Conteúdo extraído do PDF (125.765 caracteres)"
    // Let's check if there are other MLK books that might have the original content

    console.log('1. Searching for all MLK-related books...');
    
    const { data: allMLKBooks, error: searchError } = await supabase
      .from('books')
      .select('*')
      .or('title.ilike.%apelo%,title.ilike.%martin%luther%king%,author.ilike.%martin%luther%king%');

    if (searchError) {
      console.log('❌ Error searching books:', searchError.message);
      return;
    }

    console.log(`📚 Found ${allMLKBooks.length} MLK-related books:`);
    allMLKBooks.forEach(book => {
      console.log(`   ID: ${book.id} - "${book.title}" by ${book.author}`);
      console.log(`       Description: "${book.description?.substring(0, 100)}..."`);
    });

    // Check content for each book
    console.log('\n2. Checking content for each book...');
    
    for (const book of allMLKBooks) {
      console.log(`\n📖 Book ID ${book.id}: "${book.title}"`);
      
      const { data: content, error: contentError } = await supabase
        .from('book_contents')
        .select('*')
        .eq('book_id', book.id);

      if (contentError) {
        console.log(`   ❌ Error fetching content: ${contentError.message}`);
        continue;
      }

      if (content.length === 0) {
        console.log('   📄 No content found');
        continue;
      }

      content.forEach((entry, index) => {
        console.log(`   📋 Content Entry ${index + 1}:`);
        
        if (typeof entry.content === 'string') {
          console.log(`       Type: String`);
          console.log(`       Length: ${entry.content.length} characters`);
          console.log(`       Preview: "${entry.content.substring(0, 200)}..."`);
          
          // Check if this looks like original PDF content
          const looksLikeOriginalPDF = 
            entry.content.length > 100000 || // Large content
            entry.content.includes('1955') || // Historical content
            entry.content.includes('segregação') || // Portuguese content
            entry.content.includes('direitos civis'); // Civil rights content
          
          console.log(`       Looks like original PDF: ${looksLikeOriginalPDF ? 'YES' : 'NO'}`);
          
        } else if (typeof entry.content === 'object') {
          console.log(`       Type: Object`);
          console.log(`       Structure: ${Object.keys(entry.content).join(', ')}`);
          
          if (entry.content.chapters) {
            console.log(`       Chapters: ${entry.content.chapters.length}`);
            console.log(`       Total chars: ${entry.content.total_characters || 'unknown'}`);
            
            // Check if chapters contain original content
            const firstChapter = entry.content.chapters[0];
            if (firstChapter && firstChapter.content) {
              console.log(`       First chapter preview: "${firstChapter.content.substring(0, 200)}..."`);
              
              const hasOriginalContent = 
                firstChapter.content.includes('1955') ||
                firstChapter.content.includes('segregação') ||
                firstChapter.content.includes('direitos civis') ||
                firstChapter.content.length > 5000;
              
              console.log(`       Has original content: ${hasOriginalContent ? 'YES' : 'NO'}`);
            }
          }
        }
      });
    }

    // Look for the book that was mentioned with 125,765 characters
    console.log('\n3. Looking for book with ~125,765 characters...');
    
    const targetBook = allMLKBooks.find(book => 
      book.description && book.description.includes('125.765 caracteres')
    );

    if (targetBook) {
      console.log(`✅ Found target book: ID ${targetBook.id} - "${targetBook.title}"`);
      
      // Get its content
      const { data: targetContent, error: targetError } = await supabase
        .from('book_contents')
        .select('*')
        .eq('book_id', targetBook.id);

      if (targetError) {
        console.log('❌ Error fetching target content:', targetError.message);
      } else if (targetContent.length > 0) {
        console.log('📋 Target book content found:');
        const content = targetContent[0].content;
        
        if (typeof content === 'string') {
          console.log(`   Length: ${content.length} characters`);
          console.log(`   Preview: "${content.substring(0, 500)}..."`);
        } else if (typeof content === 'object' && content.chapters) {
          console.log(`   Chapters: ${content.chapters.length}`);
          const allText = content.chapters.map(ch => ch.content).join(' ');
          console.log(`   Total text length: ${allText.length} characters`);
          console.log(`   Preview: "${allText.substring(0, 500)}..."`);
        }
      }
    } else {
      console.log('❌ Target book with 125,765 characters not found');
    }

    console.log('\n💡 RECOMMENDATIONS:');
    console.log('1. If original PDF content exists in another book, copy it to book ID 85');
    console.log('2. If original content was lost, we need to restore it from backup');
    console.log('3. Apply Kindle-style formatting to the original content');
    console.log('4. Ensure users see the actual PDF summary, not our structured content');

  } catch (error) {
    console.error('💥 Error during search:', error);
  }
}

findOriginalMLKContent();
