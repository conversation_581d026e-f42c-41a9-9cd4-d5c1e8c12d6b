import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkCurrentBooks() {
  try {
    console.log('📚 CHECKING CURRENT BOOKS IN DATABASE\n');

    // Get all books
    const { data: books, error: booksError } = await supabase
      .from('books')
      .select('*')
      .order('id');

    if (booksError) {
      console.log('❌ Error fetching books:', booksError.message);
      return;
    }

    console.log(`📖 Found ${books.length} books in database:`);
    console.log('');

    books.forEach(book => {
      console.log(`📚 ID: ${book.id}`);
      console.log(`   Title: "${book.title}"`);
      console.log(`   Author: "${book.author}"`);
      console.log(`   Description: "${book.description}"`);
      console.log(`   Created: ${book.created_at}`);
      console.log('');
    });

    // Check specifically for MLK book
    const mlkBooks = books.filter(book => 
      book.title.toLowerCase().includes('apelo') ||
      book.title.toLowerCase().includes('martin') ||
      book.title.toLowerCase().includes('king') ||
      book.author.toLowerCase().includes('martin')
    );

    if (mlkBooks.length > 0) {
      console.log('🎯 MLK-RELATED BOOKS FOUND:');
      mlkBooks.forEach(book => {
        console.log(`   ID: ${book.id} - "${book.title}" by ${book.author}`);
      });
    } else {
      console.log('❌ No MLK-related books found');
    }

    // Check content for book ID 85
    console.log('\n📋 CHECKING CONTENT FOR BOOK ID 85:');
    
    const { data: content, error: contentError } = await supabase
      .from('book_contents')
      .select('*')
      .eq('book_id', 85);

    if (contentError) {
      console.log('❌ Error fetching content:', contentError.message);
    } else if (content.length === 0) {
      console.log('❌ No content found for book ID 85');
    } else {
      console.log(`✅ Content found for book ID 85:`);
      content.forEach((entry, index) => {
        console.log(`   Entry ${index + 1}:`);
        if (typeof entry.content === 'string') {
          console.log(`     Type: String`);
          console.log(`     Length: ${entry.content.length} characters`);
          console.log(`     Preview: "${entry.content.substring(0, 100)}..."`);
        } else if (typeof entry.content === 'object') {
          console.log(`     Type: Object`);
          if (entry.content.chapters) {
            console.log(`     Chapters: ${entry.content.chapters.length}`);
            const firstChapter = entry.content.chapters[0];
            if (firstChapter) {
              console.log(`     First chapter title: "${firstChapter.title}"`);
              console.log(`     First chapter content length: ${firstChapter.content?.length || 0} chars`);
              console.log(`     First chapter preview: "${firstChapter.content?.substring(0, 100) || 'No content'}..."`);
            }
          }
        }
      });
    }

    console.log('\n💡 RECOMMENDATIONS:');
    if (mlkBooks.length === 0) {
      console.log('1. Create or restore the MLK book in the database');
      console.log('2. Ensure the book has the correct title and author');
    }
    if (content.length === 0) {
      console.log('3. Add content to book ID 85');
    }
    console.log('4. Test the application to ensure books are displaying correctly');

  } catch (error) {
    console.error('💥 Error checking books:', error.message);
  }
}

checkCurrentBooks();
