import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

// Simular o BookLoader
class TestBookLoader {
  static async loadBook(bookId) {
    console.log('🔍 TestBookLoader: Carregando livro ID:', bookId);

    try {
      // Primeira consulta: dados do livro
      console.log('   1. Buscando dados do livro...');
      const { data: bookData, error: bookError } = await supabase
        .from('books')
        .select('*')
        .eq('id', bookId)
        .single();

      if (bookError || !bookData) {
        console.error('   ❌ Erro ao carregar dados do livro:', bookError);
        return null;
      }

      console.log(`   ✅ Dados do livro carregados: "${bookData.title}"`);

      // Segunda consulta: conteúdo do livro
      console.log('   2. Buscando conteúdo do livro...');
      const { data: contentData, error: contentError } = await supabase
        .from('book_contents')
        .select('content')
        .eq('book_id', bookId)
        .single();

      if (contentError || !contentData) {
        console.warn('   ⚠️ Nenhum conteúdo encontrado no banco:', contentError);
        return this.createFallbackContent(bookData);
      }

      console.log('   ✅ Conteúdo carregado do banco de dados');
      
      // Combinar dados do livro com conteúdo
      const bookContent = {
        id: String(bookData.id),
        title: bookData.title,
        author: bookData.author || 'Autor Desconhecido',
        category: bookData.category || 'Geral',
        pages: bookData.pages || 300,
        duration: bookData.duration || 20,
        difficulty: bookData.difficulty || 'Intermediário',
        description: bookData.description || 'Descrição não disponível',
        cover_image_url: bookData.cover_image_url,
        content: contentData.content
      };

      console.log('   ✅ Livro combinado com sucesso');
      return bookContent;

    } catch (error) {
      console.error('💥 Erro durante carregamento:', error.message);
      return null;
    }
  }

  static createFallbackContent(bookData) {
    console.log('   📝 Criando conteúdo fallback...');
    return {
      id: String(bookData.id),
      title: bookData.title,
      author: bookData.author || 'Autor Desconhecido',
      category: bookData.category || 'Geral',
      pages: 300,
      duration: 20,
      difficulty: 'Intermediário',
      description: bookData.description || 'Descrição não disponível',
      content: {
        chapters: [
          {
            id: 'chapter1',
            title: 'Introdução',
            content: `Este é um resumo de exemplo para "${bookData.title}". O conteúdo completo estará disponível em breve.`
          }
        ],
        key_points: [
          'Este é um resumo de exemplo',
          'O conteúdo completo estará disponível em breve',
          'Continue explorando nossa biblioteca'
        ]
      }
    };
  }

  static async getBookText(bookId) {
    console.log('📖 TestBookLoader: Obtendo texto do livro ID:', bookId);
    
    const book = await this.loadBook(bookId);
    if (!book) {
      console.error('❌ Livro não encontrado');
      return 'Erro ao carregar o conteúdo do livro.';
    }

    console.log('   ✅ Livro carregado, processando texto...');
    
    let text = '';

    // Adicionar cabeçalho
    text = `# ${book.title}\n\n`;
    
    if (book.author && book.author !== 'Autor Desconhecido') {
      text += `**Por ${book.author}**\n\n`;
    }

    if (book.description && book.description.length > 50) {
      text += `${book.description}\n\n`;
    }

    // Adicionar conteúdo dos capítulos
    if (book.content && book.content.chapters) {
      console.log(`   📚 Processando ${book.content.chapters.length} capítulo(s)...`);
      
      book.content.chapters.forEach((chapter, index) => {
        if (chapter.content && chapter.content.trim().length > 0) {
          text += `## ${chapter.title}\n\n`;
          text += `${chapter.content}\n\n`;
        }
      });
    }

    // Adicionar pontos-chave se disponíveis
    if (book.content && book.content.key_points && book.content.key_points.length > 0) {
      text += `## Pontos-Chave\n\n`;
      book.content.key_points.forEach(point => {
        text += `• ${point}\n`;
      });
      text += '\n';
    }

    console.log(`   ✅ Texto processado: ${text.length} caracteres`);
    return text;
  }
}

async function testBookLoaderMLK() {
  try {
    console.log('🧪 TESTANDO BOOKLOADER COM LIVRO DO MLK\n');

    const bookId = '85';

    console.log('1. Testando loadBook()...');
    const book = await TestBookLoader.loadBook(bookId);

    if (!book) {
      console.log('❌ Falha ao carregar livro');
      return;
    }

    console.log('\n📊 RESULTADO DO LOADBOOK:');
    console.log(`   📚 Título: "${book.title}"`);
    console.log(`   ✍️ Autor: "${book.author}"`);
    console.log(`   📂 Categoria: "${book.category}"`);
    console.log(`   ⏱️ Duração: ${book.duration} min`);
    console.log(`   📝 Descrição: "${book.description.substring(0, 100)}..."`);

    // Verificar estrutura do conteúdo
    console.log('\n📖 ESTRUTURA DO CONTEÚDO:');
    console.log(`   Tipo: ${typeof book.content}`);
    console.log(`   Tem chapters: ${!!(book.content && book.content.chapters)}`);
    
    if (book.content && book.content.chapters) {
      console.log(`   Número de capítulos: ${book.content.chapters.length}`);
      
      if (book.content.chapters.length > 0) {
        const firstChapter = book.content.chapters[0];
        console.log(`   Primeiro capítulo: "${firstChapter.title}"`);
        console.log(`   Tamanho do conteúdo: ${firstChapter.content?.length || 0} caracteres`);
        
        // Verificar se é conteúdo do MLK
        if (firstChapter.content) {
          const content = firstChapter.content;
          const hasMLKContent = 
            content.includes('Martin Luther King') ||
            content.includes('direitos civis') ||
            content.includes('I Have a Dream') ||
            content.includes('Birmingham') ||
            content.includes('Montgomery');
          
          console.log(`   Conteúdo do MLK detectado: ${hasMLKContent ? '✅ SIM' : '❌ NÃO'}`);
          
          if (hasMLKContent) {
            console.log('\n📋 PREVIEW DO CONTEÚDO MLK:');
            console.log(`"${content.substring(0, 300)}..."`);
          }
        }
      }
    }

    // Verificar pontos-chave
    if (book.content && book.content.key_points) {
      console.log(`   Pontos-chave: ${book.content.key_points.length}`);
    }

    console.log('\n2. Testando getBookText()...');
    const bookText = await TestBookLoader.getBookText(bookId);
    
    console.log('\n📄 RESULTADO DO GETBOOKTEXT:');
    console.log(`   Tamanho total: ${bookText.length.toLocaleString()} caracteres`);
    console.log(`   Palavras estimadas: ${Math.round(bookText.length / 5).toLocaleString()}`);
    
    // Verificar se o texto contém conteúdo do MLK
    const mlkKeywords = [
      'Martin Luther King',
      'direitos civis',
      'I Have a Dream',
      'Birmingham',
      'Montgomery',
      'não-violência',
      'segregação',
      'boicote',
      'marcha',
      'Washington'
    ];

    const foundKeywords = mlkKeywords.filter(keyword => 
      bookText.toLowerCase().includes(keyword.toLowerCase())
    );

    console.log(`   Palavras-chave MLK encontradas: ${foundKeywords.length}/${mlkKeywords.length}`);
    if (foundKeywords.length > 0) {
      console.log(`   Palavras encontradas: ${foundKeywords.join(', ')}`);
    }

    console.log('\n📋 PREVIEW DO TEXTO FINAL:');
    console.log(`"${bookText.substring(0, 500)}..."`);

    // Verificar se o texto está adequado para exibição
    const isReadable = bookText.length > 1000 && 
                      !bookText.includes('Erro ao carregar') &&
                      foundKeywords.length >= 3;

    console.log('\n🎯 AVALIAÇÃO FINAL:');
    console.log(`   ${book ? '✅' : '❌'} Livro carregado`);
    console.log(`   ${book.content && book.content.chapters ? '✅' : '❌'} Estrutura de capítulos válida`);
    console.log(`   ${foundKeywords.length >= 3 ? '✅' : '❌'} Conteúdo autêntico do MLK`);
    console.log(`   ${bookText.length > 1000 ? '✅' : '❌'} Texto adequado para leitura`);
    console.log(`   ${isReadable ? '✅' : '❌'} Pronto para exibição`);

    const overallSuccess = book && book.content && book.content.chapters && foundKeywords.length >= 3 && bookText.length > 1000;
    
    console.log(`\n🏆 STATUS GERAL: ${overallSuccess ? '✅ SUCESSO COMPLETO' : '⚠️ PRECISA ATENÇÃO'}`);

    if (overallSuccess) {
      console.log('\n🎉 EXCELENTE! BOOKLOADER ESTÁ FUNCIONANDO PERFEITAMENTE:');
      console.log('   ✓ Carrega dados do livro corretamente');
      console.log('   ✓ Carrega conteúdo autêntico do MLK');
      console.log('   ✓ Processa texto para exibição');
      console.log('   ✓ Pronto para uso na aplicação');
    } else {
      console.log('\n🔧 ÁREAS QUE PRECISAM DE ATENÇÃO:');
      if (!book) console.log('   • Carregamento do livro falhou');
      if (!book?.content?.chapters) console.log('   • Estrutura de capítulos inválida');
      if (foundKeywords.length < 3) console.log('   • Conteúdo do MLK não detectado adequadamente');
      if (bookText.length <= 1000) console.log('   • Texto muito curto para leitura');
    }

  } catch (error) {
    console.error('💥 Erro durante teste:', error.message);
  }
}

testBookLoaderMLK();
