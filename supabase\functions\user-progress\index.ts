import { createClient } from 'npm:@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
}

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const authHeader = req.headers.get('Authorization')
    const token = authHeader?.replace('Bearer ', '')
    
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser(token)
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Authentication required' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const url = new URL(req.url)
    const pathSegments = url.pathname.split('/').filter(Boolean)
    const apiPath = pathSegments.slice(3).join('/')

    switch (req.method) {
      case 'GET':
        return await handleGetProgress(supabaseClient, user.id, apiPath, url.searchParams)
      case 'POST':
        return await handleUpdateProgress(supabaseClient, user.id, req)
      case 'PUT':
        return await handleToggleFavorite(supabaseClient, user.id, apiPath, req)
      default:
        return new Response(
          JSON.stringify({ error: 'Method not allowed' }),
          { status: 405, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
    }
  } catch (error) {
    console.error('Progress API Error:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

async function handleGetProgress(supabaseClient: any, userId: string, path: string, searchParams: URLSearchParams) {
  if (path === '' || path === 'dashboard') {
    // GET /user-progress/dashboard - Get user dashboard data
    const { data: progress, error } = await supabaseClient
      .from('user_reading_progress')
      .select(`
        *,
        summaries (
          id,
          title,
          estimated_reading_time,
          books (
            id,
            title,
            author,
            cover_image_url,
            categories (
              name,
              slug
            )
          )
        )
      `)
      .eq('user_id', userId)
      .order('last_read_at', { ascending: false })

    if (error) {
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Separate into different categories
    const recentlyRead = progress.filter((p: any) => p.last_read_at).slice(0, 5)
    const favorites = progress.filter((p: any) => p.is_favorited)
    const completed = progress.filter((p: any) => p.is_completed)
    const inProgress = progress.filter((p: any) => p.progress_percentage > 0 && !p.is_completed)

    // Get subscription info
    const { data: subscription } = await supabaseClient
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single()

    // Calculate stats
    const stats = {
      total_summaries_read: completed.length,
      total_reading_time: completed.reduce((acc: number, p: any) => 
        acc + (p.summaries?.estimated_reading_time || 0), 0),
      favorites_count: favorites.length,
      in_progress_count: inProgress.length
    }

    return new Response(
      JSON.stringify({
        data: {
          recently_read: recentlyRead,
          favorites: favorites,
          completed: completed,
          in_progress: inProgress,
          subscription: subscription,
          stats: stats
        }
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  if (path.startsWith('summary/')) {
    // GET /user-progress/summary/:id - Get progress for specific summary
    const summaryId = path.split('/')[1]
    
    const { data: progress, error } = await supabaseClient
      .from('user_reading_progress')
      .select('*')
      .eq('user_id', userId)
      .eq('summary_id', summaryId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      return new Response(
        JSON.stringify({ error: error.message }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    return new Response(
      JSON.stringify({ data: progress || null }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ error: 'Invalid endpoint' }),
    { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleUpdateProgress(supabaseClient: any, userId: string, req: Request) {
  const body = await req.json()
  const { summary_id, progress_percentage } = body

  if (!summary_id || progress_percentage === undefined) {
    return new Response(
      JSON.stringify({ error: 'summary_id and progress_percentage are required' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  const isCompleted = progress_percentage >= 100

  const { data: progress, error } = await supabaseClient
    .from('user_reading_progress')
    .upsert({
      user_id: userId,
      summary_id: summary_id,
      progress_percentage: Math.min(100, Math.max(0, progress_percentage)),
      is_completed: isCompleted,
      last_read_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ data: progress }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

async function handleToggleFavorite(supabaseClient: any, userId: string, path: string, req: Request) {
  if (!path.startsWith('favorite/')) {
    return new Response(
      JSON.stringify({ error: 'Invalid endpoint' }),
      { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  const summaryId = path.split('/')[1]
  const body = await req.json()
  const { is_favorited } = body

  if (is_favorited === undefined) {
    return new Response(
      JSON.stringify({ error: 'is_favorited is required' }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  const { data: progress, error } = await supabaseClient
    .from('user_reading_progress')
    .upsert({
      user_id: userId,
      summary_id: summaryId,
      is_favorited: is_favorited,
      updated_at: new Date().toISOString()
    })
    .select()
    .single()

  if (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }

  return new Response(
    JSON.stringify({ data: progress }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}