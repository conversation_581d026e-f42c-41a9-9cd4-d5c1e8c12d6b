import puppeteer from 'puppeteer';

async function testFontSizeControls() {
  let browser;
  try {
    console.log('🔧 TESTING FONT SIZE CONTROLS\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('📚 Opening MLK book...');
    
    // Try to find and click on the MLK book
    const mlkBookFound = await page.evaluate(() => {
      const bookElements = document.querySelectorAll('[data-testid="book-card"], .book-card, .book-item, .grid > div');
      for (let element of bookElements) {
        const text = element.textContent || '';
        if (text.includes('Um Apelo à Consciência') || text.includes('<PERSON>')) {
          element.click();
          return true;
        }
      }
      return false;
    });

    if (!mlkBookFound) {
      console.log('❌ MLK book not found');
      return;
    }

    console.log('✅ MLK book clicked, waiting for reader to load...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Test font size controls
    console.log('🔧 Testing font size controls...');
    
    const fontSizeTest = await page.evaluate(() => {
      // Check if reader is loaded
      const reader = document.querySelector('.formatted-content');
      if (!reader) {
        return { success: false, error: 'Reader not loaded' };
      }

      // Get initial font size
      const initialStyle = window.getComputedStyle(reader);
      const initialFontSize = initialStyle.fontSize;

      // Find font size controls
      const plusButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
        btn.textContent?.includes('+') || 
        btn.querySelector('svg') || 
        btn.title?.includes('Aumentar') ||
        btn.title?.includes('Zoom In')
      );

      const minusButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
        btn.textContent?.includes('-') || 
        btn.querySelector('svg') || 
        btn.title?.includes('Diminuir') ||
        btn.title?.includes('Zoom Out')
      );

      // Check if font size display exists
      const fontSizeDisplays = Array.from(document.querySelectorAll('span')).filter(span => 
        span.textContent?.includes('pt')
      );

      return {
        success: true,
        initialFontSize,
        plusButtonsFound: plusButtons.length,
        minusButtonsFound: minusButtons.length,
        fontSizeDisplaysFound: fontSizeDisplays.length,
        readerExists: !!reader,
        readerClasses: reader.className,
        readerStyle: reader.style.fontSize || 'not set'
      };
    });

    if (!fontSizeTest.success) {
      console.log('❌ Font size test failed:', fontSizeTest.error);
      return;
    }

    console.log('📊 FONT SIZE CONTROLS ANALYSIS:');
    console.log(`   Reader exists: ${fontSizeTest.readerExists}`);
    console.log(`   Reader classes: "${fontSizeTest.readerClasses}"`);
    console.log(`   Reader inline style: "${fontSizeTest.readerStyle}"`);
    console.log(`   Initial computed font size: ${fontSizeTest.initialFontSize}`);
    console.log(`   Plus buttons found: ${fontSizeTest.plusButtonsFound}`);
    console.log(`   Minus buttons found: ${fontSizeTest.minusButtonsFound}`);
    console.log(`   Font size displays found: ${fontSizeTest.fontSizeDisplaysFound}`);

    // Try to click font size controls
    console.log('\n🔧 Testing font size increase...');
    
    const increaseTest = await page.evaluate(() => {
      // Find and click the increase button
      const increaseButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
        btn.title?.includes('Aumentar') || btn.title?.includes('Zoom In')
      );

      if (increaseButtons.length === 0) {
        return { success: false, error: 'No increase button found' };
      }

      const button = increaseButtons[0];
      const beforeClick = document.querySelector('.formatted-content')?.style.fontSize || 'not set';
      
      button.click();
      
      // Wait a moment for state update
      setTimeout(() => {}, 100);
      
      const afterClick = document.querySelector('.formatted-content')?.style.fontSize || 'not set';
      
      return {
        success: true,
        beforeClick,
        afterClick,
        buttonClicked: button.title || button.textContent
      };
    });

    console.log('📈 FONT SIZE INCREASE TEST:');
    console.log(`   Button clicked: "${increaseTest.buttonClicked}"`);
    console.log(`   Font size before: ${increaseTest.beforeClick}`);
    console.log(`   Font size after: ${increaseTest.afterClick}`);
    console.log(`   Working: ${increaseTest.beforeClick !== increaseTest.afterClick ? 'YES' : 'NO'}`);

    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test decrease
    console.log('\n🔧 Testing font size decrease...');
    
    const decreaseTest = await page.evaluate(() => {
      const decreaseButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
        btn.title?.includes('Diminuir') || btn.title?.includes('Zoom Out')
      );

      if (decreaseButtons.length === 0) {
        return { success: false, error: 'No decrease button found' };
      }

      const button = decreaseButtons[0];
      const beforeClick = document.querySelector('.formatted-content')?.style.fontSize || 'not set';
      
      button.click();
      
      setTimeout(() => {}, 100);
      
      const afterClick = document.querySelector('.formatted-content')?.style.fontSize || 'not set';
      
      return {
        success: true,
        beforeClick,
        afterClick,
        buttonClicked: button.title || button.textContent
      };
    });

    console.log('📉 FONT SIZE DECREASE TEST:');
    console.log(`   Button clicked: "${decreaseTest.buttonClicked}"`);
    console.log(`   Font size before: ${decreaseTest.beforeClick}`);
    console.log(`   Font size after: ${decreaseTest.afterClick}`);
    console.log(`   Working: ${decreaseTest.beforeClick !== decreaseTest.afterClick ? 'YES' : 'NO'}`);

    // Final assessment
    const fontControlsWorking = 
      (increaseTest.beforeClick !== increaseTest.afterClick) ||
      (decreaseTest.beforeClick !== decreaseTest.afterClick);

    console.log(`\n🎯 FONT SIZE CONTROLS STATUS: ${fontControlsWorking ? '✅ WORKING' : '❌ NOT WORKING'}`);

    if (!fontControlsWorking) {
      console.log('\n🔧 DEBUGGING INFO:');
      console.log('   The font size controls may not be working due to:');
      console.log('   1. CSS specificity issues overriding inline styles');
      console.log('   2. React state not updating properly');
      console.log('   3. Event handlers not attached correctly');
      console.log('   4. CSS classes preventing font size changes');
    }

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Error during test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testFontSizeControls();
