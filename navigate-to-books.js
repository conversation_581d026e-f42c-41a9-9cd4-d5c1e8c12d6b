import puppeteer from 'puppeteer';

async function navigateToBooks() {
  let browser;
  try {
    console.log('🔍 NAVIGATING TO BOOKS SECTION\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('🔍 Analyzing current page...');
    
    const pageAnalysis = await page.evaluate(() => {
      const url = window.location.href;
      const title = document.title;
      const bodyText = document.body.textContent.substring(0, 500);
      
      // Look for navigation elements
      const navElements = Array.from(document.querySelectorAll('nav, .nav, [role="navigation"]'));
      const navTexts = navElements.map(el => el.textContent);
      
      // Look for links that might lead to books
      const links = Array.from(document.querySelectorAll('a, button'));
      const bookLinks = links.filter(link => {
        const text = link.textContent?.toLowerCase() || '';
        return text.includes('livro') || text.includes('book') || text.includes('biblioteca') || 
               text.includes('acervo') || text.includes('resumo') || text.includes('começar');
      }).map(link => ({
        text: link.textContent,
        href: link.href || 'button',
        tag: link.tagName
      }));
      
      return {
        url,
        title,
        bodyText,
        navTexts,
        bookLinks
      };
    });

    console.log('📊 PAGE ANALYSIS:');
    console.log(`   URL: ${pageAnalysis.url}`);
    console.log(`   Title: ${pageAnalysis.title}`);
    console.log(`   Navigation elements: ${pageAnalysis.navTexts.length}`);
    console.log(`   Book-related links: ${pageAnalysis.bookLinks.length}`);
    
    console.log('\n📋 BODY TEXT PREVIEW:');
    console.log(`"${pageAnalysis.bodyText}..."`);
    
    if (pageAnalysis.navTexts.length > 0) {
      console.log('\n🧭 NAVIGATION CONTENT:');
      pageAnalysis.navTexts.forEach((nav, index) => {
        console.log(`   ${index + 1}. "${nav}"`);
      });
    }
    
    if (pageAnalysis.bookLinks.length > 0) {
      console.log('\n🔗 BOOK-RELATED LINKS:');
      pageAnalysis.bookLinks.forEach((link, index) => {
        console.log(`   ${index + 1}. ${link.tag}: "${link.text}" -> ${link.href}`);
      });
      
      // Try to click the first book-related link
      console.log('\n🖱️ Attempting to navigate to books...');
      
      const navigationResult = await page.evaluate(() => {
        const links = Array.from(document.querySelectorAll('a, button'));
        const bookLink = links.find(link => {
          const text = link.textContent?.toLowerCase() || '';
          return text.includes('começar') || text.includes('livro') || text.includes('acesso') || 
                 text.includes('biblioteca') || text.includes('resumo');
        });
        
        if (bookLink) {
          bookLink.click();
          return {
            success: true,
            clickedText: bookLink.textContent,
            clickedTag: bookLink.tagName
          };
        }
        
        return { success: false, error: 'No suitable link found' };
      });
      
      if (navigationResult.success) {
        console.log(`✅ Clicked: ${navigationResult.clickedTag} "${navigationResult.clickedText}"`);
        
        // Wait for navigation
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Check if we're now in the books section
        console.log('🔍 Checking if we reached the books section...');
        
        const booksCheck = await page.evaluate(() => {
          const url = window.location.href;
          const bodyText = document.body.textContent;
          
          // Look for MLK book specifically
          const hasMLKBook = bodyText.includes('Um Apelo à Consciência') || 
                            bodyText.includes('Martin Luther King');
          
          // Look for book-like elements
          const bookElements = document.querySelectorAll('[data-testid="book-card"], .book-card, .book-item');
          
          // Count elements that look like books
          const allElements = Array.from(document.querySelectorAll('*'));
          const bookLikeElements = allElements.filter(el => {
            const text = el.textContent || '';
            return text.length > 20 && text.length < 200 && 
                   (text.includes('Autor') || text.includes('por') || text.includes('de'));
          });
          
          return {
            url,
            hasMLKBook,
            bookElementsCount: bookElements.length,
            bookLikeElementsCount: bookLikeElements.length,
            bodyTextPreview: bodyText.substring(0, 300)
          };
        });
        
        console.log('\n📚 BOOKS SECTION CHECK:');
        console.log(`   URL: ${booksCheck.url}`);
        console.log(`   Has MLK Book: ${booksCheck.hasMLKBook ? '✅' : '❌'}`);
        console.log(`   Book Elements: ${booksCheck.bookElementsCount}`);
        console.log(`   Book-like Elements: ${booksCheck.bookLikeElementsCount}`);
        console.log(`   Content Preview: "${booksCheck.bodyTextPreview}..."`);
        
        if (booksCheck.hasMLKBook) {
          console.log('\n🎯 MLK BOOK FOUND! Attempting to open it...');
          
          const mlkBookClick = await page.evaluate(() => {
            const allElements = Array.from(document.querySelectorAll('*'));
            
            for (let element of allElements) {
              const text = element.textContent || '';
              if (text.includes('Um Apelo à Consciência') || text.includes('Martin Luther King')) {
                // Find clickable parent
                let clickable = element;
                while (clickable && clickable !== document.body) {
                  if (clickable.tagName === 'DIV' || clickable.tagName === 'BUTTON' ||
                      clickable.classList.contains('book-card') || 
                      clickable.classList.contains('book-item')) {
                    clickable.click();
                    return {
                      success: true,
                      clickedText: text.substring(0, 50),
                      element: clickable.tagName + '.' + clickable.className
                    };
                  }
                  clickable = clickable.parentElement;
                }
                
                element.click();
                return {
                  success: true,
                  clickedText: text.substring(0, 50),
                  element: element.tagName + '.' + element.className
                };
              }
            }
            
            return { success: false, error: 'MLK book element not clickable' };
          });
          
          if (mlkBookClick.success) {
            console.log(`✅ MLK book clicked: "${mlkBookClick.clickedText}..."`);
            console.log(`   Element: ${mlkBookClick.element}`);
            
            // Wait for reader to load
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Check if reader loaded
            const readerCheck = await page.evaluate(() => {
              const reader = document.querySelector('.formatted-content');
              if (reader) {
                return {
                  success: true,
                  hasContent: !!reader.textContent,
                  contentLength: reader.textContent?.length || 0,
                  contentPreview: reader.textContent?.substring(0, 200) || '',
                  hasMLKContent: reader.textContent?.includes('Martin Luther King') || false
                };
              }
              return { success: false, error: 'Reader not found' };
            });
            
            if (readerCheck.success) {
              console.log('\n📖 READER LOADED SUCCESSFULLY!');
              console.log(`   Has Content: ${readerCheck.hasContent}`);
              console.log(`   Content Length: ${readerCheck.contentLength.toLocaleString()} chars`);
              console.log(`   Has MLK Content: ${readerCheck.hasMLKContent ? '✅' : '❌'}`);
              console.log(`   Preview: "${readerCheck.contentPreview}..."`);
              
              console.log('\n🎉 SUCCESS! MLK book is now open and displaying content.');
              console.log('✅ You can now test the font size controls manually.');
              
            } else {
              console.log('❌ Reader failed to load:', readerCheck.error);
            }
            
          } else {
            console.log('❌ Could not click MLK book:', mlkBookClick.error);
          }
          
        } else {
          console.log('❌ MLK book not found in books section');
        }
        
      } else {
        console.log('❌ Could not navigate to books:', navigationResult.error);
      }
      
    } else {
      console.log('❌ No book-related links found');
    }

    // Keep browser open for manual testing
    console.log('\n⏳ Keeping browser open for 30 seconds for manual testing...');
    await new Promise(resolve => setTimeout(resolve, 30000));

  } catch (error) {
    console.error('💥 Error during navigation:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

navigateToBooks();
