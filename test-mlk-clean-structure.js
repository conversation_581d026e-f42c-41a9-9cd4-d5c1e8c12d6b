import puppeteer from 'puppeteer';

async function testMLKCleanStructure() {
  let browser;
  try {
    console.log('📚 TESTANDO ESTRUTURA LIMPA DO MLK\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Testar a estrutura limpa do MLK
    console.log('🧪 Testando estrutura limpa do MLK...');
    
    const mlkTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${Date.now()}`);
        
        // Testar livro MLK (ID 85)
        const bookText = await BookLoader.getBookText('85');
        
        // Analisar a estrutura
        const lines = bookText.split('\n');
        const analysis = {
          totalLines: lines.length,
          textLength: bookText.length,
          
          // Verificações específicas de limpeza
          titleCount: (bookText.match(/Um Apelo à Consciência/g) || []).length,
          authorCount: (bookText.match(/Martin Luther King Jr\./g) || []).length,
          
          // Estrutura do conteúdo
          chapterTitles: (bookText.match(/^# /gm) || []).length,
          sectionTitles: (bookText.match(/^## /gm) || []).length,
          
          // Verificar se as repetições foram eliminadas
          hasCleanStart: !bookText.includes('Um Apelo a Consciencia Os Melhores Discursos Martin Luther King UM APELO'),
          hasProperSeparation: bookText.includes('## Introdução e Contextualização'),
          
          // Verificar conteúdo específico
          hasIntroduction: bookText.includes('Em 1955, o panorama social'),
          hasMontgomery: bookText.includes('Boicote aos Ônibus de Montgomery'),
          hasDreamSpeech: bookText.includes('I Have a Dream'),
          hasBirmingham: bookText.includes('Carta da Prisão de Birmingham'),
          hasLastSpeech: bookText.includes('I\'ve Been to the Mountaintop'),
          
          preview: bookText.substring(0, 800)
        };
        
        // Verificar duplicação
        const paragraphs = bookText.split('\n\n').filter(p => p.trim().length > 20);
        const uniqueParagraphs = new Set(paragraphs.map(p => p.toLowerCase().trim()));
        analysis.duplicationRate = ((paragraphs.length - uniqueParagraphs.size) / paragraphs.length * 100).toFixed(1);
        
        return {
          success: true,
          analysis,
          bookText: bookText.substring(0, 1500) // Primeiros 1500 chars para inspeção
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (mlkTest.success) {
      console.log('✅ Teste da estrutura MLK concluído com sucesso');
      
      const analysis = mlkTest.analysis;
      console.log('\n📊 ANÁLISE DA ESTRUTURA LIMPA:');
      console.log(`   Total de linhas: ${analysis.totalLines}`);
      console.log(`   Comprimento do texto: ${analysis.textLength.toLocaleString()} caracteres`);
      console.log(`   Taxa de duplicação: ${analysis.duplicationRate}%`);
      
      console.log('\n🧹 VERIFICAÇÕES DE LIMPEZA:');
      console.log(`   ${analysis.hasCleanStart ? '✅' : '❌'} Início limpo (sem repetições)`);
      console.log(`   ${analysis.hasProperSeparation ? '✅' : '❌'} Separação adequada de seções`);
      console.log(`   ${analysis.titleCount <= 2 ? '✅' : '❌'} Título não repetido (${analysis.titleCount} ocorrências)`);
      console.log(`   ${analysis.authorCount <= 2 ? '✅' : '❌'} Autor não repetido (${analysis.authorCount} ocorrências)`);
      
      console.log('\n📚 ESTRUTURA DO CONTEÚDO:');
      console.log(`   ${analysis.chapterTitles >= 1 ? '✅' : '❌'} Títulos de capítulo: ${analysis.chapterTitles}`);
      console.log(`   ${analysis.sectionTitles >= 5 ? '✅' : '❌'} Títulos de seção: ${analysis.sectionTitles}`);
      
      console.log('\n🎤 CONTEÚDO DOS DISCURSOS:');
      console.log(`   ${analysis.hasIntroduction ? '✅' : '❌'} Introdução histórica`);
      console.log(`   ${analysis.hasMontgomery ? '✅' : '❌'} Boicote de Montgomery`);
      console.log(`   ${analysis.hasDreamSpeech ? '✅' : '❌'} Discurso "I Have a Dream"`);
      console.log(`   ${analysis.hasBirmingham ? '✅' : '❌'} Carta da Prisão de Birmingham`);
      console.log(`   ${analysis.hasLastSpeech ? '✅' : '❌'} Último discurso em Memphis`);
      
      console.log('\n📋 Preview do conteúdo limpo:');
      console.log(analysis.preview + '...');
      
      // Avaliar qualidade geral
      const qualityChecks = [
        { name: 'Início limpo', passed: analysis.hasCleanStart },
        { name: 'Separação adequada', passed: analysis.hasProperSeparation },
        { name: 'Sem repetição excessiva', passed: analysis.titleCount <= 2 && analysis.authorCount <= 2 },
        { name: 'Estrutura organizada', passed: analysis.sectionTitles >= 5 },
        { name: 'Conteúdo histórico', passed: analysis.hasIntroduction && analysis.hasMontgomery },
        { name: 'Discursos famosos', passed: analysis.hasDreamSpeech && analysis.hasBirmingham },
        { name: 'Baixa duplicação', passed: parseFloat(analysis.duplicationRate) < 5 }
      ];
      
      const passedChecks = qualityChecks.filter(check => check.passed).length;
      console.log(`\n🎯 Qualidade da Estrutura: ${passedChecks}/${qualityChecks.length}`);
      
      qualityChecks.forEach(check => {
        console.log(`   ${check.passed ? '✅' : '❌'} ${check.name}`);
      });
      
      if (passedChecks === qualityChecks.length) {
        console.log('\n🎉 ESTRUTURA PERFEITA!');
        console.log('✅ Título, resumo e capítulos claramente separados');
        console.log('✅ Sem repetições desnecessárias');
        console.log('✅ Conteúdo histórico bem organizado');
        console.log('✅ Formatação profissional');
      } else if (passedChecks >= qualityChecks.length * 0.8) {
        console.log('\n✅ ESTRUTURA MUITO BOA!');
        console.log('Pequenos ajustes podem ser necessários');
      } else {
        console.log('\n⚠️ ESTRUTURA PRECISA DE MELHORIAS');
      }
      
    } else {
      console.log('❌ Teste da estrutura MLK falhou:', mlkTest.error);
    }

    console.log('\n🚀 INSTRUÇÕES PARA TESTE MANUAL:');
    console.log('1. Abra a aplicação no navegador');
    console.log('2. Procure por "Um Apelo à Consciência" na biblioteca');
    console.log('3. Clique em "Ler Agora"');
    console.log('4. Verifique:');
    console.log('   • Título aparece limpo no topo');
    console.log('   • Resumo está separado do conteúdo');
    console.log('   • Capítulos têm hierarquia clara');
    console.log('   • Não há repetições desnecessárias');
    console.log('   • Discursos históricos estão organizados');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro durante teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testMLKCleanStructure();
