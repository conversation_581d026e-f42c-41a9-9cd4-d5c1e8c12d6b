import puppeteer from 'puppeteer';

async function testSimpleFormatting() {
  let browser;
  try {
    console.log('📝 TESTE SIMPLES DE FORMATAÇÃO\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Testar formatação diretamente
    console.log('🧪 Testando formatação no console...');
    
    const result = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        
        // Teste simples
        const testText = `# Capítulo Um

Este é um parágrafo de teste.

## Seção Importante

Outro parágrafo aqui.`;

        const formatted = BookLoader.formatTextForReader(testText, 'light');
        
        return {
          success: true,
          original: testText,
          formatted: formatted,
          hasChapterTitle: formatted.includes('chapter-title'),
          hasSectionTitle: formatted.includes('section-title'),
          hasBodyText: formatted.includes('body-text'),
          length: formatted.length
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (result.success) {
      console.log('✅ Formatação funcionando!');
      console.log(`📏 Tamanho: ${result.length} caracteres`);
      console.log(`📖 Título de capítulo: ${result.hasChapterTitle ? 'SIM' : 'NÃO'}`);
      console.log(`📑 Título de seção: ${result.hasSectionTitle ? 'SIM' : 'NÃO'}`);
      console.log(`📄 Corpo do texto: ${result.hasBodyText ? 'SIM' : 'NÃO'}`);
      
      console.log('\n📋 Texto original:');
      console.log(result.original);
      
      console.log('\n🎨 Texto formatado:');
      console.log(result.formatted);
      
      if (result.hasChapterTitle && result.hasSectionTitle && result.hasBodyText) {
        console.log('\n🎉 FORMATAÇÃO FUNCIONANDO CORRETAMENTE!');
      } else {
        console.log('\n⚠️ Alguns elementos não foram formatados corretamente.');
      }
      
    } else {
      console.log('❌ Erro na formatação:');
      console.log(result.error);
    }

    console.log('\n🚀 AGORA TESTE MANUALMENTE:');
    console.log('1. Faça login na aplicação');
    console.log('2. Vá para a biblioteca');
    console.log('3. Clique em "Ler Agora" em qualquer livro');
    console.log('4. Observe:');
    console.log('   • Títulos centralizados em CAIXA ALTA');
    console.log('   • Texto justificado com recuo na primeira linha');
    console.log('   • Fonte Roboto aplicada');
    console.log('   • Controles de zoom (+/-) na barra superior');
    console.log('   • Tamanho da fonte em "pt" (16pt padrão)');

    await new Promise(resolve => setTimeout(resolve, 15000));

  } catch (error) {
    console.error('💥 Erro:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testSimpleFormatting();
