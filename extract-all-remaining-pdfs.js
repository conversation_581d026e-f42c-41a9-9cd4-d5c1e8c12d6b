import fs from 'fs';
import { processRealPDF } from './extract-real-pdf-text.js';

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

async function extractAllRemainingPDFs() {
  try {
    console.log('🚀 PROCESSANDO TODOS OS PDFs RESTANTES');
    console.log('=' * 60);
    
    // Listar todos os PDFs
    const allFiles = fs.readdirSync(PDF_FOLDER).filter(file => file.endsWith('.pdf'));
    console.log(`📚 Total de PDFs na pasta: ${allFiles.length}`);
    
    // PDFs já processados (que sabemos que funcionaram)
    const processedFiles = [
      'a_interpretacao_dos_sonhos_sigmund_freud_3.pdf',
      'a_sutil_arte_de_ligar_o_foda_se_mark_manson_3.pdf',
      'habitos_atomicos_james_clear_5.pdf',
      'o_poder_do_habito_charles_duhigg_3.pdf',
      'pai_rico_pai_pobre_robert_t_kiyosaki_3.pdf',
      'a_influencia_de_heidegger_na_psicologia_existencial-humanista_foco_em_heidegger_3.pdf',
      'a_mente_de_adolf_hitler_walter_c_langer_3.pdf',
      'a_psicologia_das_cores_eva_heller_3.pdf',
      'a_psicologia_da_mulher_maravilha_autores_travis_langley_mara_wood_travis_langley_mara_wood_3.pdf',
      'a_sensacao_de_estar_sendo_observado_analise_autor_desconhecido_3.pdf',
      'a_teoria_do_amadurecimento_d_w_winnicott_3.pdf',
      'cem_anos_de_solidao_gabriel_garcia_marquez_3.pdf',
      'determined_a_science_of_life_without_free_will_robert_sapolsky_3.pdf',
      'e_o_cerebro_criou_o_homem_antonio_r_damasio_3.pdf',
      'freud_fundamentos_da_clinica_sigmund_freud_3.pdf'
    ];
    
    // Encontrar PDFs restantes
    const remainingFiles = allFiles.filter(file => !processedFiles.includes(file));
    console.log(`📖 PDFs restantes para processar: ${remainingFiles.length}`);
    console.log(`✅ PDFs já processados: ${processedFiles.length}\n`);
    
    if (remainingFiles.length === 0) {
      console.log('🎉 TODOS OS PDFs JÁ FORAM PROCESSADOS!');
      console.log(`✅ Total de ${allFiles.length} PDFs com conteúdo real extraído`);
      return;
    }
    
    console.log('📋 PDFs restantes:');
    remainingFiles.forEach((file, index) => {
      console.log(`   ${index + 1}. ${file}`);
    });
    console.log('');
    
    let processedCount = 0;
    let errorCount = 0;
    let totalPages = 0;
    let totalCharacters = 0;
    const startTime = Date.now();
    const results = [];
    
    for (let i = 0; i < remainingFiles.length; i++) {
      const file = remainingFiles[i];
      
      console.log(`\n📖 [${i + 1}/${remainingFiles.length}] PROCESSANDO: ${file}`);
      
      try {
        const result = await processRealPDF(file);
        
        if (result.success) {
          processedCount++;
          totalPages += result.pages;
          totalCharacters += result.characters;
          
          results.push({
            file,
            title: result.title,
            author: result.author,
            pages: result.pages,
            characters: result.characters,
            chapters: result.chapters
          });
          
          console.log(`✅ SUCESSO: ${result.title}`);
          console.log(`   📊 ${result.pages} páginas, ${result.characters.toLocaleString()} caracteres`);
        } else {
          errorCount++;
          console.log(`❌ FALHA: ${file}`);
          console.log(`   Erro: ${result.error}`);
        }
        
        // Pausa entre processamentos
        await new Promise(resolve => setTimeout(resolve, 1500));
        
      } catch (error) {
        errorCount++;
        console.error(`💥 ERRO: ${file} - ${error.message}`);
      }
      
      // Log de progresso a cada 5 arquivos
      if ((i + 1) % 5 === 0) {
        const elapsedTime = Math.round((Date.now() - startTime) / 1000);
        const avgTime = Math.round(elapsedTime / (i + 1));
        const remainingTime = Math.round((remainingFiles.length - (i + 1)) * avgTime / 60);
        
        console.log(`\n📊 PROGRESSO:`);
        console.log(`   ✅ Processados: ${processedCount}/${i + 1}`);
        console.log(`   ❌ Erros: ${errorCount}`);
        console.log(`   📈 Taxa de sucesso: ${((processedCount / (i + 1)) * 100).toFixed(1)}%`);
        console.log(`   ⏱️ Tempo médio: ${avgTime}s por PDF`);
        console.log(`   🕐 Tempo estimado restante: ${remainingTime}min`);
        console.log(`   📄 Páginas extraídas: ${totalPages.toLocaleString()}`);
        console.log(`   📝 Caracteres extraídos: ${totalCharacters.toLocaleString()}`);
      }
    }
    
    const endTime = Date.now();
    const totalTime = Math.round((endTime - startTime) / 1000);
    
    console.log('\n' + '=' * 60);
    console.log('🎉 PROCESSAMENTO FINAL CONCLUÍDO!');
    console.log('=' * 60);
    
    console.log(`\n📊 ESTATÍSTICAS DESTA EXECUÇÃO:`);
    console.log(`   📚 PDFs processados: ${processedCount}/${remainingFiles.length}`);
    console.log(`   ❌ Erros: ${errorCount}`);
    console.log(`   📈 Taxa de sucesso: ${((processedCount / remainingFiles.length) * 100).toFixed(1)}%`);
    console.log(`   ⏱️ Tempo total: ${Math.floor(totalTime / 60)}m ${totalTime % 60}s`);
    console.log(`   📄 Páginas extraídas: ${totalPages.toLocaleString()}`);
    console.log(`   📝 Caracteres extraídos: ${totalCharacters.toLocaleString()}`);
    
    // Estatísticas totais
    const totalProcessed = processedFiles.length + processedCount;
    const totalPagesAll = 1165 + totalPages; // 1165 das execuções anteriores
    const totalCharsAll = 2693959 + totalCharacters; // 2693959 das execuções anteriores
    
    console.log(`\n🎯 ESTATÍSTICAS TOTAIS DA BIBLIOTECA:`);
    console.log(`   📚 Total de livros com conteúdo real: ${totalProcessed}/${allFiles.length}`);
    console.log(`   📄 Total de páginas extraídas: ${totalPagesAll.toLocaleString()}`);
    console.log(`   📝 Total de caracteres extraídos: ${totalCharsAll.toLocaleString()}`);
    console.log(`   📖 Média de páginas por livro: ${Math.round(totalPagesAll / totalProcessed)}`);
    console.log(`   📝 Média de caracteres por livro: ${Math.round(totalCharsAll / totalProcessed).toLocaleString()}`);
    
    if (processedCount > 0) {
      console.log(`\n📚 NOVOS LIVROS ADICIONADOS NESTA EXECUÇÃO:`);
      results.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title} - ${result.author}`);
        console.log(`      📄 ${result.pages} páginas, ${result.characters.toLocaleString()} chars`);
      });
    }
    
    if (totalProcessed === allFiles.length) {
      console.log(`\n🏆 MISSÃO COMPLETAMENTE CUMPRIDA!`);
      console.log(`✨ TODOS OS ${allFiles.length} PDFs FORAM PROCESSADOS COM SUCESSO!`);
      console.log(`📚 Biblioteca completa com conteúdo REAL de todos os PDFs`);
      console.log(`📄 ${totalPagesAll.toLocaleString()} páginas de conteúdo autêntico`);
      console.log(`📝 ${totalCharsAll.toLocaleString()} caracteres de texto real`);
      console.log(`\n🎯 AGORA TODOS OS LIVROS MOSTRAM CONTEÚDO REAL DOS PDFs!`);
    } else {
      console.log(`\n⚠️ ATENÇÃO:`);
      console.log(`   ${allFiles.length - totalProcessed} PDFs ainda precisam ser processados`);
      console.log(`   Execute novamente para processar os restantes`);
    }
    
    console.log(`\n🚀 TESTE AGORA:`);
    console.log(`   1. Abra a aplicação no navegador`);
    console.log(`   2. Faça login`);
    console.log(`   3. Clique em "Ler Agora" em qualquer livro`);
    console.log(`   4. Verifique que o conteúdo REAL do PDF está sendo exibido`);
    console.log(`   5. Confirme que todas as páginas estão disponíveis`);
    
  } catch (error) {
    console.error('💥 Erro geral:', error);
  }
}

extractAllRemainingPDFs();
