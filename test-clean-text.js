import puppeteer from 'puppeteer';

async function testCleanText() {
  let browser;
  try {
    console.log('🧹 TESTANDO REMOÇÃO DOS MARCADORES $2\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Testar BookLoader diretamente no console
    console.log('🧪 Testando formatação de texto no console...');
    
    const testResult = await page.evaluate(async () => {
      try {
        // Importar BookLoader
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        
        // Testar com texto que contém marcadores problemáticos
        const testTexts = [
          '# Título Principal\n\n## Subtítulo $2 Teste\n\nTexto normal com $1 marcadores $3.',
          'Texto com $2 no meio e $4 no final.',
          '## Capítulo $2\n\nConteúdo do capítulo com $1 e $3.',
          'Texto limpo sem marcadores.'
        ];
        
        const results = [];
        
        for (let i = 0; i < testTexts.length; i++) {
          const originalText = testTexts[i];
          const formattedText = BookLoader.formatTextForReader(originalText, 'light');
          
          // Verificar se ainda há marcadores $
          const hasMarkers = formattedText.includes('$1') || 
                            formattedText.includes('$2') || 
                            formattedText.includes('$3') || 
                            formattedText.includes('$4');
          
          results.push({
            testIndex: i + 1,
            originalText,
            formattedLength: formattedText.length,
            hasMarkers,
            cleanedText: formattedText.substring(0, 200) + '...'
          });
        }
        
        return { success: true, results };
        
      } catch (error) {
        return {
          success: false,
          error: error.message,
          stack: error.stack
        };
      }
    });

    console.log('\n📊 RESULTADOS DOS TESTES:');
    
    if (testResult.success) {
      let allClean = true;
      
      testResult.results.forEach(result => {
        console.log(`\n🧪 Teste ${result.testIndex}:`);
        console.log(`   📝 Texto original: ${result.originalText}`);
        console.log(`   📏 Tamanho formatado: ${result.formattedLength} caracteres`);
        console.log(`   🧹 Marcadores removidos: ${!result.hasMarkers ? 'SIM' : 'NÃO'}`);
        console.log(`   📋 Preview: ${result.cleanedText}`);
        
        if (result.hasMarkers) {
          allClean = false;
          console.log(`   ❌ PROBLEMA: Ainda contém marcadores $`);
        } else {
          console.log(`   ✅ SUCESSO: Texto limpo`);
        }
      });
      
      console.log('\n🎯 RESULTADO FINAL:');
      if (allClean) {
        console.log('🎉 PERFEITO! Todos os marcadores $2 foram removidos!');
        console.log('✅ O texto agora está limpo e sem artefatos');
      } else {
        console.log('⚠️ ATENÇÃO: Alguns marcadores ainda estão presentes');
        console.log('Verifique a função formatTextForReader');
      }
      
    } else {
      console.log('❌ Erro no teste:');
      console.log(`   ${testResult.error}`);
    }

    // Testar também com um livro real
    console.log('\n📖 TESTANDO COM LIVRO REAL...');
    
    const realBookTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        
        // Testar com um livro que sabemos que existe
        const bookText = await BookLoader.getBookText('3'); // Pai Rico, Pai Pobre
        
        // Verificar se há marcadores $ no texto real
        const dollarMatches = bookText.match(/\$\d+/g) || [];
        const hasProblematicMarkers = dollarMatches.length > 0;
        
        return {
          success: true,
          textLength: bookText.length,
          dollarMatches: dollarMatches.slice(0, 10), // Primeiros 10 matches
          hasProblematicMarkers,
          preview: bookText.substring(0, 500)
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (realBookTest.success) {
      console.log(`📏 Tamanho do texto real: ${realBookTest.textLength.toLocaleString()} caracteres`);
      console.log(`🔍 Marcadores $ encontrados: ${realBookTest.dollarMatches.length}`);
      
      if (realBookTest.hasProblematicMarkers) {
        console.log(`❌ PROBLEMA: Encontrados marcadores: ${realBookTest.dollarMatches.join(', ')}`);
      } else {
        console.log(`✅ SUCESSO: Nenhum marcador problemático encontrado`);
      }
      
      console.log(`📋 Preview do texto real:`);
      console.log(realBookTest.preview + '...');
    } else {
      console.log(`❌ Erro ao testar livro real: ${realBookTest.error}`);
    }

    console.log('\n🚀 TESTE MANUAL:');
    console.log('Agora você pode testar manualmente:');
    console.log('1. Faça login na aplicação');
    console.log('2. Vá para a biblioteca');
    console.log('3. Clique em "Ler Agora" em qualquer livro');
    console.log('4. Verifique se não há mais marcadores $2 no texto');
    console.log('5. Confirme que o texto está limpo e bem formatado');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testCleanText();
