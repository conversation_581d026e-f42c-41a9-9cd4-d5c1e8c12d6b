import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugContentStructure() {
  try {
    console.log('🔍 DEBUGGING CONTENT STRUCTURE AND DUPLICATION ISSUES\n');

    // Test a specific book that we know has content
    const bookId = 3; // Pai Rico, Pai Pobre
    
    console.log(`📖 Analyzing book ID ${bookId}...`);
    
    // Get book data
    const { data: bookData, error: bookError } = await supabase
      .from('books')
      .select('*')
      .eq('id', bookId)
      .single();

    if (bookError || !bookData) {
      console.log('❌ Error fetching book:', bookError?.message);
      return;
    }

    console.log(`✅ Book found: ${bookData.title}`);
    console.log(`   Author: ${bookData.author}`);
    console.log(`   Description: ${bookData.description?.substring(0, 100)}...`);

    // Get book content
    const { data: contentData, error: contentError } = await supabase
      .from('book_contents')
      .select('content')
      .eq('book_id', bookId)
      .single();

    if (contentError || !contentData) {
      console.log('❌ Error fetching content:', contentError?.message);
      return;
    }

    console.log('✅ Content found in database');
    
    const content = contentData.content;
    console.log(`📊 Content structure:`);
    console.log(`   Chapters: ${content.chapters?.length || 0}`);
    console.log(`   Total characters: ${content.total_characters || 'unknown'}`);
    console.log(`   Total pages: ${content.total_pages || 'unknown'}`);

    if (content.chapters && content.chapters.length > 0) {
      console.log('\n📚 CHAPTER ANALYSIS:');
      
      content.chapters.forEach((chapter, index) => {
        console.log(`\nChapter ${index + 1}:`);
        console.log(`   Title: "${chapter.title}"`);
        console.log(`   Content length: ${chapter.content?.length || 0} characters`);
        
        if (chapter.content) {
          // Check for potential duplication patterns
          const lines = chapter.content.split('\n');
          console.log(`   Lines: ${lines.length}`);
          
          // Show first few lines to understand structure
          console.log(`   First 3 lines:`);
          lines.slice(0, 3).forEach((line, i) => {
            console.log(`     ${i + 1}: "${line.substring(0, 80)}${line.length > 80 ? '...' : ''}"`);
          });
          
          // Check for repetitive patterns
          const uniqueLines = new Set(lines);
          const duplicateRatio = (lines.length - uniqueLines.size) / lines.length;
          console.log(`   Duplicate lines ratio: ${(duplicateRatio * 100).toFixed(1)}%`);
          
          // Look for chapter/title patterns
          const titlePatterns = lines.filter(line => 
            /^(CAPÍTULO|CHAPTER|CAP\.?|SEÇÃO|SECTION)\s+/i.test(line.trim()) ||
            line.trim().startsWith('#')
          );
          console.log(`   Potential titles found: ${titlePatterns.length}`);
          titlePatterns.forEach(pattern => {
            console.log(`     - "${pattern.trim()}"`);
          });
        }
      });
    }

    // Test the BookLoader formatting
    console.log('\n🔧 TESTING BOOKLOADER FORMATTING:');
    
    // Simulate what BookLoader.getBookText does
    let fullText = `# ${bookData.title}\n\n**Por ${bookData.author}**\n\n`;
    fullText += `${bookData.description}\n\n`;
    
    if (content.chapters) {
      content.chapters.forEach(chapter => {
        fullText += `## ${chapter.title}\n\n`;
        fullText += `${chapter.content}\n\n`;
      });
    }

    console.log(`📏 Generated full text length: ${fullText.length} characters`);
    console.log(`📋 First 500 characters of full text:`);
    console.log(fullText.substring(0, 500) + '...');

    // Check for duplication in the full text
    const fullTextLines = fullText.split('\n');
    const uniqueFullLines = new Set(fullTextLines);
    const fullDuplicateRatio = (fullTextLines.length - uniqueFullLines.size) / fullTextLines.length;
    console.log(`📊 Full text duplicate lines ratio: ${(fullDuplicateRatio * 100).toFixed(1)}%`);

    // Look for structural elements
    const structuralElements = {
      chapters: (fullText.match(/^# /gm) || []).length,
      sections: (fullText.match(/^## /gm) || []).length,
      paragraphs: fullTextLines.filter(line => line.trim().length > 50).length,
      emptyLines: fullTextLines.filter(line => line.trim() === '').length
    };

    console.log('\n📐 STRUCTURAL ANALYSIS:');
    console.log(`   Chapters (# ): ${structuralElements.chapters}`);
    console.log(`   Sections (## ): ${structuralElements.sections}`);
    console.log(`   Content paragraphs: ${structuralElements.paragraphs}`);
    console.log(`   Empty lines: ${structuralElements.emptyLines}`);

    // Recommendations
    console.log('\n💡 RECOMMENDATIONS:');
    
    if (fullDuplicateRatio > 0.1) {
      console.log('⚠️ High duplication detected - content may need deduplication');
    } else {
      console.log('✅ Low duplication - content structure looks good');
    }

    if (structuralElements.chapters === 0 && structuralElements.sections === 0) {
      console.log('⚠️ No clear chapter/section structure detected');
      console.log('   Consider improving content parsing to identify titles');
    } else {
      console.log('✅ Good structural elements detected');
    }

    if (structuralElements.emptyLines / fullTextLines.length > 0.3) {
      console.log('⚠️ Too many empty lines - may cause visual spacing issues');
    } else {
      console.log('✅ Reasonable spacing structure');
    }

  } catch (error) {
    console.error('💥 Error during analysis:', error);
  }
}

debugContentStructure();
