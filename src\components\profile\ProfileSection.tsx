import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  User, 
  Mail, 
  Calendar, 
  Settings, 
  Crown, 
  BookOpen, 
  Clock, 
  Star, 
  Trophy,
  Edit3,
  Camera,
  Save,
  X,
  Shield,
  Bell,
  CreditCard,
  Download,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ProfileSectionProps {
  user: any;
  dashboardData: any;
}

export function ProfileSection({ user, dashboardData }: ProfileSectionProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    name: user.email?.split('@')[0] || 'Usuário',
    email: user.email || '',
    bio: 'Apaixonado por aprendizado contínuo e desenvolvimento pessoal.',
    location: 'Brasil',
    joinDate: new Date().toLocaleDateString('pt-BR'),
    avatar: null as string | null
  });

  const tabs = [
    { id: 'overview', label: 'Visão Geral', icon: User },
    { id: 'stats', label: 'Estatísticas', icon: Trophy },
    { id: 'preferences', label: 'Preferências', icon: Settings },
    { id: 'subscription', label: 'Assinatura', icon: Crown },
    { id: 'privacy', label: 'Privacidade', icon: Shield },
  ];

  const handleSaveProfile = () => {
    setIsEditing(false);
    // Here you would typically save to backend
    console.log('Saving profile:', profileData);
  };

  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileData(prev => ({ ...prev, avatar: e.target?.result as string }));
      };
      reader.readAsDataURL(file);
    }
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Profile Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 text-white relative overflow-hidden"
      >
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16" />
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12" />
        
        <div className="relative flex items-start space-x-6">
          {/* Avatar */}
          <div className="relative">
            <div className="w-24 h-24 bg-white/20 rounded-2xl flex items-center justify-center overflow-hidden">
              {profileData.avatar ? (
                <img src={profileData.avatar} alt="Avatar" className="w-full h-full object-cover" />
              ) : (
                <User className="w-12 h-12 text-white/80" />
              )}
            </div>
            {isEditing && (
              <label className="absolute -bottom-2 -right-2 w-8 h-8 bg-white text-gray-900 rounded-full flex items-center justify-center cursor-pointer hover:bg-gray-100 transition-colors">
                <Camera className="w-4 h-4" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </label>
            )}
          </div>

          {/* Profile Info */}
          <div className="flex-1">
            <div className="flex items-start justify-between mb-4">
              <div>
                {isEditing ? (
                  <Input
                    value={profileData.name}
                    onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
                    className="text-2xl font-bold bg-white/10 border-white/20 text-white placeholder:text-white/60 mb-2"
                    placeholder="Seu nome"
                  />
                ) : (
                  <h1 className="text-3xl font-bold mb-2">{profileData.name}</h1>
                )}
                <p className="text-white/80 flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>{profileData.email}</span>
                </p>
              </div>
              
              <div className="flex items-center space-x-2">
                {isEditing ? (
                  <>
                    <Button
                      onClick={handleSaveProfile}
                      size="sm"
                      className="bg-white text-gray-900 hover:bg-gray-100"
                    >
                      <Save className="w-4 h-4 mr-2" />
                      Salvar
                    </Button>
                    <Button
                      onClick={() => setIsEditing(false)}
                      size="sm"
                      variant="ghost"
                      className="text-white hover:bg-white/10"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </>
                ) : (
                  <Button
                    onClick={() => setIsEditing(true)}
                    size="sm"
                    variant="ghost"
                    className="text-white hover:bg-white/10"
                  >
                    <Edit3 className="w-4 h-4 mr-2" />
                    Editar
                  </Button>
                )}
              </div>
            </div>

            {/* Bio */}
            <div className="mb-4">
              {isEditing ? (
                <textarea
                  value={profileData.bio}
                  onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
                  className="w-full bg-white/10 border border-white/20 rounded-lg p-3 text-white placeholder:text-white/60 resize-none"
                  rows={3}
                  placeholder="Conte um pouco sobre você..."
                />
              ) : (
                <p className="text-white/90 leading-relaxed">{profileData.bio}</p>
              )}
            </div>

            {/* Quick Stats */}
            <div className="flex items-center space-x-6 text-sm">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-white/60" />
                <span className="text-white/80">Membro desde {profileData.joinDate}</span>
              </div>
              <div className="flex items-center space-x-2">
                <BookOpen className="w-4 h-4 text-white/60" />
                <span className="text-white/80">{dashboardData?.stats?.total_summaries_read || 0} resumos lidos</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Quick Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          {
            label: 'Resumos Lidos',
            value: dashboardData?.stats?.total_summaries_read || 0,
            icon: BookOpen,
            color: 'bg-blue-50 text-blue-600',
            bgColor: 'bg-blue-500'
          },
          {
            label: 'Horas de Leitura',
            value: `${Math.round((dashboardData?.stats?.total_reading_time || 0) / 60)}h`,
            icon: Clock,
            color: 'bg-green-50 text-green-600',
            bgColor: 'bg-green-500'
          },
          {
            label: 'Favoritos',
            value: dashboardData?.stats?.favorites_count || 0,
            icon: Star,
            color: 'bg-yellow-50 text-yellow-600',
            bgColor: 'bg-yellow-500'
          },
          {
            label: 'Sequência Atual',
            value: `${dashboardData?.streak?.current || 0} dias`,
            icon: Trophy,
            color: 'bg-purple-50 text-purple-600',
            bgColor: 'bg-purple-500'
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * index }}
            className="bg-white rounded-2xl border border-gray-100 p-6 hover:shadow-lg transition-all duration-300"
          >
            <div className={`w-12 h-12 rounded-xl ${stat.color} flex items-center justify-center mb-4`}>
              <stat.icon className="w-6 h-6" />
            </div>
            <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
            <p className="text-sm text-gray-500">{stat.label}</p>
          </motion.div>
        ))}
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-2xl border border-gray-100 p-6"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-4">Atividade Recente</h3>
        <div className="space-y-4">
          {dashboardData?.recently_read?.slice(0, 3).map((book: any, index: number) => (
            <div key={book.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-xl">
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-gray-600" />
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900 text-sm">
                  {book.summaries?.books?.title || book.title}
                </p>
                <p className="text-xs text-gray-500">
                  {Math.round(book.progress_percentage)}% concluído
                </p>
              </div>
              <div className="text-xs text-gray-400">
                {new Date(book.last_read_at).toLocaleDateString('pt-BR')}
              </div>
            </div>
          )) || (
            <p className="text-gray-500 text-center py-8">Nenhuma atividade recente</p>
          )}
        </div>
      </motion.div>
    </div>
  );

  const renderStats = () => (
    <div className="space-y-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl border border-gray-100 p-6"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-6">Estatísticas Detalhadas</h3>
        
        {/* Reading Progress Chart */}
        <div className="mb-8">
          <h4 className="font-semibold text-gray-900 mb-4">Progresso de Leitura (Últimos 7 dias)</h4>
          <div className="flex items-end space-x-2 h-32">
            {Array.from({ length: 7 }).map((_, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div 
                  className="w-full bg-gray-900 rounded-t-lg transition-all duration-500"
                  style={{ height: `${Math.random() * 80 + 20}%` }}
                />
                <span className="text-xs text-gray-500 mt-2">
                  {new Date(Date.now() - (6 - index) * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR', { weekday: 'short' })}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Categories Breakdown */}
        <div>
          <h4 className="font-semibold text-gray-900 mb-4">Categorias Mais Lidas</h4>
          <div className="space-y-3">
            {[
              { category: 'Desenvolvimento Pessoal', count: 5, percentage: 45 },
              { category: 'Finanças', count: 3, percentage: 27 },
              { category: 'Negócios', count: 2, percentage: 18 },
              { category: 'Tecnologia', count: 1, percentage: 10 }
            ].map((item) => (
              <div key={item.category} className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-gray-700">{item.category}</span>
                    <span className="text-sm text-gray-500">{item.count} resumos</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-gray-900 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${item.percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </motion.div>
    </div>
  );

  const renderPreferences = () => (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl border border-gray-100 p-6"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-6">Preferências de Leitura</h3>
        
        <div className="space-y-6">
          {/* Reading Preferences */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-3 block">Tema Padrão</label>
            <div className="grid grid-cols-3 gap-3">
              {['Claro', 'Escuro', 'Sépia'].map((theme) => (
                <button
                  key={theme}
                  className="p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors text-sm font-medium"
                >
                  {theme}
                </button>
              ))}
            </div>
          </div>

          {/* Notifications */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-3 block">Notificações</label>
            <div className="space-y-3">
              {[
                { label: 'Novos resumos disponíveis', enabled: true },
                { label: 'Lembrete de leitura diária', enabled: true },
                { label: 'Resumo semanal de progresso', enabled: false },
                { label: 'Recomendações personalizadas', enabled: true }
              ].map((notification) => (
                <div key={notification.label} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{notification.label}</span>
                  <button
                    className={`w-12 h-6 rounded-full transition-colors ${
                      notification.enabled ? 'bg-gray-900' : 'bg-gray-300'
                    }`}
                  >
                    <div
                      className={`w-5 h-5 bg-white rounded-full transition-transform ${
                        notification.enabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );

  const renderSubscription = () => (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 text-white"
      >
        <div className="flex items-center space-x-3 mb-4">
          <Crown className="w-8 h-8 text-yellow-400" />
          <h3 className="text-2xl font-bold">Plano Gratuito</h3>
        </div>
        
        <p className="text-white/80 mb-6">
          Você está no plano gratuito com acesso limitado. Faça upgrade para desbloquear todo o potencial.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="bg-white/10 rounded-xl p-4">
            <h4 className="font-semibold mb-2">Acesso Atual</h4>
            <ul className="space-y-1 text-sm text-white/80">
              <li>• 2 resumos por mês</li>
              <li>• Acesso básico à biblioteca</li>
              <li>• Progresso de leitura</li>
            </ul>
          </div>
          
          <div className="bg-white/10 rounded-xl p-4">
            <h4 className="font-semibold mb-2">Premium (R$ 15/mês)</h4>
            <ul className="space-y-1 text-sm text-white/80">
              <li>• Acesso ilimitado</li>
              <li>• Novos resumos semanais</li>
              <li>• Recursos avançados</li>
              <li>• Suporte prioritário</li>
            </ul>
          </div>
        </div>

        <Button className="bg-white text-gray-900 hover:bg-gray-100 font-semibold">
          <Crown className="w-4 h-4 mr-2" />
          Fazer Upgrade para Premium
        </Button>
      </motion.div>

      {/* Billing History */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white rounded-2xl border border-gray-100 p-6"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-4">Histórico de Cobrança</h3>
        <div className="text-center py-8">
          <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-3" />
          <p className="text-gray-500">Nenhuma cobrança encontrada</p>
          <p className="text-sm text-gray-400">Seu histórico aparecerá aqui após o primeiro pagamento</p>
        </div>
      </motion.div>
    </div>
  );

  const renderPrivacy = () => (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-2xl border border-gray-100 p-6"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-6">Privacidade e Segurança</h3>
        
        <div className="space-y-6">
          {/* Data Export */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
            <div className="flex items-center space-x-3">
              <Download className="w-5 h-5 text-gray-600" />
              <div>
                <h4 className="font-medium text-gray-900">Exportar Dados</h4>
                <p className="text-sm text-gray-500">Baixe uma cópia dos seus dados</p>
              </div>
            </div>
            <Button variant="outline" size="sm">
              Exportar
            </Button>
          </div>

          {/* Account Deletion */}
          <div className="flex items-center justify-between p-4 bg-red-50 rounded-xl border border-red-200">
            <div className="flex items-center space-x-3">
              <Trash2 className="w-5 h-5 text-red-600" />
              <div>
                <h4 className="font-medium text-red-900">Excluir Conta</h4>
                <p className="text-sm text-red-600">Esta ação não pode ser desfeita</p>
              </div>
            </div>
            <Button variant="outline" size="sm" className="text-red-600 border-red-300 hover:bg-red-50">
              Excluir
            </Button>
          </div>

          {/* Privacy Settings */}
          <div>
            <h4 className="font-medium text-gray-900 mb-4">Configurações de Privacidade</h4>
            <div className="space-y-3">
              {[
                { label: 'Permitir análise de uso para melhorias', enabled: true },
                { label: 'Compartilhar progresso com outros usuários', enabled: false },
                { label: 'Receber emails promocionais', enabled: false }
              ].map((setting) => (
                <div key={setting.label} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{setting.label}</span>
                  <button
                    className={`w-12 h-6 rounded-full transition-colors ${
                      setting.enabled ? 'bg-gray-900' : 'bg-gray-300'
                    }`}
                  >
                    <div
                      className={`w-5 h-5 bg-white rounded-full transition-transform ${
                        setting.enabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview': return renderOverview();
      case 'stats': return renderStats();
      case 'preferences': return renderPreferences();
      case 'subscription': return renderSubscription();
      case 'privacy': return renderPrivacy();
      default: return renderOverview();
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Meu Perfil</h1>
        <p className="text-gray-600 text-lg">
          Gerencie suas informações pessoais e preferências de conta.
        </p>
      </motion.div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-gray-900 text-gray-900'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="min-h-[600px]">
        {renderContent()}
      </div>
    </div>
  );
}