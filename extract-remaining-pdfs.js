import { processRealPDF } from './extract-real-pdf-text.js';

// Lista dos PDFs que falharam na primeira execução
const failedPDFs = [
  'a_influencia_de_heidegger_na_psicologia_existencial-humanista_foco_em_heidegger_3.pdf',
  'a_mente_de_adolf_hitler_walter_c_langer_3.pdf',
  'a_psicologia_das_cores_eva_heller_3.pdf',
  'a_psicologia_da_mulher_maravilha_autores_travis_langley_mara_wood_travis_langley_mara_wood_3.pdf',
  'a_sensacao_de_estar_sendo_observado_analise_autor_desconhecido_3.pdf',
  'a_teoria_do_amadurecimento_d_w_winnicott_3.pdf',
  'cem_anos_de_solidao_gabriel_garcia_marquez_3.pdf',
  'determined_a_science_of_life_without_free_will_robert_sapolsky_3.pdf',
  'e_o_cerebro_criou_o_homem_antonio_r_damasio_3.pdf',
  'freud_fundamentos_da_clinica_sigmund_freud_3.pdf'
];

async function extractRemainingPDFs() {
  try {
    console.log('🚀 EXTRAINDO PDFs RESTANTES COM POLÍTICA RLS AJUSTADA');
    console.log('=' * 60);
    console.log(`📚 Total de PDFs para processar: ${failedPDFs.length}\n`);
    
    let processedCount = 0;
    let errorCount = 0;
    let totalPages = 0;
    let totalCharacters = 0;
    const startTime = Date.now();
    const results = [];
    
    for (let i = 0; i < failedPDFs.length; i++) {
      const file = failedPDFs[i];
      
      console.log(`\n📖 [${i + 1}/${failedPDFs.length}] PROCESSANDO: ${file}`);
      
      try {
        const result = await processRealPDF(file);
        
        if (result.success) {
          processedCount++;
          totalPages += result.pages;
          totalCharacters += result.characters;
          
          results.push({
            file,
            title: result.title,
            author: result.author,
            pages: result.pages,
            characters: result.characters,
            chapters: result.chapters
          });
          
          console.log(`✅ SUCESSO: ${result.title}`);
          console.log(`   📊 ${result.pages} páginas, ${result.characters.toLocaleString()} caracteres`);
        } else {
          errorCount++;
          console.log(`❌ FALHA: ${file}`);
          console.log(`   Erro: ${result.error}`);
        }
        
        // Pausa entre processamentos
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        errorCount++;
        console.error(`💥 ERRO: ${file} - ${error.message}`);
      }
    }
    
    const endTime = Date.now();
    const totalTime = Math.round((endTime - startTime) / 1000);
    
    console.log('\n' + '=' * 60);
    console.log('🎉 EXTRAÇÃO DOS PDFs RESTANTES FINALIZADA!');
    console.log('=' * 60);
    
    console.log(`\n📊 ESTATÍSTICAS:`);
    console.log(`   📚 PDFs processados: ${processedCount}/${failedPDFs.length}`);
    console.log(`   ❌ Erros: ${errorCount}`);
    console.log(`   📈 Taxa de sucesso: ${((processedCount / failedPDFs.length) * 100).toFixed(1)}%`);
    console.log(`   ⏱️ Tempo total: ${Math.floor(totalTime / 60)}m ${totalTime % 60}s`);
    console.log(`   📄 Páginas extraídas: ${totalPages.toLocaleString()}`);
    console.log(`   📝 Caracteres extraídos: ${totalCharacters.toLocaleString()}`);
    
    if (processedCount > 0) {
      console.log(`\n📚 NOVOS LIVROS ADICIONADOS:`);
      results.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title} - ${result.author}`);
        console.log(`      📄 ${result.pages} páginas, ${result.characters.toLocaleString()} chars`);
      });
      
      console.log(`\n🎯 TOTAL GERAL AGORA:`);
      console.log(`   📖 ${5 + processedCount} livros com conteúdo real extraído`);
      console.log(`   📄 ${355 + totalPages} páginas de conteúdo real`);
      console.log(`   📝 ${813942 + totalCharacters} caracteres de texto real`);
    }
    
    console.log(`\n✨ MISSÃO ATUALIZADA!`);
    console.log(`Biblioteca agora tem ${5 + processedCount} livros com conteúdo REAL dos PDFs!`);
    
  } catch (error) {
    console.error('💥 Erro geral:', error);
  }
}

extractRemainingPDFs();
