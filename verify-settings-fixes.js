import fs from 'fs';

function verifySettingsFixes() {
  console.log('🔍 VERIFICANDO CORREÇÕES NAS CONFIGURAÇÕES\n');
  
  try {
    // Ler o arquivo PDFReader.tsx
    const pdfReaderContent = fs.readFileSync('src/components/reader/PDFReader.tsx', 'utf8');
    
    console.log('📄 Analisando PDFReader.tsx...\n');
    
    // Verificações específicas
    const checks = [
      {
        name: 'Controle de espaçamento removido',
        pattern: /Espaçamento entre Linhas/,
        shouldExist: false,
        description: 'Controle de line height foi removido'
      },
      {
        name: 'Variável lineHeight removida',
        pattern: /const \[lineHeight, setLineHeight\]/,
        shouldExist: false,
        description: 'Estado lineHeight não é mais necessário'
      },
      {
        name: 'Botões de tema presentes',
        pattern: /onClick.*setTheme.*light|onClick.*setTheme.*dark|onClick.*setTheme.*sepia/,
        shouldExist: true,
        description: 'Botões de tema funcionais'
      },
      {
        name: 'Controles de fonte presentes',
        pattern: /onClick.*setFontSize.*Math\.max|onClick.*setFontSize.*Math\.min/,
        shouldExist: true,
        description: 'Controles de zoom funcionais'
      },
      {
        name: 'Display de fonte em pt',
        pattern: /\{fontSize\}pt/,
        shouldExist: true,
        description: 'Mostra tamanho em pontos'
      },
      {
        name: 'Tema aplicado ao conteúdo',
        pattern: /className.*formatted-content.*\$\{theme\}/,
        shouldExist: true,
        description: 'Classe de tema aplicada ao conteúdo'
      },
      {
        name: 'Line height fixo',
        pattern: /lineHeight: 1\.5/,
        shouldExist: true,
        description: 'Line height fixo em 1.5'
      },
      {
        name: 'Fonte Roboto aplicada',
        pattern: /fontFamily.*Roboto/,
        shouldExist: true,
        description: 'Fonte Roboto configurada'
      }
    ];
    
    let passedChecks = 0;
    
    checks.forEach((check, index) => {
      const found = check.pattern.test(pdfReaderContent);
      const passed = check.shouldExist ? found : !found;
      
      console.log(`${index + 1}. ${passed ? '✅' : '❌'} ${check.name}`);
      console.log(`   ${check.description}`);
      
      if (passed) {
        passedChecks++;
      } else {
        if (check.shouldExist) {
          console.log(`   ⚠️ Padrão esperado não encontrado: ${check.pattern}`);
        } else {
          console.log(`   ⚠️ Padrão indesejado ainda presente: ${check.pattern}`);
        }
      }
      console.log('');
    });
    
    console.log('📊 RESULTADO DA VERIFICAÇÃO:');
    console.log(`✅ Verificações corretas: ${passedChecks}/${checks.length}`);
    console.log(`📈 Taxa de sucesso: ${((passedChecks / checks.length) * 100).toFixed(1)}%`);
    
    if (passedChecks === checks.length) {
      console.log('\n🎉 TODAS AS CORREÇÕES IMPLEMENTADAS CORRETAMENTE!');
      console.log('✅ Controle de espaçamento removido');
      console.log('✅ Controles de tema funcionais');
      console.log('✅ Controles de fonte funcionais');
      console.log('✅ Formatação profissional aplicada');
    } else if (passedChecks >= checks.length * 0.8) {
      console.log('\n✅ MAIORIA DAS CORREÇÕES IMPLEMENTADAS!');
      console.log('Algumas verificações menores podem estar faltando.');
    } else {
      console.log('\n⚠️ VÁRIAS CORREÇÕES AINDA PRECISAM SER IMPLEMENTADAS');
      console.log('Verifique o código do PDFReader.tsx');
    }
    
    // Verificar se há problemas de sintaxe
    console.log('\n🔍 VERIFICANDO ESTRUTURA DO CÓDIGO...');
    
    // Contar elementos importantes
    const themeButtonCount = (pdfReaderContent.match(/setTheme\(/g) || []).length;
    const fontControlCount = (pdfReaderContent.match(/setFontSize\(/g) || []).length;
    const configSectionCount = (pdfReaderContent.match(/Configurações de Leitura/g) || []).length;
    
    console.log(`📱 Botões de tema: ${themeButtonCount} (esperado: 3)`);
    console.log(`🔤 Controles de fonte: ${fontControlCount} (esperado: 4+)`);
    console.log(`⚙️ Seções de configuração: ${configSectionCount} (esperado: 1)`);
    
    // Verificar se não há referências ao line height removido
    const lineHeightRefs = (pdfReaderContent.match(/lineHeight.*1\.[4-9]|setLineHeight/g) || []).length;
    console.log(`📏 Referências a line height variável: ${lineHeightRefs} (esperado: 0)`);
    
    console.log('\n🚀 PRÓXIMOS PASSOS PARA TESTE MANUAL:');
    console.log('1. Abra a aplicação no navegador');
    console.log('2. Faça login e vá para a biblioteca');
    console.log('3. Clique em "Ler Agora" em qualquer livro');
    console.log('4. Clique no ícone de configurações (⚙️) na barra superior');
    console.log('5. Teste os controles:');
    console.log('   • Botões de tema: Claro, Escuro, Sépia');
    console.log('   • Controles de fonte: +/- (deve mostrar em pt)');
    console.log('   • Verifique que NÃO há controle de espaçamento');
    console.log('6. Confirme que as mudanças são aplicadas imediatamente');
    
    // Verificar arquivo CSS também
    console.log('\n📄 VERIFICANDO ARQUIVO CSS...');
    
    try {
      const cssContent = fs.readFileSync('src/components/reader/reader.css', 'utf8');
      const hasThemeStyles = cssContent.includes('.dark .formatted-content') && 
                            cssContent.includes('.sepia .formatted-content');
      const hasRobotoFont = cssContent.includes('Roboto');
      const hasFixedLineHeight = cssContent.includes('line-height: 1.5');
      
      console.log(`🎨 Estilos de tema: ${hasThemeStyles ? 'PRESENTES' : 'AUSENTES'}`);
      console.log(`🔤 Fonte Roboto: ${hasRobotoFont ? 'PRESENTE' : 'AUSENTE'}`);
      console.log(`📏 Line height fixo: ${hasFixedLineHeight ? 'PRESENTE' : 'AUSENTE'}`);
      
    } catch (error) {
      console.log('⚠️ Arquivo CSS não encontrado ou erro na leitura');
    }
    
  } catch (error) {
    console.error('💥 Erro ao verificar correções:', error.message);
  }
}

verifySettingsFixes();
