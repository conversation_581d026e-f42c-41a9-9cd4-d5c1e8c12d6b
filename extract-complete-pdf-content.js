import fs from 'fs';
import path from 'path';
import pdf from 'pdf-parse';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

// Função para extrair TODO o texto de um PDF (todas as páginas)
async function extractCompleteTextFromPDF(dataBuffer) {
  try {
    console.log('   📄 Iniciando extração completa do PDF...');

    const pdfData = await pdf(dataBuffer);
    const fullText = pdfData.text;
    const totalPages = pdfData.numpages;

    console.log(`   📊 Total de páginas: ${totalPages}`);
    console.log(`   ✅ Extração completa: ${fullText.length} caracteres de ${totalPages} páginas`);

    // Simular divisão por páginas baseada no conteúdo
    const lines = fullText.split('\n').filter(line => line.trim().length > 0);
    const linesPerPage = Math.max(1, Math.floor(lines.length / totalPages));

    const pageTexts = [];
    for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
      const startLine = (pageNum - 1) * linesPerPage;
      const endLine = pageNum === totalPages ? lines.length : pageNum * linesPerPage;
      const pageLines = lines.slice(startLine, endLine);

      if (pageLines.length > 0) {
        pageTexts.push({
          pageNumber: pageNum,
          text: pageLines.join('\n')
        });
      }
    }

    return {
      fullText: fullText.trim(),
      pageTexts,
      totalPages: totalPages,
      totalCharacters: fullText.length
    };
  } catch (error) {
    console.error('   ❌ Erro na extração do PDF:', error.message);
    return null;
  }
}

// Função para detectar estrutura real do PDF (capítulos, seções)
function detectPDFStructure(fullText, pageTexts) {
  console.log('   🏗️ Detectando estrutura do PDF...');
  
  const lines = fullText.split('\n').filter(line => line.trim().length > 0);
  const chapters = [];
  let currentChapter = null;
  let chapterContent = '';
  
  // Padrões para detectar títulos/capítulos
  const chapterPatterns = [
    /^CAPÍTULO\s+\d+/i,
    /^PARTE\s+\d+/i,
    /^SEÇÃO\s+\d+/i,
    /^\d+\.\s+[A-Z]/,
    /^[A-Z\s]{10,80}$/,  // Linhas em maiúsculas (possíveis títulos)
    /^[IVX]+\.\s+/,      // Numeração romana
  ];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Verificar se a linha parece ser um título de capítulo
    const isChapterTitle = chapterPatterns.some(pattern => pattern.test(line)) ||
                          (line.length < 100 && line.length > 5 && 
                           /^[A-Z]/.test(line) && 
                           !line.includes('.') && 
                           lines[i + 1]?.trim().length > 50);
    
    if (isChapterTitle && chapterContent.length > 200) {
      // Salvar capítulo anterior
      if (currentChapter) {
        chapters.push({
          id: `chapter${chapters.length + 1}`,
          title: currentChapter,
          content: chapterContent.trim()
        });
      }
      
      // Iniciar novo capítulo
      currentChapter = line;
      chapterContent = '';
    } else {
      chapterContent += line + '\n';
    }
  }
  
  // Adicionar último capítulo
  if (currentChapter && chapterContent.length > 200) {
    chapters.push({
      id: `chapter${chapters.length + 1}`,
      title: currentChapter,
      content: chapterContent.trim()
    });
  }
  
  // Se não encontrou estrutura de capítulos, dividir por páginas
  if (chapters.length === 0) {
    console.log('   📄 Estrutura de capítulos não detectada, organizando por páginas...');
    
    // Agrupar páginas em seções
    const pagesPerSection = Math.max(1, Math.floor(pageTexts.length / 10)); // Máximo 10 seções
    
    for (let i = 0; i < pageTexts.length; i += pagesPerSection) {
      const sectionPages = pageTexts.slice(i, i + pagesPerSection);
      const sectionContent = sectionPages.map(p => p.text).join('\n\n');
      
      if (sectionContent.length > 100) {
        chapters.push({
          id: `section${chapters.length + 1}`,
          title: `Seção ${chapters.length + 1} (Páginas ${sectionPages[0].pageNumber}-${sectionPages[sectionPages.length - 1].pageNumber})`,
          content: sectionContent
        });
      }
    }
  }
  
  console.log(`   📚 Estrutura detectada: ${chapters.length} seções/capítulos`);
  return chapters;
}

// Função para extrair pontos-chave reais do texto completo
function extractRealKeyPointsFromFullText(fullText) {
  console.log('   🔑 Extraindo pontos-chave do texto completo...');
  
  const sentences = fullText.split(/[.!?]+/).filter(s => s.trim().length > 30);
  const keyPoints = [];
  
  // Padrões que indicam pontos importantes
  const importantPatterns = [
    /é importante/i,
    /fundamental/i,
    /essencial/i,
    /princípio/i,
    /conceito/i,
    /conclusão/i,
    /resumindo/i,
    /em suma/i,
    /portanto/i,
    /assim/i,
    /desta forma/i,
    /podemos concluir/i
  ];
  
  // Procurar frases que começam com números ou marcadores
  const numberedPoints = fullText.match(/(?:^|\n)\s*(?:\d+[\.\)]\s*|[•\-\*]\s*).{20,200}(?=\n|$)/gm);
  if (numberedPoints) {
    keyPoints.push(...numberedPoints.slice(0, 10).map(point => point.trim()));
  }
  
  // Procurar frases importantes
  for (const sentence of sentences) {
    if (importantPatterns.some(pattern => pattern.test(sentence))) {
      const cleanSentence = sentence.trim();
      if (cleanSentence.length > 40 && cleanSentence.length < 300) {
        keyPoints.push(cleanSentence);
        if (keyPoints.length >= 15) break;
      }
    }
  }
  
  // Remover duplicatas e limitar
  const uniquePoints = [...new Set(keyPoints)].slice(0, 12);
  console.log(`   ✅ ${uniquePoints.length} pontos-chave extraídos`);
  
  return uniquePoints;
}

// Função para extrair exercícios práticos do texto completo
function extractRealExercisesFromFullText(fullText) {
  console.log('   💡 Extraindo exercícios práticos...');
  
  const exercises = [];
  
  // Padrões que indicam exercícios ou atividades práticas
  const exercisePatterns = [
    /exercício/i,
    /atividade/i,
    /prática/i,
    /aplicação/i,
    /experimente/i,
    /faça/i,
    /tente/i,
    /implemente/i,
    /realize/i,
    /execute/i
  ];
  
  const sentences = fullText.split(/[.!?]+/).filter(s => s.trim().length > 20);
  
  for (const sentence of sentences) {
    if (exercisePatterns.some(pattern => pattern.test(sentence))) {
      const cleanSentence = sentence.trim();
      if (cleanSentence.length > 30 && cleanSentence.length < 250) {
        exercises.push(cleanSentence);
        if (exercises.length >= 8) break;
      }
    }
  }
  
  // Se não encontrou exercícios específicos, criar baseado no conteúdo
  if (exercises.length < 3) {
    exercises.push(
      'Reflita sobre os conceitos apresentados e como se aplicam à sua realidade',
      'Identifique exemplos práticos dos princípios discutidos no texto',
      'Analise criticamente os argumentos e evidências apresentados',
      'Discuta os pontos principais com outras pessoas para aprofundar a compreensão'
    );
  }
  
  console.log(`   ✅ ${exercises.length} exercícios extraídos`);
  return exercises.slice(0, 8);
}

// Função para extrair metadados reais do PDF
function extractBookMetadataFromPDF(filename, fullText) {
  console.log('   📋 Extraindo metadados do PDF...');
  
  const nameWithoutExt = filename.replace('.pdf', '');
  const lines = fullText.split('\n').filter(line => line.trim().length > 0);
  
  // Tentar extrair título das primeiras linhas
  let extractedTitle = '';
  let extractedAuthor = '';
  
  for (let i = 0; i < Math.min(20, lines.length); i++) {
    const line = lines[i].trim();
    
    // Procurar título
    if (!extractedTitle && line.length > 10 && line.length < 150) {
      if (line.toUpperCase() === line || 
          /^[A-Z][^a-z]*[A-Z]$/.test(line) ||
          line.includes('RESUMO') ||
          line.includes('LIVRO')) {
        extractedTitle = line.replace(/RESUMO\s*:?\s*/i, '').replace(/LIVRO\s*:?\s*/i, '').trim();
      }
    }
    
    // Procurar autor
    if (line.toLowerCase().includes('autor:') || 
        line.toLowerCase().includes('por:') || 
        line.toLowerCase().includes('de:')) {
      extractedAuthor = line.replace(/autor:?/i, '').replace(/por:?/i, '').replace(/de:?/i, '').trim();
    }
  }
  
  // Mapeamento manual para casos conhecidos
  const manualMappings = {
    'a_interpretacao_dos_sonhos_sigmund_freud_3': {
      title: 'A Interpretação dos Sonhos',
      author: 'Sigmund Freud',
      category: 'Psicologia'
    },
    'a_sutil_arte_de_ligar_o_foda_se_mark_manson_3': {
      title: 'A Sutil Arte de Ligar o F*da-se',
      author: 'Mark Manson',
      category: 'Autoajuda'
    },
    'o_poder_do_habito_charles_duhigg_3': {
      title: 'O Poder do Hábito',
      author: 'Charles Duhigg',
      category: 'Produtividade'
    },
    'pai_rico_pai_pobre_robert_t_kiyosaki_3': {
      title: 'Pai Rico, Pai Pobre',
      author: 'Robert Kiyosaki',
      category: 'Finanças'
    },
    'habitos_atomicos_james_clear_5': {
      title: 'Hábitos Atômicos',
      author: 'James Clear',
      category: 'Produtividade'
    }
  };
  
  const manual = manualMappings[nameWithoutExt];
  
  return {
    title: manual?.title || extractedTitle || nameWithoutExt.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    author: manual?.author || extractedAuthor || 'Autor Desconhecido',
    category: manual?.category || 'Geral',
    description: `Conteúdo completo extraído do PDF original com ${fullText.length} caracteres.`,
    duration: Math.max(30, Math.min(180, Math.floor(fullText.length / 200))), // Baseado no tamanho real
    difficulty: fullText.length > 50000 ? 'Avançado' : fullText.length > 20000 ? 'Intermediário' : 'Fácil'
  };
}

// Função principal para processar UM PDF completamente
async function processCompletePDF(filename) {
  console.log(`\n📖 PROCESSANDO COMPLETAMENTE: ${filename}`);
  console.log('=' * 60);
  
  try {
    const pdfPath = path.join(PDF_FOLDER, filename);
    
    if (!fs.existsSync(pdfPath)) {
      console.log('   ❌ Arquivo não encontrado');
      return false;
    }
    
    const dataBuffer = fs.readFileSync(pdfPath);
    console.log(`   📁 Arquivo carregado: ${(dataBuffer.length / 1024 / 1024).toFixed(2)} MB`);
    
    // Extrair TODO o texto do PDF
    const extractionResult = await extractCompleteTextFromPDF(dataBuffer);
    
    if (!extractionResult || extractionResult.fullText.length < 500) {
      console.log('   ❌ Extração falhou ou conteúdo insuficiente');
      return false;
    }
    
    console.log(`   📊 ESTATÍSTICAS:`);
    console.log(`      📄 Total de páginas: ${extractionResult.totalPages}`);
    console.log(`      📝 Total de caracteres: ${extractionResult.totalCharacters.toLocaleString()}`);
    console.log(`      📏 Tamanho médio por página: ${Math.round(extractionResult.totalCharacters / extractionResult.totalPages)} chars`);
    
    // Detectar estrutura do PDF
    const chapters = detectPDFStructure(extractionResult.fullText, extractionResult.pageTexts);
    
    // Extrair pontos-chave e exercícios do texto completo
    const keyPoints = extractRealKeyPointsFromFullText(extractionResult.fullText);
    const exercises = extractRealExercisesFromFullText(extractionResult.fullText);
    
    // Extrair metadados
    const metadata = extractBookMetadataFromPDF(filename, extractionResult.fullText);
    
    console.log(`   📚 Estrutura final:`);
    console.log(`      📖 Capítulos/Seções: ${chapters.length}`);
    console.log(`      🔑 Pontos-chave: ${keyPoints.length}`);
    console.log(`      💡 Exercícios: ${exercises.length}`);
    console.log(`      ⏱️ Duração estimada: ${metadata.duration} min`);
    
    // Preparar conteúdo completo para o banco
    const completeContent = {
      chapters: chapters,
      key_points: keyPoints,
      practical_exercises: exercises,
      full_text: extractionResult.fullText, // Texto completo para busca
      total_pages: extractionResult.totalPages,
      total_characters: extractionResult.totalCharacters,
      extraction_date: new Date().toISOString()
    };
    
    // Salvar no banco de dados
    console.log('   💾 Salvando no banco de dados...');
    
    // Verificar se o livro já existe
    const { data: existingBook, error: searchError } = await supabase
      .from('books')
      .select('id')
      .eq('title', metadata.title)
      .single();

    let bookId;
    
    if (existingBook) {
      bookId = existingBook.id;
      console.log(`   ✅ Livro encontrado (ID: ${bookId}), atualizando...`);
      
      // Atualizar informações do livro
      await supabase
        .from('books')
        .update({
          author: metadata.author,
          category: metadata.category,
          description: metadata.description,
          duration: metadata.duration,
          difficulty: metadata.difficulty,
          is_featured: true,
          is_free: true,
          pdf_key: filename
        })
        .eq('id', bookId);
    } else {
      // Inserir novo livro
      const { data: newBook, error: insertError } = await supabase
        .from('books')
        .insert({
          title: metadata.title,
          author: metadata.author,
          category: metadata.category,
          description: metadata.description,
          duration: metadata.duration,
          difficulty: metadata.difficulty,
          is_featured: true,
          is_free: true,
          pdf_key: filename
        })
        .select('id')
        .single();

      if (insertError) {
        console.error(`   ❌ Erro ao inserir livro: ${insertError.message}`);
        return false;
      }
      
      bookId = newBook.id;
      console.log(`   ✅ Novo livro criado (ID: ${bookId})`);
    }

    // Inserir/atualizar conteúdo COMPLETO
    const { error: contentError } = await supabase
      .from('book_contents')
      .upsert({
        book_id: bookId,
        content: completeContent
      });

    if (contentError) {
      console.error(`   ❌ Erro ao salvar conteúdo: ${contentError.message}`);
      return false;
    }
    
    console.log(`   🎉 SUCESSO! Conteúdo completo salvo:`);
    console.log(`      📖 ${metadata.title}`);
    console.log(`      ✍️ ${metadata.author}`);
    console.log(`      📊 ${extractionResult.totalCharacters.toLocaleString()} caracteres de ${extractionResult.totalPages} páginas`);
    
    return true;
    
  } catch (error) {
    console.error(`   💥 Erro ao processar ${filename}: ${error.message}`);
    return false;
  }
}

export { processCompletePDF };
