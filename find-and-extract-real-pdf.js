import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

function searchForPDFFiles(dir, searchTerm = 'martin') {
  const results = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Recursively search subdirectories
        results.push(...searchForPDFFiles(fullPath, searchTerm));
      } else if (stat.isFile() && item.toLowerCase().endsWith('.pdf')) {
        // Check if filename contains search term
        if (item.toLowerCase().includes(searchTerm.toLowerCase()) || 
            item.toLowerCase().includes('king') ||
            item.toLowerCase().includes('apelo') ||
            item.toLowerCase().includes('consciencia')) {
          results.push({
            path: fullPath,
            name: item,
            size: stat.size,
            modified: stat.mtime
          });
        }
      }
    }
  } catch (error) {
    // Skip directories we can't read
  }
  
  return results;
}

function extractTextFromPDFBuffer(buffer) {
  try {
    // Convert buffer to string for text extraction
    const pdfString = buffer.toString('latin1');
    
    // Method 1: Extract text from PDF streams
    const textStreams = [];
    
    // Look for text objects in PDF
    const streamRegex = /stream\s*([\s\S]*?)\s*endstream/g;
    let match;
    
    while ((match = streamRegex.exec(pdfString)) !== null) {
      const streamContent = match[1];
      
      // Try to extract readable text from stream
      const textMatches = streamContent.match(/\(([^)]*)\)/g);
      if (textMatches) {
        textMatches.forEach(textMatch => {
          const text = textMatch.slice(1, -1) // Remove parentheses
            .replace(/\\n/g, '\n')
            .replace(/\\r/g, '\r')
            .replace(/\\t/g, '\t')
            .replace(/\\\\/g, '\\');
          
          if (text.length > 3 && /[a-zA-Z]/.test(text)) {
            textStreams.push(text);
          }
        });
      }
    }
    
    // Method 2: Look for text in different PDF structures
    const textObjects = [];
    
    // Look for Tj operators (show text)
    const tjRegex = /\(([^)]*)\)\s*Tj/g;
    while ((match = tjRegex.exec(pdfString)) !== null) {
      const text = match[1]
        .replace(/\\n/g, '\n')
        .replace(/\\r/g, '\r')
        .replace(/\\t/g, '\t')
        .replace(/\\\\/g, '\\');
      
      if (text.length > 2 && /[a-zA-Z]/.test(text)) {
        textObjects.push(text);
      }
    }
    
    // Method 3: Look for TJ operators (show text with positioning)
    const tjArrayRegex = /\[(.*?)\]\s*TJ/g;
    while ((match = tjArrayRegex.exec(pdfString)) !== null) {
      const arrayContent = match[1];
      const stringMatches = arrayContent.match(/\(([^)]*)\)/g);
      
      if (stringMatches) {
        stringMatches.forEach(stringMatch => {
          const text = stringMatch.slice(1, -1)
            .replace(/\\n/g, '\n')
            .replace(/\\r/g, '\r')
            .replace(/\\t/g, '\t')
            .replace(/\\\\/g, '\\');
          
          if (text.length > 2 && /[a-zA-Z]/.test(text)) {
            textObjects.push(text);
          }
        });
      }
    }
    
    // Combine all extracted text
    const allText = [...textStreams, ...textObjects];
    
    if (allText.length === 0) {
      return null;
    }
    
    // Join and clean the text
    let extractedText = allText.join(' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    // If text is too short or seems corrupted, try alternative method
    if (extractedText.length < 100 || !/martin|luther|king|direitos|discurso/i.test(extractedText)) {
      // Try to find readable text patterns
      const readableMatches = pdfString.match(/[A-Za-zÀ-ÿ]{3,}[\s\w\.,;:!?\-À-ÿ]{20,}/g);
      if (readableMatches && readableMatches.length > 10) {
        extractedText = readableMatches
          .filter(text => text.length > 20)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim();
      }
    }
    
    return extractedText.length > 50 ? extractedText : null;
    
  } catch (error) {
    console.log('Error extracting text:', error.message);
    return null;
  }
}

async function findAndExtractRealPDF() {
  try {
    console.log('🔍 PROCURANDO PELO PDF ORIGINAL DO MLK\n');

    // Lista de caminhos possíveis para procurar
    const searchPaths = [
      'C:\\Users\\<USER>\\.claude\\book\\resumos_padronizados_roboto_final\\home\\ubuntu\\resumos_padronizados\\pdf_final',
      'C:\\Users\\<USER>\\.claude\\book\\resumos_padronizados_roboto_final',
      'C:\\Users\\<USER>\\.claude\\book',
      './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final',
      './resumos_padronizados_roboto_final',
      '.',
      '..',
      '../..',
      'resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final'
    ];

    console.log('1. Procurando arquivos PDF do MLK em todos os caminhos...');
    
    let allPDFs = [];
    
    for (const searchPath of searchPaths) {
      console.log(`   Procurando em: ${searchPath}`);
      
      if (fs.existsSync(searchPath)) {
        const pdfs = searchForPDFFiles(searchPath);
        allPDFs.push(...pdfs);
        console.log(`   ✅ Encontrados ${pdfs.length} PDFs relacionados ao MLK`);
      } else {
        console.log(`   ❌ Caminho não existe`);
      }
    }

    // Remove duplicatas
    const uniquePDFs = allPDFs.filter((pdf, index, self) => 
      index === self.findIndex(p => p.path === pdf.path)
    );

    console.log(`\n📚 TOTAL DE PDFs ENCONTRADOS: ${uniquePDFs.length}`);
    
    if (uniquePDFs.length === 0) {
      console.log('❌ Nenhum PDF do MLK encontrado!');
      console.log('\n🔍 Vou listar todos os PDFs disponíveis...');
      
      // Procurar por qualquer PDF
      for (const searchPath of searchPaths) {
        if (fs.existsSync(searchPath)) {
          const allPDFs = searchForPDFFiles(searchPath, ''); // Buscar todos os PDFs
          if (allPDFs.length > 0) {
            console.log(`\n📁 PDFs em ${searchPath}:`);
            allPDFs.forEach(pdf => {
              console.log(`   ${pdf.name} (${pdf.size} bytes)`);
            });
          }
        }
      }
      return;
    }

    // Mostrar todos os PDFs encontrados
    console.log('\n📋 PDFs DO MLK ENCONTRADOS:');
    uniquePDFs.forEach((pdf, index) => {
      console.log(`   ${index + 1}. ${pdf.name}`);
      console.log(`      Caminho: ${pdf.path}`);
      console.log(`      Tamanho: ${pdf.size.toLocaleString()} bytes`);
      console.log(`      Modificado: ${pdf.modified}`);
      console.log('');
    });

    // Tentar extrair texto de cada PDF
    console.log('2. Tentando extrair texto de cada PDF...');
    
    let bestExtraction = null;
    let bestPDF = null;

    for (const pdf of uniquePDFs) {
      console.log(`\n📄 Processando: ${pdf.name}`);
      
      try {
        const buffer = fs.readFileSync(pdf.path);
        console.log(`   ✅ PDF lido (${buffer.length} bytes)`);
        
        const extractedText = extractTextFromPDFBuffer(buffer);
        
        if (extractedText) {
          console.log(`   ✅ Texto extraído (${extractedText.length} caracteres)`);
          console.log(`   📋 Preview: "${extractedText.substring(0, 200)}..."`);
          
          // Verificar se o texto parece ser do MLK
          const hasMLKContent = 
            extractedText.toLowerCase().includes('martin luther king') ||
            extractedText.toLowerCase().includes('direitos civis') ||
            extractedText.toLowerCase().includes('discurso') ||
            extractedText.toLowerCase().includes('montgomery') ||
            extractedText.toLowerCase().includes('birmingham');
          
          console.log(`   🎯 Conteúdo do MLK detectado: ${hasMLKContent ? 'SIM' : 'NÃO'}`);
          
          if (hasMLKContent && (!bestExtraction || extractedText.length > bestExtraction.length)) {
            bestExtraction = extractedText;
            bestPDF = pdf;
            console.log(`   ⭐ MELHOR EXTRAÇÃO ATÉ AGORA!`);
          }
          
        } else {
          console.log(`   ❌ Não foi possível extrair texto legível`);
        }
        
      } catch (error) {
        console.log(`   ❌ Erro ao processar: ${error.message}`);
      }
    }

    if (!bestExtraction) {
      console.log('\n❌ Não foi possível extrair texto de nenhum PDF');
      console.log('💡 Os PDFs podem estar criptografados, ser baseados em imagem, ou ter codificação especial');
      return;
    }

    console.log(`\n🎉 MELHOR EXTRAÇÃO ENCONTRADA!`);
    console.log(`   Arquivo: ${bestPDF.name}`);
    console.log(`   Tamanho do texto: ${bestExtraction.length.toLocaleString()} caracteres`);
    
    // Limpar e formatar o texto extraído
    console.log('\n3. Limpando e formatando o texto extraído...');
    
    let cleanText = bestExtraction
      .replace(/\s+/g, ' ') // Normalizar espaços
      .replace(/\n\s*\n/g, '\n\n') // Normalizar quebras de linha
      .replace(/[^\x20-\x7E\u00C0-\u017F\n\r\t]/g, '') // Manter apenas caracteres legíveis
      .trim();

    console.log(`✅ Texto limpo (${cleanText.length} caracteres)`);

    // Atualizar o banco de dados com o conteúdo REAL
    console.log('\n4. Atualizando banco de dados com conteúdo REAL do PDF...');
    
    const realContentStructure = {
      chapters: [
        {
          title: "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King",
          content: cleanText // CONTEÚDO REAL DO PDF
        }
      ],
      key_points: [], // Vazio - apenas conteúdo original
      practical_exercises: [], // Vazio - apenas conteúdo original
      total_characters: cleanText.length,
      total_pages: Math.ceil(cleanText.length / 2000),
      source: `Extraído de: ${bestPDF.name}`,
      extraction_date: new Date().toISOString()
    };

    const bookId = 85;

    // Remover conteúdo fake/mock
    const { error: deleteError } = await supabase
      .from('book_contents')
      .delete()
      .eq('book_id', bookId);

    if (deleteError) {
      console.log('❌ Erro ao deletar conteúdo anterior:', deleteError.message);
      return;
    }

    console.log('✅ Conteúdo fake/mock removido');

    // Inserir conteúdo REAL do PDF
    const { error: insertError } = await supabase
      .from('book_contents')
      .insert({
        book_id: bookId,
        content: realContentStructure
      });

    if (insertError) {
      console.log('❌ Erro ao inserir conteúdo real:', insertError.message);
      return;
    }

    console.log('✅ Conteúdo REAL do PDF inserido no banco');

    // Atualizar descrição do livro
    const { error: updateError } = await supabase
      .from('books')
      .update({
        description: `Conteúdo extraído do PDF (${cleanText.length.toLocaleString()} caracteres).`
      })
      .eq('id', bookId);

    if (updateError) {
      console.log('❌ Erro ao atualizar descrição:', updateError.message);
    } else {
      console.log('✅ Descrição do livro atualizada');
    }

    console.log('\n🎉 CONTEÚDO REAL DO PDF EXTRAÍDO E ARMAZENADO!');
    console.log('');
    console.log('📊 ESTATÍSTICAS DO CONTEÚDO REAL:');
    console.log(`   Arquivo fonte: ${bestPDF.name}`);
    console.log(`   Caracteres: ${cleanText.length.toLocaleString()}`);
    console.log(`   Páginas estimadas: ${Math.ceil(cleanText.length / 2000)}`);
    console.log(`   Tipo: Extração autêntica do PDF original`);
    console.log('');
    console.log('✅ RESULTADO:');
    console.log('   ✓ TODO conteúdo fake/mock removido');
    console.log('   ✓ Conteúdo REAL extraído do PDF original');
    console.log('   ✓ Texto autêntico preservado sem modificações');
    console.log('   ✓ Pronto para formatação Kindle');
    console.log('');
    console.log('📋 PREVIEW DO CONTEÚDO REAL:');
    console.log(`"${cleanText.substring(0, 500)}..."`);

  } catch (error) {
    console.error('💥 Erro durante extração:', error.message);
  }
}

findAndExtractRealPDF();
