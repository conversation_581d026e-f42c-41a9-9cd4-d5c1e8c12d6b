import puppeteer from 'puppeteer';

async function testBookLoaderInBrowser() {
  let browser;
  try {
    console.log('🧪 TESTE: BookLoader no console do navegador\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Testar BookLoader diretamente no console
    console.log('🧪 Testando BookLoader diretamente no console...');
    
    const testResults = await page.evaluate(async () => {
      try {
        // Importar BookLoader
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        
        const results = {};
        
        // Testar livros específicos que sabemos que têm conteúdo real
        const testBooks = [
          { id: '3', name: '<PERSON><PERSON>, <PERSON>i <PERSON>' },
          { id: '4', name: 'A Sutil Arte' },
          { id: '7', name: 'A Interpretação dos Sonhos' },
          { id: '85', name: 'Martin Luther King' }
        ];

        for (const testBook of testBooks) {
          console.log(`Testing BookLoader for book ${testBook.id}: ${testBook.name}`);
          
          try {
            // Testar loadBook
            const book = await BookLoader.loadBook(testBook.id);
            
            if (book) {
              const firstChapter = book.content?.chapters?.[0];
              const content = firstChapter?.content || '';
              
              // Verificar se é conteúdo real
              const isRealContent = content.length > 1000 && 
                                   !content.includes('Este é um resumo profissional') &&
                                   !content.includes('Nossa metodologia garante') &&
                                   !content.includes('Conteúdo não disponível');
              
              results[testBook.id] = {
                name: testBook.name,
                title: book.title,
                author: book.author,
                hasContent: !!firstChapter,
                chapterTitle: firstChapter?.title || 'N/A',
                contentLength: content.length,
                contentPreview: content.substring(0, 200),
                isRealContent,
                status: isRealContent ? 'REAL' : 'GENERIC'
              };
            } else {
              results[testBook.id] = {
                name: testBook.name,
                error: 'Book not found'
              };
            }
          } catch (error) {
            results[testBook.id] = {
              name: testBook.name,
              error: error.message
            };
          }
        }
        
        return results;
        
      } catch (error) {
        return { error: error.message };
      }
    });

    console.log('\n📊 RESULTADOS DOS TESTES:');
    console.log('=' * 50);

    let successCount = 0;
    let totalCount = 0;

    for (const [bookId, result] of Object.entries(testResults)) {
      if (result.error) {
        console.log(`❌ ${result.name}: ERRO - ${result.error}`);
        continue;
      }

      totalCount++;
      console.log(`\n📖 ${result.name}:`);
      console.log(`   📝 Título: ${result.title}`);
      console.log(`   ✍️ Autor: ${result.author}`);
      console.log(`   📄 Capítulo: ${result.chapterTitle}`);
      console.log(`   📏 Tamanho: ${result.contentLength} caracteres`);
      console.log(`   🎯 Status: ${result.status}`);
      
      if (result.status === 'REAL') {
        console.log(`   ✅ SUCESSO: Conteúdo real detectado!`);
        successCount++;
      } else {
        console.log(`   ❌ PROBLEMA: Ainda mostra conteúdo genérico`);
      }
      
      console.log(`   📋 Preview: ${result.contentPreview}...`);
    }

    // Testar também getBookText
    console.log('\n📄 TESTANDO getBookText...');
    
    const textTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import('/src/lib/bookLoader.ts');
        const bookText = await BookLoader.getBookText('3'); // Pai Rico, Pai Pobre
        
        const isRealText = bookText.length > 5000 && 
                          !bookText.includes('Este é um resumo profissional') &&
                          !bookText.includes('Nossa metodologia garante');
        
        return {
          success: true,
          length: bookText.length,
          isRealText,
          preview: bookText.substring(0, 400)
        };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    if (textTest.success) {
      console.log(`✅ Texto gerado: ${textTest.length} caracteres`);
      console.log(`🎯 Conteúdo real: ${textTest.isRealText ? 'SIM' : 'NÃO'}`);
      console.log(`📋 Preview: ${textTest.preview}...`);
    } else {
      console.log(`❌ Erro na conversão: ${textTest.error}`);
    }

    console.log('\n🎯 RESUMO FINAL:');
    console.log(`✅ Livros com conteúdo real: ${successCount}/${totalCount}`);
    console.log(`📊 Taxa de sucesso: ${totalCount > 0 ? ((successCount / totalCount) * 100).toFixed(1) : 0}%`);

    if (successCount === totalCount && totalCount > 0) {
      console.log('\n🎉 PROBLEMA COMPLETAMENTE RESOLVIDO!');
      console.log('Todos os livros testados agora mostram conteúdo real dos PDFs');
      console.log('O BookLoader está funcionando corretamente!');
    } else if (successCount > 0) {
      console.log('\n⚠️ PROBLEMA PARCIALMENTE RESOLVIDO');
      console.log('Alguns livros ainda mostram conteúdo genérico.');
    } else {
      console.log('\n❌ PROBLEMA PERSISTE');
      console.log('Os livros ainda mostram conteúdo genérico.');
    }

    console.log('\n🚀 PRÓXIMO PASSO:');
    console.log('Teste manualmente no navegador:');
    console.log('1. Faça login na aplicação');
    console.log('2. Vá para a biblioteca');
    console.log('3. Clique em "Ler Agora" em qualquer livro');
    console.log('4. Verifique se o conteúdo real está sendo exibido');

    await page.screenshot({ 
      path: 'bookloader-test.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot salvo como bookloader-test.png');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testBookLoaderInBrowser();
