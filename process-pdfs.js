import fs from 'fs';
import path from 'path';
import pdf from 'pdf-parse';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

// Função para extrair informações do nome do arquivo
function extractBookInfo(filename) {
  // Remove a extensão .pdf
  const nameWithoutExt = filename.replace('.pdf', '');
  
  // Mapear nomes de arquivos para informações dos livros
  const bookMappings = {
    'a_sutil_arte_de_ligar_o_foda_se_mark_manson_3': {
      title: 'A Sutil Arte de Ligar o F*da-se',
      author: 'Mark Manson',
      category: 'Autoajuda'
    },
    'o_poder_do_habito_charles_duhigg_3': {
      title: 'O Poder do Hábito',
      author: 'Charles Duhigg',
      category: 'Produtividade'
    },
    'pai_rico_pai_pobre_robert_t_kiyosaki_3': {
      title: 'Pai Rico, Pai Pobre',
      author: 'Robert Kiyosaki',
      category: 'Finanças'
    },
    'pense_e_enriqueca_napoleon_hill_3': {
      title: 'Pense e Enriqueça',
      author: 'Napoleon Hill',
      category: 'Finanças/Autoajuda'
    },
    'habitos_atomicos_james_clear_5': {
      title: 'Hábitos Atômicos',
      author: 'James Clear',
      category: 'Produtividade'
    },
    'habitos_atomicos_james_clear_6': {
      title: 'Hábitos Atômicos',
      author: 'James Clear',
      category: 'Produtividade'
    },
    'a_interpretacao_dos_sonhos_sigmund_freud_3': {
      title: 'A Interpretação dos Sonhos',
      author: 'Sigmund Freud',
      category: 'Psicologia'
    },
    'cem_anos_de_solidao_gabriel_garcia_marquez_3': {
      title: 'Cem Anos de Solidão',
      author: 'Gabriel García Márquez',
      category: 'Literatura'
    },
    'o_codigo_da_vinci_dan_brown_3': {
      title: 'O Código da Vinci',
      author: 'Dan Brown',
      category: 'Literatura'
    },
    'tornar_se_pessoa_carl_rogers_3': {
      title: 'Tornar-se Pessoa',
      author: 'Carl Rogers',
      category: 'Psicologia'
    }
  };

  return bookMappings[nameWithoutExt] || {
    title: nameWithoutExt.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    author: 'Autor Desconhecido',
    category: 'Geral'
  };
}

// Função para limpar e estruturar o texto
function cleanAndStructureText(text) {
  // Remove caracteres especiais e normaliza espaços
  let cleanText = text
    .replace(/\s+/g, ' ')
    .replace(/[^\w\s\.\,\!\?\;\:\-\(\)\[\]]/g, '')
    .trim();

  // Divide o texto em seções
  const sections = cleanText.split(/(?:\n\s*\n|\.\s+(?=[A-Z]))/);
  
  return sections.filter(section => section.length > 50);
}

// Função para criar estrutura padronizada do resumo
function createStandardizedContent(bookInfo, rawText) {
  const sections = cleanAndStructureText(rawText);
  
  // Criar resumo executivo (primeiros parágrafos)
  const executiveSummary = sections.slice(0, 2).join('\n\n');
  
  // Criar capítulos baseados nas seções
  const chapters = [];
  
  if (sections.length >= 3) {
    chapters.push({
      id: 'chapter1',
      title: 'Resumo Executivo',
      content: executiveSummary
    });
    
    chapters.push({
      id: 'chapter2',
      title: 'Conceitos Principais',
      content: sections.slice(2, Math.ceil(sections.length * 0.6)).join('\n\n')
    });
    
    chapters.push({
      id: 'chapter3',
      title: 'Aplicação Prática',
      content: sections.slice(Math.ceil(sections.length * 0.6)).join('\n\n')
    });
  } else {
    chapters.push({
      id: 'chapter1',
      title: 'Conteúdo Principal',
      content: sections.join('\n\n')
    });
  }

  // Gerar pontos-chave baseados na categoria
  const keyPoints = generateKeyPoints(bookInfo.category, bookInfo.title);
  
  // Gerar exercícios práticos
  const practicalExercises = generatePracticalExercises(bookInfo.category);

  return {
    chapters,
    key_points: keyPoints,
    practical_exercises: practicalExercises
  };
}

// Função para gerar pontos-chave baseados na categoria
function generateKeyPoints(category, title) {
  const categoryKeyPoints = {
    'Autoajuda': [
      'Desenvolva autoconhecimento e consciência pessoal',
      'Foque no que você pode controlar',
      'Pratique a aceitação de situações inevitáveis',
      'Cultive relacionamentos saudáveis',
      'Invista em crescimento pessoal contínuo'
    ],
    'Produtividade': [
      'Pequenas mudanças geram grandes resultados',
      'Consistência é mais importante que intensidade',
      'Identifique e elimine hábitos prejudiciais',
      'Crie sistemas em vez de focar apenas em objetivos',
      'Use o poder dos hábitos compostos'
    ],
    'Finanças': [
      'Invista em educação financeira',
      'Diversifique seus investimentos',
      'Viva abaixo de suas possibilidades',
      'Crie múltiplas fontes de renda',
      'Planeje para o longo prazo'
    ],
    'Psicologia': [
      'Compreenda os mecanismos da mente humana',
      'Reconheça padrões de comportamento',
      'Desenvolva inteligência emocional',
      'Pratique a empatia e compreensão',
      'Busque equilíbrio mental e bem-estar'
    ],
    'Literatura': [
      'Explore diferentes perspectivas humanas',
      'Desenvolva senso crítico e reflexivo',
      'Aprecie a arte da narrativa',
      'Conecte-se com experiências universais',
      'Expanda sua visão de mundo'
    ]
  };

  return categoryKeyPoints[category] || [
    'Principais conceitos e ideias centrais',
    'Aplicações práticas do conhecimento',
    'Reflexões importantes sobre o tema',
    'Insights valiosos para desenvolvimento',
    'Lições fundamentais para a vida'
  ];
}

// Função para gerar exercícios práticos
function generatePracticalExercises(category) {
  const categoryExercises = {
    'Autoajuda': [
      'Faça uma autoavaliação honesta de suas forças e fraquezas',
      'Pratique mindfulness por 10 minutos diários',
      'Identifique e questione pensamentos negativos automáticos',
      'Estabeleça limites saudáveis em relacionamentos'
    ],
    'Produtividade': [
      'Identifique um hábito que você quer desenvolver',
      'Crie um sistema de recompensas para novos hábitos',
      'Elimine uma distração do seu ambiente',
      'Estabeleça uma rotina matinal consistente'
    ],
    'Finanças': [
      'Crie um orçamento detalhado de receitas e despesas',
      'Defina metas financeiras de curto e longo prazo',
      'Pesquise sobre diferentes tipos de investimento',
      'Calcule seu patrimônio líquido atual'
    ],
    'Psicologia': [
      'Observe e registre seus padrões emocionais',
      'Pratique técnicas de regulação emocional',
      'Reflita sobre suas motivações e valores',
      'Desenvolva estratégias de enfrentamento saudáveis'
    ],
    'Literatura': [
      'Reflita sobre os temas centrais da obra',
      'Analise o desenvolvimento dos personagens',
      'Conecte a narrativa com experiências pessoais',
      'Discuta a obra com outros leitores'
    ]
  };

  return categoryExercises[category] || [
    'Reflita sobre os conceitos apresentados',
    'Aplique os conhecimentos em situações reais',
    'Compartilhe os aprendizados com outros',
    'Pratique regularmente os ensinamentos'
  ];
}

// Função principal para processar todos os PDFs
async function processAllPDFs() {
  try {
    console.log('🚀 Iniciando processamento dos PDFs...');
    
    const files = fs.readdirSync(PDF_FOLDER).filter(file => file.endsWith('.pdf'));
    console.log(`📚 Encontrados ${files.length} PDFs para processar`);

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`\n📖 Processando ${i + 1}/${files.length}: ${file}`);
      
      try {
        // Ler o PDF
        const pdfPath = path.join(PDF_FOLDER, file);
        const dataBuffer = fs.readFileSync(pdfPath);
        const pdfData = await pdf(dataBuffer);
        
        // Extrair informações do livro
        const bookInfo = extractBookInfo(file);
        console.log(`   📝 Título: ${bookInfo.title}`);
        console.log(`   ✍️  Autor: ${bookInfo.author}`);
        
        // Criar conteúdo estruturado
        const structuredContent = createStandardizedContent(bookInfo, pdfData.text);
        
        // Buscar o livro no banco de dados
        const { data: existingBook, error: searchError } = await supabase
          .from('books')
          .select('id')
          .eq('title', bookInfo.title)
          .single();

        let bookId;
        
        if (existingBook) {
          bookId = existingBook.id;
          console.log(`   ✅ Livro encontrado no banco (ID: ${bookId})`);
        } else {
          // Inserir novo livro
          const { data: newBook, error: insertError } = await supabase
            .from('books')
            .insert({
              title: bookInfo.title,
              author: bookInfo.author,
              category: bookInfo.category,
              description: `Resumo profissional de "${bookInfo.title}" por ${bookInfo.author}`,
              duration: 15,
              difficulty: 'Intermediário',
              is_featured: false,
              is_free: true,
              pdf_key: file
            })
            .select('id')
            .single();

          if (insertError) {
            console.error(`   ❌ Erro ao inserir livro: ${insertError.message}`);
            continue;
          }
          
          bookId = newBook.id;
          console.log(`   ✅ Novo livro criado (ID: ${bookId})`);
        }

        // Inserir ou atualizar conteúdo
        const { error: contentError } = await supabase
          .from('book_contents')
          .upsert({
            book_id: bookId,
            content: structuredContent
          });

        if (contentError) {
          console.error(`   ❌ Erro ao inserir conteúdo: ${contentError.message}`);
        } else {
          console.log(`   ✅ Conteúdo inserido com sucesso!`);
        }

      } catch (error) {
        console.error(`   ❌ Erro ao processar ${file}: ${error.message}`);
      }
    }

    console.log('\n🎉 Processamento concluído!');
    
  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar o processamento
processAllPDFs();
