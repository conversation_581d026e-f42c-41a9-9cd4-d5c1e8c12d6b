import puppeteer from 'puppeteer';

async function debugFormatting() {
  let browser;
  try {
    console.log('🔍 DEBUG DA FORMATAÇÃO\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Debug detalhado
    const result = await page.evaluate(async () => {
      try {
        // Forçar reload do módulo
        const timestamp = Date.now();
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${timestamp}`);
        
        const testText = `# Capítulo Um

Este é um parágrafo de teste.

## Seção Importante

Outro parágrafo aqui.`;

        console.log('Original text:', testText);
        
        // Testar passo a passo
        const lines = testText.split('\n');
        console.log('Lines:', lines);
        
        const formatted = BookLoader.formatTextForReader(testText, 'light');
        console.log('Formatted result:', formatted);
        
        return {
          success: true,
          original: testText,
          formatted: formatted,
          lines: lines,
          hasChapterTitle: formatted.includes('chapter-title'),
          hasSectionTitle: formatted.includes('section-title'),
          hasBodyText: formatted.includes('body-text')
        };
        
      } catch (error) {
        console.error('Error in evaluation:', error);
        return {
          success: false,
          error: error.message,
          stack: error.stack
        };
      }
    });

    if (result.success) {
      console.log('✅ Debug executado com sucesso');
      console.log('\n📋 Linhas originais:');
      result.lines.forEach((line, i) => {
        console.log(`${i}: "${line}"`);
      });
      
      console.log('\n🎨 Resultado formatado:');
      console.log(result.formatted);
      
      console.log('\n🔍 Verificações:');
      console.log(`📖 Título de capítulo: ${result.hasChapterTitle}`);
      console.log(`📑 Título de seção: ${result.hasSectionTitle}`);
      console.log(`📄 Corpo do texto: ${result.hasBodyText}`);
      
    } else {
      console.log('❌ Erro no debug:');
      console.log(result.error);
      console.log(result.stack);
    }

    // Verificar se o arquivo foi realmente atualizado
    console.log('\n📁 Verificando se o arquivo foi atualizado...');
    
    const fileCheck = await page.evaluate(() => {
      // Verificar timestamp do módulo
      const scripts = Array.from(document.querySelectorAll('script'));
      const moduleScript = scripts.find(s => s.src && s.src.includes('bookLoader'));
      
      return {
        hasModuleScript: !!moduleScript,
        scriptSrc: moduleScript?.src || 'not found',
        timestamp: Date.now()
      };
    });

    console.log(`📄 Script do módulo: ${fileCheck.hasModuleScript ? 'ENCONTRADO' : 'NÃO ENCONTRADO'}`);
    console.log(`🔗 URL do script: ${fileCheck.scriptSrc}`);
    console.log(`⏰ Timestamp: ${fileCheck.timestamp}`);

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro no debug:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

debugFormatting();
