import puppeteer from 'puppeteer';

async function checkApp() {
  let browser;
  try {
    console.log('🚀 Iniciando verificação da aplicação...');
    
    browser = await puppeteer.launch({
      headless: false, // Para ver o que está acontecendo
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Capturar erros do console
    const consoleMessages = [];
    const errors = [];
    
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        location: msg.location()
      });
      console.log(`📝 Console ${msg.type()}: ${msg.text()}`);
    });

    page.on('pageerror', error => {
      errors.push(error.message);
      console.log(`❌ Erro na página: ${error.message}`);
    });

    page.on('requestfailed', request => {
      console.log(`🚫 Requisição falhou: ${request.url()} - ${request.failure().errorText}`);
    });

    console.log('🌐 Navegando para http://localhost:5173...');
    
    // Navegar para a aplicação
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    console.log('⏳ Aguardando carregamento da página...');
    
    // Aguardar um pouco para a página carregar completamente
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Verificar se a página carregou
    const title = await page.title();
    console.log(`📄 Título da página: ${title}`);

    // Verificar se há elementos principais
    const bodyContent = await page.evaluate(() => {
      return {
        hasRoot: !!document.getElementById('root'),
        rootContent: document.getElementById('root')?.innerHTML?.substring(0, 200) + '...',
        bodyText: document.body.innerText.substring(0, 500) + '...',
        hasReactElements: document.querySelector('[data-reactroot]') !== null || 
                         document.querySelector('#root > div') !== null,
        visibleElements: Array.from(document.querySelectorAll('*')).filter(el => {
          const style = window.getComputedStyle(el);
          return style.display !== 'none' && style.visibility !== 'hidden';
        }).length
      };
    });

    console.log('🔍 Análise da página:');
    console.log(`   - Elemento root existe: ${bodyContent.hasRoot}`);
    console.log(`   - Elementos React detectados: ${bodyContent.hasReactElements}`);
    console.log(`   - Elementos visíveis: ${bodyContent.visibleElements}`);
    console.log(`   - Conteúdo do body: ${bodyContent.bodyText}`);

    // Verificar se há erros de rede
    const networkErrors = await page.evaluate(() => {
      return window.performance.getEntriesByType('navigation').map(entry => ({
        name: entry.name,
        status: entry.responseStatus,
        duration: entry.duration
      }));
    });

    console.log('🌐 Status da rede:', networkErrors);

    // Tentar encontrar elementos específicos da aplicação
    const appElements = await page.evaluate(() => {
      const elements = {};
      
      // Procurar por elementos comuns da aplicação
      elements.heroSection = !!document.querySelector('[class*="hero"]') || 
                            !!document.querySelector('h1, h2, h3');
      elements.navigation = !!document.querySelector('nav') || 
                           !!document.querySelector('[class*="nav"]');
      elements.buttons = document.querySelectorAll('button').length;
      elements.loadingSpinner = !!document.querySelector('[class*="spin"]') ||
                               !!document.querySelector('[class*="loading"]');
      elements.errorMessages = document.querySelectorAll('[class*="error"]').length;
      
      return elements;
    });

    console.log('🎯 Elementos da aplicação encontrados:');
    console.log(`   - Hero/Título: ${appElements.heroSection}`);
    console.log(`   - Navegação: ${appElements.navigation}`);
    console.log(`   - Botões: ${appElements.buttons}`);
    console.log(`   - Loading spinner: ${appElements.loadingSpinner}`);
    console.log(`   - Mensagens de erro: ${appElements.errorMessages}`);

    // Tirar screenshot
    await page.screenshot({ 
      path: 'app-screenshot.png', 
      fullPage: true 
    });
    console.log('📸 Screenshot salvo como app-screenshot.png');

    // Resumo dos problemas encontrados
    console.log('\n📊 RESUMO DA ANÁLISE:');
    
    if (errors.length > 0) {
      console.log('❌ ERROS ENCONTRADOS:');
      errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    const consoleErrors = consoleMessages.filter(msg => msg.type === 'error');
    if (consoleErrors.length > 0) {
      console.log('🔴 ERROS NO CONSOLE:');
      consoleErrors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error.text}`);
      });
    }

    const consoleWarnings = consoleMessages.filter(msg => msg.type === 'warning');
    if (consoleWarnings.length > 0) {
      console.log('⚠️ AVISOS NO CONSOLE:');
      consoleWarnings.forEach((warning, index) => {
        console.log(`   ${index + 1}. ${warning.text}`);
      });
    }

    if (errors.length === 0 && consoleErrors.length === 0) {
      console.log('✅ Nenhum erro crítico encontrado!');
    }

    // Aguardar um pouco antes de fechar
    await new Promise(resolve => setTimeout(resolve, 2000));

  } catch (error) {
    console.error('💥 Erro durante a verificação:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

checkApp();
