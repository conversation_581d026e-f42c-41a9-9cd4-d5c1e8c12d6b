import React from 'react';
import { motion } from 'framer-motion';
import { Clock, Star, BookOpen, Play } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Book {
  id: string;
  title: string;
  author: string;
  category: string;
  duration: number;
  difficulty: string;
  is_featured: boolean;
  description: string;
  cover_image_url?: string;
}

interface BookCardProps {
  book: Book;
  onRead: (bookId: string) => void;
  onFavorite?: (bookId: string) => void;
  isFavorited?: boolean;
}

export function BookCard({ book, onRead, onFavorite, isFavorited }: BookCardProps) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'básico': return 'bg-green-100 text-green-700';
      case 'intermediário': return 'bg-yellow-100 text-yellow-700';
      case 'avançado': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -4, scale: 1.02 }}
      className="bg-white rounded-2xl border border-gray-100 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 group"
    >
      {/* Cover Image */}
      <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
        {book.cover_image_url ? (
          <img 
            src={book.cover_image_url} 
            alt={book.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-16 h-16 bg-gray-900 rounded-xl flex items-center justify-center">
            <BookOpen className="w-8 h-8 text-white" />
          </div>
        )}
        
        {/* Featured Badge */}
        {book.is_featured && (
          <div className="absolute top-3 left-3 bg-gray-900 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
            <Star className="w-3 h-3" />
            <span>Destaque</span>
          </div>
        )}

        {/* Play Button Overlay */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          whileHover={{ opacity: 1, scale: 1 }}
          className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300"
        >
          <Button
            onClick={() => onRead(book.id)}
            className="bg-white text-gray-900 hover:bg-gray-100 rounded-full w-12 h-12 p-0 shadow-lg"
          >
            <Play className="w-5 h-5 ml-0.5" />
          </Button>
        </motion.div>
      </div>

      {/* Content */}
      <div className="p-5">
        {/* Category & Difficulty */}
        <div className="flex items-center justify-between mb-3">
          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
            {book.category}
          </span>
          <span className={`text-xs font-medium px-2 py-1 rounded-full ${getDifficultyColor(book.difficulty)}`}>
            {book.difficulty}
          </span>
        </div>

        {/* Title & Author */}
        <h3 className="font-bold text-gray-900 text-lg mb-1 line-clamp-2 leading-tight">
          {book.title}
        </h3>
        <p className="text-gray-600 text-sm mb-3 font-medium">
          {book.author}
        </p>

        {/* Description */}
        <p className="text-gray-500 text-sm mb-4 line-clamp-2 leading-relaxed">
          {book.description}
        </p>

        {/* Duration & Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1 text-gray-500">
            <Clock className="w-4 h-4" />
            <span className="text-sm font-medium">{book.duration} min</span>
          </div>

          <div className="flex items-center space-x-2">
            {onFavorite && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onFavorite(book.id)}
                className={`p-2 rounded-lg transition-colors ${
                  isFavorited 
                    ? 'text-red-500 hover:text-red-600 bg-red-50' 
                    : 'text-gray-400 hover:text-gray-600'
                }`}
              >
                <Star className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
              </Button>
            )}
            
            <Button
              onClick={() => onRead(book.id)}
              className="bg-gray-900 text-white hover:bg-gray-800 px-4 py-2 text-sm font-medium rounded-lg transition-all"
            >
              Ler Agora
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}