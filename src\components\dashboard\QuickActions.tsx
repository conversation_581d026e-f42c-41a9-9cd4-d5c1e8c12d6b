import React from 'react';
import { motion } from 'framer-motion';
import { BookOpen, Heart, Clock, TrendingUp, Search, Bookmark } from 'lucide-react';

interface QuickActionsProps {
  onSectionChange: (section: string) => void;
}

export function QuickActions({ onSectionChange }: QuickActionsProps) {
  const actions = [
    {
      id: 'library',
      label: 'Explorar Biblioteca',
      description: 'Descubra novos resumos',
      icon: BookOpen,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600'
    },
    {
      id: 'favorites',
      label: 'Meus Favoritos',
      description: 'Resumos salvos',
      icon: Heart,
      color: 'from-red-500 to-red-600',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600'
    },
    {
      id: 'progress',
      label: 'Em Progresso',
      description: 'Continue lendo',
      icon: Clock,
      color: 'from-yellow-500 to-yellow-600',
      bgColor: 'bg-yellow-50',
      textColor: 'text-yellow-600'
    },
    {
      id: 'trending',
      label: 'Tendências',
      description: 'Mais populares',
      icon: TrendingUp,
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600'
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {actions.map((action, index) => (
        <motion.button
          key={action.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          whileHover={{ y: -4, scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => onSectionChange(action.id)}
          className="bg-white rounded-2xl border border-gray-100 p-6 text-left hover:shadow-lg transition-all duration-300 group"
        >
          <div className={`w-12 h-12 rounded-xl ${action.bgColor} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
            <action.icon className={`w-6 h-6 ${action.textColor}`} />
          </div>
          
          <h3 className="font-semibold text-gray-900 mb-1 group-hover:text-gray-800 transition-colors">
            {action.label}
          </h3>
          
          <p className="text-sm text-gray-500 group-hover:text-gray-600 transition-colors">
            {action.description}
          </p>
        </motion.button>
      ))}
    </div>
  );
}