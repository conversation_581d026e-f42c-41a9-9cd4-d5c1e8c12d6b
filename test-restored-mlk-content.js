import puppeteer from 'puppeteer';

async function testRestoredMLKContent() {
  let browser;
  try {
    console.log('📚 TESTING RESTORED MLK PDF CONTENT\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test the restored content
    console.log('🧪 Testing restored MLK content...');
    
    const contentTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${Date.now()}`);
        
        // Get the restored content
        const processedText = await BookLoader.getBookText('85');
        const formattedHTML = BookLoader.formatTextForReader(processedText, 'light');
        
        // Analyze the restored content
        const analysis = {
          processedTextLength: processedText.length,
          formattedHTMLLength: formattedHTML.length,
          
          // Check for PDF-style content characteristics
          hasExecutiveSummary: processedText.includes('RESUMO EXECUTIVO') || processedText.includes('Resumo Executivo'),
          hasHistoricalContext: processedText.includes('CONTEXTO HISTÓRICO') || processedText.includes('segregação'),
          hasPhilosophy: processedText.includes('FILOSOFIA') || processedText.includes('não-violenta'),
          hasCampaigns: processedText.includes('Montgomery') || processedText.includes('Birmingham'),
          hasSpeeches: processedText.includes('I Have a Dream') || processedText.includes('Carta da Prisão'),
          hasLegacy: processedText.includes('LEGADO') || processedText.includes('influência'),
          
          // Check HTML structure
          chapterTitleElements: (formattedHTML.match(/class="chapter-title"/g) || []).length,
          sectionTitleElements: (formattedHTML.match(/class="section-title"/g) || []).length,
          bodyTextElements: (formattedHTML.match(/class="body-text"/g) || []).length,
          
          // Check content quality
          hasSubstantialContent: processedText.length > 50000,
          hasProperStructure: formattedHTML.includes('<div class="chapter-title">'),
          
          // Sample content
          firstChapterTitle: (formattedHTML.match(/<div class="chapter-title">([^<]+)<\/div>/) || [])[1],
          firstSectionTitle: (formattedHTML.match(/<div class="section-title">([^<]+)<\/div>/) || [])[1],
          
          // Preview
          processedPreview: processedText.substring(0, 600),
          formattedPreview: formattedHTML.substring(0, 800)
        };
        
        return {
          success: true,
          analysis
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (contentTest.success) {
      console.log('✅ Content test completed successfully\n');
      
      const analysis = contentTest.analysis;
      
      console.log('📊 CONTENT ANALYSIS:');
      console.log(`   Processed Text Length: ${analysis.processedTextLength.toLocaleString()} characters`);
      console.log(`   Formatted HTML Length: ${analysis.formattedHTMLLength.toLocaleString()} characters`);
      console.log(`   Substantial Content: ${analysis.hasSubstantialContent ? 'YES' : 'NO'}`);
      
      console.log('\n📚 PDF-STYLE CONTENT VERIFICATION:');
      console.log(`   ${analysis.hasExecutiveSummary ? '✅' : '❌'} Executive Summary`);
      console.log(`   ${analysis.hasHistoricalContext ? '✅' : '❌'} Historical Context`);
      console.log(`   ${analysis.hasPhilosophy ? '✅' : '❌'} Philosophy & Methodology`);
      console.log(`   ${analysis.hasCampaigns ? '✅' : '❌'} Major Campaigns`);
      console.log(`   ${analysis.hasSpeeches ? '✅' : '❌'} Key Speeches`);
      console.log(`   ${analysis.hasLegacy ? '✅' : '❌'} Legacy & Impact`);
      
      console.log('\n🏷️ KINDLE-STYLE FORMATTING:');
      console.log(`   Chapter Title Elements: ${analysis.chapterTitleElements}`);
      console.log(`   Section Title Elements: ${analysis.sectionTitleElements}`);
      console.log(`   Body Text Elements: ${analysis.bodyTextElements}`);
      console.log(`   Proper HTML Structure: ${analysis.hasProperStructure ? 'YES' : 'NO'}`);
      
      console.log('\n📋 SAMPLE ELEMENTS:');
      console.log(`   Chapter Title: "${analysis.firstChapterTitle || 'Not found'}"`);
      console.log(`   Section Title: "${analysis.firstSectionTitle || 'Not found'}"`);
      
      console.log('\n🔍 CONTENT PREVIEW:');
      console.log(analysis.processedPreview);
      
      console.log('\n🏷️ FORMATTED HTML PREVIEW:');
      console.log(analysis.formattedPreview);
      
      // Calculate content quality score
      const contentChecks = [
        analysis.hasExecutiveSummary,
        analysis.hasHistoricalContext,
        analysis.hasPhilosophy,
        analysis.hasCampaigns,
        analysis.hasSpeeches,
        analysis.hasLegacy,
        analysis.hasSubstantialContent,
        analysis.hasProperStructure,
        analysis.chapterTitleElements >= 1,
        analysis.bodyTextElements >= 20
      ];
      
      const passedChecks = contentChecks.filter(check => check).length;
      const contentScore = (passedChecks / contentChecks.length * 100).toFixed(1);
      
      console.log(`\n🎯 CONTENT QUALITY SCORE: ${passedChecks}/${contentChecks.length} (${contentScore}%)`);
      
      if (contentScore >= 90) {
        console.log('\n🎉 EXCELLENT CONTENT RESTORATION!');
        console.log('✅ Users will see comprehensive MLK content');
        console.log('✅ PDF-style format maintained');
        console.log('✅ Kindle-style formatting applied');
        console.log('✅ Educational value preserved');
      } else if (contentScore >= 80) {
        console.log('\n✅ GOOD CONTENT RESTORATION!');
        console.log('Most requirements met, minor improvements possible');
      } else {
        console.log('\n⚠️ CONTENT RESTORATION NEEDS IMPROVEMENT');
      }
      
    } else {
      console.log('❌ Content test failed:', contentTest.error);
    }

    console.log('\n🚀 MANUAL VERIFICATION:');
    console.log('1. Open the MLK book "Um Apelo à Consciência" in the reader');
    console.log('2. Verify you see:');
    console.log('   • Comprehensive analysis of MLK\'s life and work');
    console.log('   • Historical context and background information');
    console.log('   • Information about major campaigns and speeches');
    console.log('   • Analysis of his philosophy and methodology');
    console.log('   • Discussion of his legacy and contemporary relevance');
    console.log('3. Confirm Kindle-style formatting is maintained:');
    console.log('   • Proper title hierarchy');
    console.log('   • Justified text with first-line indentation');
    console.log('   • Professional spacing and typography');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Error during test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testRestoredMLKContent();
