import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugBookLoaderQuery() {
  try {
    console.log('🔍 DEBUGANDO CONSULTA DO BOOKLOADER\n');

    const bookId = 85;

    console.log('1. Testando consulta simples do livro...');
    const { data: book, error: bookError } = await supabase
      .from('books')
      .select('*')
      .eq('id', bookId)
      .single();

    if (bookError) {
      console.log('❌ Erro na consulta do livro:', bookError.message);
      return;
    }

    console.log('✅ Livro encontrado:');
    console.log(`   ID: ${book.id}`);
    console.log(`   Título: ${book.title}`);
    console.log(`   Autor: ${book.author}`);

    console.log('\n2. Testando consulta simples do conteúdo...');
    const { data: content, error: contentError } = await supabase
      .from('book_contents')
      .select('*')
      .eq('book_id', bookId);

    if (contentError) {
      console.log('❌ Erro na consulta do conteúdo:', contentError.message);
      return;
    }

    console.log(`✅ Conteúdo encontrado: ${content.length} entrada(s)`);
    if (content.length > 0) {
      console.log(`   ID do conteúdo: ${content[0].id}`);
      console.log(`   Book ID: ${content[0].book_id}`);
      console.log(`   Tipo do conteúdo: ${typeof content[0].content}`);
    }

    console.log('\n3. Testando consulta JOIN (como o BookLoader faz)...');
    const { data: bookWithContent, error: joinError } = await supabase
      .from('books')
      .select(`
        *,
        book_contents (
          content
        )
      `)
      .eq('id', bookId)
      .single();

    if (joinError) {
      console.log('❌ Erro na consulta JOIN:', joinError.message);
      console.log('   Código:', joinError.code);
      console.log('   Detalhes:', joinError.details);
      console.log('   Hint:', joinError.hint);
      return;
    }

    console.log('✅ Consulta JOIN bem-sucedida');
    console.log(`   Livro carregado: ${bookWithContent.title}`);
    console.log(`   book_contents: ${bookWithContent.book_contents ? 'presente' : 'ausente'}`);
    
    if (bookWithContent.book_contents) {
      console.log(`   Tipo: ${typeof bookWithContent.book_contents}`);
      console.log(`   É array: ${Array.isArray(bookWithContent.book_contents)}`);
      console.log(`   Comprimento: ${bookWithContent.book_contents.length}`);
      
      if (bookWithContent.book_contents.length > 0) {
        const firstContent = bookWithContent.book_contents[0];
        console.log(`   Primeiro conteúdo: ${typeof firstContent.content}`);
        
        if (firstContent.content && firstContent.content.chapters) {
          console.log(`   Capítulos: ${firstContent.content.chapters.length}`);
          console.log(`   Caracteres: ${firstContent.content.total_characters}`);
        }
      }
    }

    console.log('\n4. Testando diferentes variações da consulta...');

    // Variação 1: Sem single()
    console.log('   4a. Testando sem single()...');
    const { data: booksArray, error: arrayError } = await supabase
      .from('books')
      .select(`
        *,
        book_contents (
          content
        )
      `)
      .eq('id', bookId);

    if (arrayError) {
      console.log('   ❌ Erro na consulta array:', arrayError.message);
    } else {
      console.log(`   ✅ Consulta array: ${booksArray.length} resultado(s)`);
      if (booksArray.length > 0 && booksArray[0].book_contents) {
        console.log(`   ✅ book_contents presente: ${booksArray[0].book_contents.length} entrada(s)`);
      }
    }

    // Variação 2: Consulta específica do book_contents
    console.log('   4b. Testando consulta específica do book_contents...');
    const { data: specificContent, error: specificError } = await supabase
      .from('book_contents')
      .select('content')
      .eq('book_id', bookId)
      .single();

    if (specificError) {
      console.log('   ❌ Erro na consulta específica:', specificError.message);
    } else {
      console.log('   ✅ Consulta específica bem-sucedida');
      console.log(`   ✅ Conteúdo: ${typeof specificContent.content}`);
      if (specificContent.content && specificContent.content.chapters) {
        console.log(`   ✅ Capítulos: ${specificContent.content.chapters.length}`);
      }
    }

    console.log('\n5. Verificando estrutura exata que o BookLoader espera...');
    
    // Simular exatamente o que o BookLoader faz
    try {
      const bookLoaderQuery = await supabase
        .from('books')
        .select(`
          *,
          book_contents (
            content
          )
        `)
        .eq('id', bookId)
        .single();

      if (bookLoaderQuery.error) {
        console.log('❌ BookLoader query falhou:', bookLoaderQuery.error.message);
      } else {
        const book = bookLoaderQuery.data;
        console.log('✅ BookLoader query bem-sucedida');
        
        // Verificar se tem conteúdo
        if (book.book_contents && book.book_contents.length > 0) {
          const content = book.book_contents[0].content;
          console.log('✅ Conteúdo encontrado pelo BookLoader');
          console.log(`   Tipo: ${typeof content}`);
          console.log(`   Tem chapters: ${!!(content && content.chapters)}`);
          
          if (content && content.chapters && content.chapters.length > 0) {
            console.log('✅ Estrutura de capítulos válida');
            console.log(`   Primeiro capítulo: "${content.chapters[0].title}"`);
            console.log(`   Tamanho do conteúdo: ${content.chapters[0].content?.length || 0}`);
          } else {
            console.log('❌ Estrutura de capítulos inválida');
          }
        } else {
          console.log('❌ Nenhum conteúdo encontrado pelo BookLoader');
        }
      }
    } catch (error) {
      console.log('❌ Erro no teste do BookLoader:', error.message);
    }

    console.log('\n🎯 DIAGNÓSTICO:');
    if (bookWithContent && bookWithContent.book_contents && bookWithContent.book_contents.length > 0) {
      console.log('✅ A consulta JOIN está funcionando');
      console.log('✅ O conteúdo está presente no banco');
      console.log('💡 O problema pode estar no frontend (React/BookLoader)');
      console.log('🔧 Sugestões:');
      console.log('   1. Verificar se o BookLoader está usando a consulta correta');
      console.log('   2. Verificar se há cache no frontend');
      console.log('   3. Verificar se há filtros ou condições adicionais');
      console.log('   4. Verificar logs do console no navegador');
    } else {
      console.log('❌ A consulta JOIN não está retornando conteúdo');
      console.log('🔧 Possíveis problemas:');
      console.log('   1. Relacionamento entre tabelas');
      console.log('   2. Permissões do Supabase');
      console.log('   3. Estrutura dos dados');
    }

  } catch (error) {
    console.error('💥 Erro durante debug:', error.message);
  }
}

debugBookLoaderQuery();
