import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyMLKUpdate() {
  try {
    console.log('🔍 VERIFICANDO ATUALIZAÇÃO DO LIVRO MLK\n');

    // Verificar o livro ID 85
    const { data: book85, error: error85 } = await supabase
      .from('books')
      .select('*')
      .eq('id', 85)
      .single();

    if (error85) {
      console.log('❌ Erro ao buscar livro ID 85:', error85.message);
    } else {
      console.log('📚 Livro ID 85 (MLK):');
      console.log(`   Título: "${book85.title}"`);
      console.log(`   Autor: "${book85.author}"`);
      console.log(`   Descrição: "${book85.description}"`);
      
      const isCorrect = book85.title.includes('Um Apelo à Consciência') && 
                       book85.author === 'Martin Luther King Jr.';
      
      console.log(`   Status: ${isCorrect ? '✅ CORRETO' : '❌ AINDA INCORRETO'}`);
    }

    // Verificar também o livro ID 36 (o outro MLK)
    const { data: book36, error: error36 } = await supabase
      .from('books')
      .select('*')
      .eq('id', 36)
      .single();

    if (error36) {
      console.log('❌ Erro ao buscar livro ID 36:', error36.message);
    } else {
      console.log('\n📚 Livro ID 36 (MLK original):');
      console.log(`   Título: "${book36.title}"`);
      console.log(`   Autor: "${book36.author}"`);
      console.log(`   Descrição: "${book36.description}"`);
    }

    // Buscar todos os livros do MLK
    console.log('\n🔍 Buscando todos os livros do Martin Luther King...');
    
    const { data: mlkBooks, error: mlkError } = await supabase
      .from('books')
      .select('*')
      .or('author.ilike.%martin%luther%king%,title.ilike.%martin%luther%king%,title.ilike.%apelo%consciencia%');

    if (mlkError) {
      console.log('❌ Erro ao buscar livros MLK:', mlkError.message);
    } else {
      console.log(`📖 Encontrados ${mlkBooks.length} livros relacionados ao MLK:`);
      mlkBooks.forEach(book => {
        console.log(`   ID: ${book.id} - "${book.title}" por ${book.author}`);
      });
    }

    // Verificar se há duplicatas
    const duplicates = mlkBooks.filter(book => 
      book.title.toLowerCase().includes('apelo') || 
      book.title.toLowerCase().includes('martin luther king')
    );

    if (duplicates.length > 1) {
      console.log('\n⚠️ DUPLICATAS ENCONTRADAS:');
      duplicates.forEach(book => {
        console.log(`   ID: ${book.id} - "${book.title}" por ${book.author}`);
      });
      
      console.log('\n💡 RECOMENDAÇÃO:');
      console.log('   Considere manter apenas um livro do MLK e remover duplicatas');
      console.log('   O livro ID 36 já tem conteúdo estruturado');
      console.log('   O livro ID 85 pode ser removido se for duplicata');
    }

    console.log('\n🎯 PRÓXIMOS PASSOS:');
    console.log('1. Se a atualização não funcionou, pode haver restrições de permissão');
    console.log('2. Considere usar o livro ID 36 que já está funcionando');
    console.log('3. Remova duplicatas se necessário');
    console.log('4. Verifique na biblioteca se o livro aparece corretamente');

  } catch (error) {
    console.error('💥 Erro durante verificação:', error);
  }
}

verifyMLKUpdate();
