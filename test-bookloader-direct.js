import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testBookLoaderDirect() {
  try {
    console.log('🧪 TESTANDO BOOKLOADER DIRETAMENTE...\n');

    const testBookId = '7'; // A Interpretação dos Sonhos

    console.log(`📖 Testando livro ID: ${testBookId}`);

    // Simular exatamente o que o BookLoader faz
    console.log('1️⃣ Buscando dados básicos do livro...');
    const { data: bookData, error: bookError } = await supabase
      .from('books')
      .select('*')
      .eq('id', testBookId)
      .single();

    if (bookError || !bookData) {
      console.error('❌ Erro ao carregar dados do livro:', bookError);
      return;
    }

    console.log('✅ Dados do livro carregados:');
    console.log(`   📝 Título: ${bookData.title}`);
    console.log(`   ✍️ Autor: ${bookData.author}`);
    console.log(`   📂 Categoria: ${bookData.category}`);

    console.log('\n2️⃣ Buscando conteúdo do livro...');
    const { data: contentData, error: contentError } = await supabase
      .from('book_contents')
      .select('content')
      .eq('book_id', testBookId)
      .single();

    if (contentError || !contentData) {
      console.error('❌ Erro ao carregar conteúdo:', contentError);
      console.log('⚠️ Isso significa que o BookLoader usará conteúdo fallback');
      return;
    }

    console.log('✅ Conteúdo carregado com sucesso!');
    const content = contentData.content;
    console.log(`   📚 Capítulos: ${content.chapters?.length || 0}`);
    console.log(`   🔑 Pontos-chave: ${content.key_points?.length || 0}`);
    console.log(`   💡 Exercícios: ${content.practical_exercises?.length || 0}`);

    if (content.chapters && content.chapters.length > 0) {
      const firstChapter = content.chapters[0];
      console.log(`\n📄 Primeiro capítulo: "${firstChapter.title}"`);
      console.log(`📋 Conteúdo (primeiros 300 caracteres):`);
      console.log(firstChapter.content.substring(0, 300) + '...');

      // Verificar se é conteúdo específico do Freud
      const isFreudContent = firstChapter.content.toLowerCase().includes('freud') ||
                            firstChapter.content.toLowerCase().includes('sonho') ||
                            firstChapter.content.toLowerCase().includes('inconsciente');
      
      console.log(`\n🎯 Conteúdo específico do Freud detectado: ${isFreudContent ? 'SIM' : 'NÃO'}`);
    }

    // Testar o método getBookText
    console.log('\n3️⃣ Testando conversão para texto...');
    
    const bookContent = {
      id: String(bookData.id),
      title: bookData.title,
      author: bookData.author,
      category: bookData.category,
      pages: bookData.pages || 300,
      duration: bookData.duration || 20,
      difficulty: bookData.difficulty || 'Intermediário',
      description: bookData.description || 'Descrição não disponível',
      content: contentData.content
    };

    // Simular getBookText
    let text = `# ${bookContent.title}\n\n**Por ${bookContent.author}**\n\n`;
    text += `${bookContent.description}\n\n`;
    
    bookContent.content.chapters.forEach(chapter => {
      text += `## ${chapter.title}\n\n`;
      text += `${chapter.content}\n\n`;
    });
    
    if (bookContent.content.key_points.length > 0) {
      text += `## Pontos-Chave\n\n`;
      bookContent.content.key_points.forEach(point => {
        text += `- ${point}\n`;
      });
      text += '\n';
    }

    console.log(`✅ Texto gerado com ${text.length} caracteres`);
    console.log(`📋 Preview do texto final:`);
    console.log(text.substring(0, 500) + '...');

    console.log('\n🎉 TESTE CONCLUÍDO COM SUCESSO!');
    console.log('O BookLoader deve estar funcionando corretamente com conteúdo real.');

  } catch (error) {
    console.error('💥 Erro no teste:', error);
  }
}

testBookLoaderDirect();
