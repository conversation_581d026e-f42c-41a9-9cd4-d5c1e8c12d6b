import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>ap } from 'lucide-react';

export default function HeroSection() {
  return (
    <div className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-gray-50 to-white pattern-grid">
      {/* Beautiful Background Effects */}
      <div className="absolute inset-0">
        <div className="floating-orb absolute top-1/4 left-1/4 w-96 h-96 rounded-full" />
        <div className="floating-orb-2 absolute bottom-1/4 right-1/4 w-64 h-64 rounded-full" />
        <div className="floating-orb-3 absolute top-3/4 left-3/4 w-48 h-48 rounded-full" />
        
        {/* Geometric Elements */}
        <div className="absolute top-20 left-20 w-2 h-2 bg-gray-300 rounded-full animate-pulse-elegant" />
        <div className="absolute top-40 right-32 w-1 h-1 bg-gray-400 rounded-full animate-pulse-elegant animation-delay-200" />
        <div className="absolute bottom-32 left-40 w-1.5 h-1.5 bg-gray-300 rounded-full animate-pulse-elegant animation-delay-500" />
      </div>

      <div className="relative z-10 text-center px-6 max-w-6xl mx-auto">
        {/* Logo */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="flex items-center justify-center mb-16"
        >
          <motion.div 
            className="flex items-center space-x-4"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center shadow-elegant-xl">
              <BookOpen className="w-7 h-7 text-white" />
            </div>
            <span className="text-3xl font-bold text-gray-900 text-elegant">Paretto Estudos</span>
          </motion.div>
        </motion.div>

        {/* Main Headline */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.2, ease: "easeOut" }}
          className="mb-20"
        >
          <motion.div 
            className="inline-flex items-center px-6 py-3 rounded-full glass border border-gray-200 mb-10 shadow-elegant"
            whileHover={{ scale: 1.05, y: -2 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <Brain className="w-5 h-5 mr-3 text-gray-700" />
            <span className="text-gray-800 font-medium">Princípio de Pareto Aplicado</span>
            <Sparkles className="w-4 h-4 ml-3 text-gray-600 animate-pulse-elegant" />
          </motion.div>
          
          <h1 className="text-6xl md:text-8xl font-bold text-gray-900 leading-tight mb-10 text-elegant">
            Obtenha{' '}
            <motion.span 
              className="text-gradient relative"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              95%
              <motion.div
                className="absolute -inset-2 bg-gray-100 rounded-lg -z-10"
                initial={{ scale: 0, opacity: 0 }}
                whileHover={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.2 }}
              />
            </motion.span>{' '}
            do conhecimento
            <br />
            em{' '}
            <motion.span 
              className="text-gradient relative"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              20%
              <motion.div
                className="absolute -inset-2 bg-gray-100 rounded-lg -z-10"
                initial={{ scale: 0, opacity: 0 }}
                whileHover={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.2 }}
              />
            </motion.span>{' '}
            do tempo
          </h1>
          
          <motion.p 
            className="text-xl text-gray-600 leading-relaxed max-w-4xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
          >
            Resumos profissionais criados para extrair todo o conteúdo valioso 
            dos seus livros favoritos.
          </motion.p>
        </motion.div>

        {/* Enhanced CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.4, ease: "easeOut" }}
          className="mb-20"
        >
          <motion.div
            className="glass-card interactive-card rounded-3xl p-12 border border-gray-100 max-w-2xl mx-auto"
            whileHover={{ scale: 1.02, y: -5 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <motion.div 
              className="flex items-center justify-center w-20 h-20 rounded-3xl bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 mx-auto mb-8 shadow-elegant-xl"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Zap className="w-10 h-10 text-white" />
            </motion.div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-4 text-elegant">
              Transforme sua forma de aprender
            </h2>
            
            <p className="text-gray-600 text-lg mb-8 leading-relaxed">
              Acesse o conhecimento essencial de qualquer livro em minutos, 
              não em horas. Ideal para quem busca aprendizado eficiente e prático.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                { icon: BookOpen, text: "Qualquer livro" },
                { icon: Brain, text: "Insights essenciais" },
                { icon: Sparkles, text: "Aprendizado rápido" }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 + index * 0.1, duration: 0.5 }}
                  className="flex flex-col items-center text-center"
                >
                  <div className="w-12 h-12 rounded-xl bg-gray-100 flex items-center justify-center mb-3">
                    <item.icon className="w-6 h-6 text-gray-700" />
                  </div>
                  <span className="text-gray-600 text-sm font-medium">{item.text}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>

        {/* CTA Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 0.8 }}
          className="space-y-6"
        >
          <motion.div
            className="inline-flex items-center space-x-2 text-gray-500"
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
          >
            <Sparkles className="w-4 h-4" />
            <span className="text-sm font-medium">Comece gratuitamente • Primeiro mês com acesso total</span>
            <Sparkles className="w-4 h-4" />
          </motion.div>
          
          <motion.div
            className="flex items-center justify-center space-x-2 text-xs text-gray-400"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1, duration: 0.8 }}
          >
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse-elegant" />
            <span>Sistema em desenvolvimento • Novidades em breve</span>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}