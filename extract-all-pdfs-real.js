import fs from 'fs';
import { processRealPDF } from './extract-real-pdf-text.js';

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

async function extractAllPDFsReal() {
  try {
    console.log('🚀 INICIANDO EXTRAÇÃO REAL DE TODOS OS PDFs');
    console.log('=' * 80);
    console.log('📋 OBJETIVO: Extrair TODO o conteúdo real de cada PDF');
    console.log('🎯 META: Conteúdo integral, todas as páginas, sem limitações\n');
    
    // Listar todos os PDFs
    const files = fs.readdirSync(PDF_FOLDER).filter(file => file.endsWith('.pdf'));
    console.log(`📚 Total de PDFs encontrados: ${files.length}\n`);
    
    // Estatísticas
    let processedCount = 0;
    let errorCount = 0;
    let totalPages = 0;
    let totalCharacters = 0;
    const startTime = Date.now();
    const results = [];
    
    // Processar cada PDF completamente
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const currentTime = Date.now();
      const elapsedTime = Math.round((currentTime - startTime) / 1000);
      
      console.log(`\n📖 [${i + 1}/${files.length}] PROCESSANDO: ${file}`);
      console.log(`⏱️ Tempo decorrido: ${Math.floor(elapsedTime / 60)}m ${elapsedTime % 60}s`);
      console.log(`📊 Progresso: ${((i / files.length) * 100).toFixed(1)}%`);
      
      try {
        const result = await processRealPDF(file);
        
        if (result.success) {
          processedCount++;
          totalPages += result.pages;
          totalCharacters += result.characters;
          
          results.push({
            file,
            title: result.title,
            author: result.author,
            pages: result.pages,
            characters: result.characters,
            chapters: result.chapters
          });
          
          console.log(`✅ [${i + 1}/${files.length}] SUCESSO: ${result.title}`);
          console.log(`   📊 ${result.pages} páginas, ${result.characters.toLocaleString()} caracteres`);
        } else {
          errorCount++;
          console.log(`❌ [${i + 1}/${files.length}] FALHA: ${file}`);
          console.log(`   Erro: ${result.error}`);
        }
        
        // Pequena pausa para não sobrecarregar o sistema
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        errorCount++;
        console.error(`💥 [${i + 1}/${files.length}] ERRO: ${file} - ${error.message}`);
      }
      
      // Log de progresso a cada 5 arquivos
      if ((i + 1) % 5 === 0) {
        const avgTime = Math.round((Date.now() - startTime) / (i + 1) / 1000);
        const remainingFiles = files.length - (i + 1);
        const estimatedTimeLeft = Math.round((remainingFiles * avgTime) / 60);
        
        console.log(`\n📊 PROGRESSO INTERMEDIÁRIO:`);
        console.log(`   ✅ Processados: ${processedCount}/${i + 1}`);
        console.log(`   ❌ Erros: ${errorCount}`);
        console.log(`   📈 Taxa de sucesso: ${((processedCount / (i + 1)) * 100).toFixed(1)}%`);
        console.log(`   ⏱️ Tempo médio por PDF: ${avgTime}s`);
        console.log(`   🕐 Tempo estimado restante: ${estimatedTimeLeft}min`);
        console.log(`   📄 Total de páginas extraídas: ${totalPages.toLocaleString()}`);
        console.log(`   📝 Total de caracteres extraídos: ${totalCharacters.toLocaleString()}`);
      }
    }
    
    const endTime = Date.now();
    const totalTime = Math.round((endTime - startTime) / 1000);
    
    console.log('\n' + '=' * 80);
    console.log('🎉 EXTRAÇÃO REAL FINALIZADA!');
    console.log('=' * 80);
    
    console.log(`\n📊 ESTATÍSTICAS FINAIS:`);
    console.log(`   📚 Total de PDFs: ${files.length}`);
    console.log(`   ✅ Processados com sucesso: ${processedCount}`);
    console.log(`   ❌ Erros: ${errorCount}`);
    console.log(`   📈 Taxa de sucesso: ${((processedCount / files.length) * 100).toFixed(1)}%`);
    console.log(`   ⏱️ Tempo total: ${Math.floor(totalTime / 60)}m ${totalTime % 60}s`);
    console.log(`   ⚡ Tempo médio por PDF: ${Math.round(totalTime / files.length)}s`);
    console.log(`   📄 Total de páginas extraídas: ${totalPages.toLocaleString()}`);
    console.log(`   📝 Total de caracteres extraídos: ${totalCharacters.toLocaleString()}`);
    console.log(`   📖 Média de páginas por livro: ${Math.round(totalPages / processedCount)}`);
    console.log(`   📝 Média de caracteres por livro: ${Math.round(totalCharacters / processedCount).toLocaleString()}`);
    
    if (processedCount > 0) {
      console.log(`\n🎯 RESULTADO:`);
      console.log(`   📖 ${processedCount} livros agora têm conteúdo REAL extraído dos PDFs`);
      console.log(`   📄 ${totalPages.toLocaleString()} páginas de conteúdo real disponível`);
      console.log(`   📝 ${totalCharacters.toLocaleString()} caracteres de texto real`);
      console.log(`   🏗️ Estrutura original preservada (capítulos, seções)`);
      console.log(`   🔍 Conteúdo integral disponível para leitura`);
      
      console.log(`\n📚 LIVROS PROCESSADOS COM SUCESSO:`);
      results.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.title} - ${result.author}`);
        console.log(`      📄 ${result.pages} páginas, ${result.characters.toLocaleString()} chars, ${result.chapters} capítulos`);
      });
    }
    
    if (errorCount > 0) {
      console.log(`\n⚠️ ATENÇÃO:`);
      console.log(`   ${errorCount} PDFs tiveram problemas na extração`);
      console.log(`   Verifique os logs acima para detalhes dos erros`);
    }
    
    console.log(`\n🚀 PRÓXIMOS PASSOS:`);
    console.log(`   1. Teste a funcionalidade no navegador`);
    console.log(`   2. Faça login na aplicação`);
    console.log(`   3. Clique em "Ler Agora" em qualquer livro`);
    console.log(`   4. Verifique se o conteúdo REAL do PDF está sendo exibido`);
    console.log(`   5. Confirme que todas as páginas estão disponíveis`);
    
    console.log(`\n✨ MISSÃO CUMPRIDA!`);
    console.log(`Conteúdo integral de ${processedCount} PDFs extraído e disponível!`);
    console.log(`Total: ${totalCharacters.toLocaleString()} caracteres de ${totalPages.toLocaleString()} páginas reais!`);
    
  } catch (error) {
    console.error('💥 Erro geral na extração:', error);
  }
}

// Executar a extração completa
extractAllPDFsReal();
