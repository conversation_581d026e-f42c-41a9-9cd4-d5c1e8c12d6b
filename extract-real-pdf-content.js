import fs from 'fs';
import path from 'path';
import * as pdfjs from 'pdfjs-dist/legacy/build/pdf.js';
import { createClient } from '@supabase/supabase-js';

// Configure PDF.js
pdfjs.GlobalWorkerOptions.workerSrc = 'pdfjs-dist/legacy/build/pdf.worker.js';

// Function to extract text from PDF using pdfjs-dist
async function extractTextFromPDF(dataBuffer) {
  try {
    const loadingTask = pdfjs.getDocument({ data: dataBuffer });
    const pdf = await loadingTask.promise;

    let fullText = '';

    // Extract text from all pages
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();

      const pageText = textContent.items
        .map(item => item.str)
        .join(' ');

      fullText += pageText + '\n';
    }

    return fullText.trim();
  } catch (error) {
    console.error('Error extracting PDF text:', error);
    return '';
  }
}

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

// Função para extrair metadados reais do PDF
function extractRealBookInfo(filename, pdfText) {
  const nameWithoutExt = filename.replace('.pdf', '');
  
  // Tentar extrair título e autor do texto do PDF
  const lines = pdfText.split('\n').filter(line => line.trim().length > 0);
  
  // Procurar por padrões de título e autor no início do texto
  let extractedTitle = '';
  let extractedAuthor = '';
  
  // Procurar título (geralmente nas primeiras linhas, em maiúsculas ou com formatação especial)
  for (let i = 0; i < Math.min(10, lines.length); i++) {
    const line = lines[i].trim();
    if (line.length > 10 && line.length < 100) {
      // Se a linha parece ser um título (maiúsculas, ou contém palavras-chave)
      if (line.toUpperCase() === line || 
          line.includes('RESUMO') || 
          line.includes('LIVRO') ||
          /^[A-Z][^a-z]*[A-Z]$/.test(line)) {
        extractedTitle = line.replace(/RESUMO\s*:?\s*/i, '').replace(/LIVRO\s*:?\s*/i, '').trim();
        break;
      }
    }
  }
  
  // Procurar autor (geralmente após o título, ou contém "por", "autor", etc.)
  for (let i = 0; i < Math.min(15, lines.length); i++) {
    const line = lines[i].trim().toLowerCase();
    if (line.includes('autor:') || line.includes('por:') || line.includes('de:')) {
      extractedAuthor = lines[i].replace(/autor:?/i, '').replace(/por:?/i, '').replace(/de:?/i, '').trim();
      break;
    }
  }
  
  // Mapeamento manual para casos específicos conhecidos
  const manualMappings = {
    'a_interpretacao_dos_sonhos_sigmund_freud_3': {
      title: 'A Interpretação dos Sonhos',
      author: 'Sigmund Freud',
      category: 'Psicologia'
    },
    'a_sutil_arte_de_ligar_o_foda_se_mark_manson_3': {
      title: 'A Sutil Arte de Ligar o F*da-se',
      author: 'Mark Manson',
      category: 'Autoajuda'
    },
    'o_poder_do_habito_charles_duhigg_3': {
      title: 'O Poder do Hábito',
      author: 'Charles Duhigg',
      category: 'Produtividade'
    },
    'pai_rico_pai_pobre_robert_t_kiyosaki_3': {
      title: 'Pai Rico, Pai Pobre',
      author: 'Robert Kiyosaki',
      category: 'Finanças'
    },
    'habitos_atomicos_james_clear_5': {
      title: 'Hábitos Atômicos',
      author: 'James Clear',
      category: 'Produtividade'
    }
  };
  
  const manual = manualMappings[nameWithoutExt];
  
  return {
    title: manual?.title || extractedTitle || nameWithoutExt.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    author: manual?.author || extractedAuthor || 'Autor Desconhecido',
    category: manual?.category || 'Geral',
    description: `Resumo profissional extraído do PDF original.`,
    duration: Math.max(15, Math.min(60, Math.floor(pdfText.length / 100))), // Baseado no tamanho real do texto
    difficulty: pdfText.length > 5000 ? 'Avançado' : pdfText.length > 2000 ? 'Intermediário' : 'Fácil'
  };
}

// Função para extrair estrutura real do conteúdo
function extractRealContentStructure(pdfText) {
  const lines = pdfText.split('\n').filter(line => line.trim().length > 0);
  const cleanText = pdfText.replace(/\s+/g, ' ').trim();
  
  // Dividir o texto em seções baseadas em padrões reais
  const sections = [];
  let currentSection = '';
  let sectionTitle = '';
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Detectar títulos de seção (linhas curtas, maiúsculas, ou com números)
    if (line.length < 100 && (
        /^\d+\./.test(line) || // Começa com número
        /^[A-Z\s]{5,50}$/.test(line) || // Maiúsculas
        line.includes('CAPÍTULO') ||
        line.includes('PARTE') ||
        line.includes('SEÇÃO')
    )) {
      // Salvar seção anterior
      if (currentSection.length > 100) {
        sections.push({
          title: sectionTitle || `Seção ${sections.length + 1}`,
          content: currentSection.trim()
        });
      }
      
      // Iniciar nova seção
      sectionTitle = line;
      currentSection = '';
    } else {
      currentSection += line + ' ';
    }
  }
  
  // Adicionar última seção
  if (currentSection.length > 100) {
    sections.push({
      title: sectionTitle || `Seção ${sections.length + 1}`,
      content: currentSection.trim()
    });
  }
  
  // Se não encontrou seções estruturadas, dividir por tamanho
  if (sections.length === 0) {
    const chunkSize = Math.floor(cleanText.length / 3);
    sections.push(
      {
        title: 'Introdução e Conceitos Fundamentais',
        content: cleanText.substring(0, chunkSize)
      },
      {
        title: 'Desenvolvimento e Análise',
        content: cleanText.substring(chunkSize, chunkSize * 2)
      },
      {
        title: 'Conclusões e Aplicações',
        content: cleanText.substring(chunkSize * 2)
      }
    );
  }
  
  // Extrair pontos-chave reais do texto
  const keyPoints = extractRealKeyPoints(cleanText);
  
  // Extrair exercícios práticos se existirem
  const practicalExercises = extractRealExercises(cleanText);
  
  return {
    chapters: sections.slice(0, 5).map((section, index) => ({
      id: `chapter${index + 1}`,
      title: section.title,
      content: section.content
    })),
    key_points: keyPoints,
    practical_exercises: practicalExercises
  };
}

// Função para extrair pontos-chave reais do texto
function extractRealKeyPoints(text) {
  const keyPoints = [];
  
  // Procurar por listas, pontos numerados, ou frases importantes
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
  
  // Procurar por padrões que indicam pontos importantes
  const importantPatterns = [
    /é importante/i,
    /fundamental/i,
    /essencial/i,
    /princípio/i,
    /conceito/i,
    /estratégia/i,
    /método/i,
    /técnica/i
  ];
  
  for (const sentence of sentences) {
    if (importantPatterns.some(pattern => pattern.test(sentence))) {
      const cleanSentence = sentence.trim();
      if (cleanSentence.length > 30 && cleanSentence.length < 200) {
        keyPoints.push(cleanSentence);
        if (keyPoints.length >= 7) break;
      }
    }
  }
  
  // Se não encontrou pontos específicos, extrair frases representativas
  if (keyPoints.length < 3) {
    const representativeSentences = sentences
      .filter(s => s.length > 50 && s.length < 150)
      .slice(0, 5);
    keyPoints.push(...representativeSentences);
  }
  
  return keyPoints.slice(0, 7);
}

// Função para extrair exercícios práticos reais
function extractRealExercises(text) {
  const exercises = [];
  
  // Procurar por seções de exercícios, práticas, ou aplicações
  const exercisePatterns = [
    /exercício/i,
    /prática/i,
    /aplicação/i,
    /experimente/i,
    /faça/i,
    /tente/i,
    /implemente/i
  ];
  
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
  
  for (const sentence of sentences) {
    if (exercisePatterns.some(pattern => pattern.test(sentence))) {
      const cleanSentence = sentence.trim();
      if (cleanSentence.length > 30 && cleanSentence.length < 200) {
        exercises.push(cleanSentence);
        if (exercises.length >= 5) break;
      }
    }
  }
  
  // Se não encontrou exercícios específicos, criar baseado no conteúdo
  if (exercises.length === 0) {
    exercises.push(
      'Reflita sobre os conceitos apresentados e como se aplicam à sua situação',
      'Identifique exemplos práticos dos princípios discutidos no texto',
      'Analise como os insights podem ser implementados em sua vida',
      'Discuta os pontos principais com outras pessoas para aprofundar a compreensão'
    );
  }
  
  return exercises.slice(0, 5);
}

// Função principal para processar todos os PDFs com conteúdo real
async function extractAllRealPDFContent() {
  try {
    console.log('🚀 INICIANDO EXTRAÇÃO REAL DE CONTEÚDO DOS PDFs...\n');
    
    const files = fs.readdirSync(PDF_FOLDER).filter(file => file.endsWith('.pdf'));
    console.log(`📚 Total de ${files.length} PDFs para processar\n`);

    let processedCount = 0;
    let errorCount = 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`📖 [${i + 1}/${files.length}] Processando: ${file}`);
      
      try {
        // Ler e extrair texto do PDF
        const pdfPath = path.join(PDF_FOLDER, file);
        const dataBuffer = fs.readFileSync(pdfPath);

        console.log('   📄 Extraindo texto do PDF...');
        const pdfText = await extractTextFromPDF(dataBuffer);
        
        if (!pdfText || pdfText.length < 100) {
          console.log('   ⚠️ PDF vazio ou com pouco conteúdo, pulando...');
          continue;
        }
        
        console.log(`   ✅ Texto extraído: ${pdfText.length} caracteres`);
        
        // Extrair informações reais do livro
        const bookInfo = extractRealBookInfo(file, pdfText);
        console.log(`   📝 Título: ${bookInfo.title}`);
        console.log(`   ✍️ Autor: ${bookInfo.author}`);
        console.log(`   ⏱️ Duração estimada: ${bookInfo.duration} min`);
        
        // Extrair estrutura real do conteúdo
        console.log('   🏗️ Estruturando conteúdo real...');
        const realContent = extractRealContentStructure(pdfText);
        console.log(`   📚 Capítulos extraídos: ${realContent.chapters.length}`);
        console.log(`   🔑 Pontos-chave: ${realContent.key_points.length}`);
        console.log(`   💡 Exercícios: ${realContent.practical_exercises.length}`);
        
        // Verificar se o livro já existe no banco
        const { data: existingBook, error: searchError } = await supabase
          .from('books')
          .select('id')
          .eq('title', bookInfo.title)
          .single();

        let bookId;
        
        if (existingBook) {
          bookId = existingBook.id;
          console.log(`   ✅ Livro encontrado no banco (ID: ${bookId})`);
          
          // Atualizar informações do livro com dados reais
          const { error: updateError } = await supabase
            .from('books')
            .update({
              author: bookInfo.author,
              category: bookInfo.category,
              description: bookInfo.description,
              duration: bookInfo.duration,
              difficulty: bookInfo.difficulty,
              pdf_key: file
            })
            .eq('id', bookId);
            
          if (updateError) {
            console.log(`   ⚠️ Erro ao atualizar livro: ${updateError.message}`);
          }
        } else {
          // Inserir novo livro
          const { data: newBook, error: insertError } = await supabase
            .from('books')
            .insert({
              title: bookInfo.title,
              author: bookInfo.author,
              category: bookInfo.category,
              description: bookInfo.description,
              duration: bookInfo.duration,
              difficulty: bookInfo.difficulty,
              is_featured: true,
              is_free: true,
              pdf_key: file
            })
            .select('id')
            .single();

          if (insertError) {
            console.error(`   ❌ Erro ao inserir livro: ${insertError.message}`);
            errorCount++;
            continue;
          }
          
          bookId = newBook.id;
          console.log(`   ✅ Novo livro criado (ID: ${bookId})`);
        }

        // Inserir ou atualizar conteúdo REAL
        const { error: contentError } = await supabase
          .from('book_contents')
          .upsert({
            book_id: bookId,
            content: realContent
          });

        if (contentError) {
          console.error(`   ❌ Erro ao inserir conteúdo: ${contentError.message}`);
          errorCount++;
        } else {
          console.log(`   ✅ CONTEÚDO REAL inserido com sucesso!`);
          processedCount++;
        }

        console.log(''); // Linha em branco

      } catch (error) {
        console.error(`   ❌ Erro ao processar ${file}: ${error.message}`);
        errorCount++;
        console.log('');
      }
    }

    console.log('🎉 EXTRAÇÃO DE CONTEÚDO REAL CONCLUÍDA!');
    console.log(`✅ PDFs processados com sucesso: ${processedCount}`);
    console.log(`❌ Erros encontrados: ${errorCount}`);
    console.log(`📊 Taxa de sucesso: ${((processedCount / files.length) * 100).toFixed(1)}%`);
    
  } catch (error) {
    console.error('💥 Erro geral na extração:', error);
  }
}

// Executar a extração
extractAllRealPDFContent();
