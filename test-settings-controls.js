import puppeteer from 'puppeteer';

async function testSettingsControls() {
  let browser;
  try {
    console.log('⚙️ TESTANDO CONTROLES DE CONFIGURAÇÕES\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Fazer login rápido se necessário
    try {
      const loginButton = await page.$('button:has-text("Entrar"), a:has-text("Entrar")');
      if (loginButton) {
        await loginButton.click();
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await page.type('input[type="email"]', '<EMAIL>', { delay: 50 });
        await page.type('input[type="password"]', 'password123', { delay: 50 });
        
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
          await submitButton.click();
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    } catch (error) {
      console.log('⚠️ Login não necessário ou já logado');
    }

    // Navegar para biblioteca e abrir um livro
    console.log('📚 Navegando para biblioteca...');
    await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a, button'));
      const libraryLink = links.find(link => {
        const text = link.textContent?.toLowerCase() || '';
        return text.includes('biblioteca');
      });
      if (libraryLink) libraryLink.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Abrir um livro
    console.log('📖 Abrindo um livro...');
    const bookOpened = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const readButton = buttons.find(btn => {
        const text = btn.textContent?.toLowerCase() || '';
        return text.includes('ler');
      });
      
      if (readButton) {
        readButton.click();
        return true;
      }
      return false;
    });

    if (!bookOpened) {
      console.log('❌ Não foi possível abrir um livro');
      return;
    }

    console.log('✅ Livro aberto, aguardando carregamento...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Abrir painel de configurações
    console.log('⚙️ Abrindo painel de configurações...');
    const settingsOpened = await page.evaluate(() => {
      // Procurar pelo botão de configurações (ícone de engrenagem)
      const buttons = Array.from(document.querySelectorAll('button'));
      const settingsButton = buttons.find(btn => {
        const svg = btn.querySelector('svg');
        return svg && (btn.title?.includes('config') || btn.getAttribute('aria-label')?.includes('config'));
      });
      
      if (settingsButton) {
        settingsButton.click();
        return true;
      }
      
      // Tentar encontrar por posição (último botão da barra superior)
      const topButtons = buttons.filter(btn => {
        const rect = btn.getBoundingClientRect();
        return rect.top < 100; // Botões na parte superior
      });
      
      if (topButtons.length > 0) {
        topButtons[0].click(); // Primeiro botão (pode ser configurações)
        return true;
      }
      
      return false;
    });

    if (!settingsOpened) {
      console.log('❌ Não foi possível abrir o painel de configurações');
      return;
    }

    console.log('✅ Painel de configurações aberto');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Testar controles de tema
    console.log('\n🎨 TESTANDO CONTROLES DE TEMA...');
    
    const themeTests = await page.evaluate(() => {
      const results = [];
      
      // Encontrar botões de tema
      const themeButtons = Array.from(document.querySelectorAll('button')).filter(btn => {
        const text = btn.textContent?.toLowerCase() || '';
        return text.includes('claro') || text.includes('escuro') || text.includes('sépia');
      });
      
      results.push({
        test: 'Botões de tema encontrados',
        success: themeButtons.length >= 3,
        details: `${themeButtons.length} botões encontrados`
      });
      
      // Testar clique no tema escuro
      const darkButton = themeButtons.find(btn => 
        btn.textContent?.toLowerCase().includes('escuro')
      );
      
      if (darkButton) {
        darkButton.click();
        results.push({
          test: 'Clique no tema escuro',
          success: true,
          details: 'Botão clicado'
        });
      } else {
        results.push({
          test: 'Clique no tema escuro',
          success: false,
          details: 'Botão não encontrado'
        });
      }
      
      return results;
    });

    themeTests.forEach(test => {
      console.log(`${test.success ? '✅' : '❌'} ${test.test}: ${test.details}`);
    });

    await new Promise(resolve => setTimeout(resolve, 2000));

    // Testar controles de fonte
    console.log('\n🔤 TESTANDO CONTROLES DE FONTE...');
    
    const fontTests = await page.evaluate(() => {
      const results = [];
      
      // Encontrar controles de fonte
      const fontControls = Array.from(document.querySelectorAll('button')).filter(btn => {
        const svg = btn.querySelector('svg');
        const hasMinusIcon = svg && svg.innerHTML.includes('minus');
        const hasPlusIcon = svg && svg.innerHTML.includes('plus');
        return hasMinusIcon || hasPlusIcon;
      });
      
      results.push({
        test: 'Controles de fonte encontrados',
        success: fontControls.length >= 2,
        details: `${fontControls.length} controles encontrados`
      });
      
      // Encontrar display de tamanho da fonte
      const fontDisplay = Array.from(document.querySelectorAll('span')).find(span => {
        const text = span.textContent || '';
        return text.includes('pt') && /\d+pt/.test(text);
      });
      
      results.push({
        test: 'Display de tamanho da fonte',
        success: !!fontDisplay,
        details: fontDisplay ? fontDisplay.textContent : 'Não encontrado'
      });
      
      // Testar clique no botão de aumentar fonte
      const plusButton = fontControls.find(btn => {
        const svg = btn.querySelector('svg');
        return svg && svg.innerHTML.includes('plus');
      });
      
      if (plusButton) {
        const beforeText = fontDisplay?.textContent || '';
        plusButton.click();
        
        // Aguardar um pouco para a mudança
        setTimeout(() => {
          const afterText = fontDisplay?.textContent || '';
          results.push({
            test: 'Aumento de fonte',
            success: afterText !== beforeText,
            details: `${beforeText} → ${afterText}`
          });
        }, 500);
      }
      
      return results;
    });

    fontTests.forEach(test => {
      console.log(`${test.success ? '✅' : '❌'} ${test.test}: ${test.details}`);
    });

    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verificar se o controle de espaçamento de linha foi removido
    console.log('\n📏 VERIFICANDO REMOÇÃO DO CONTROLE DE ESPAÇAMENTO...');
    
    const lineHeightCheck = await page.evaluate(() => {
      const text = document.body.innerText.toLowerCase();
      const hasLineHeightControl = text.includes('espaçamento entre linhas') || 
                                   text.includes('line height') ||
                                   text.includes('1.4x') ||
                                   text.includes('1.6x');
      
      return {
        removed: !hasLineHeightControl,
        details: hasLineHeightControl ? 'Ainda presente' : 'Removido com sucesso'
      };
    });

    console.log(`${lineHeightCheck.removed ? '✅' : '❌'} Controle de espaçamento removido: ${lineHeightCheck.details}`);

    // Resumo final
    console.log('\n📊 RESUMO DOS TESTES:');
    const allTests = [
      ...themeTests,
      ...fontTests,
      { test: 'Remoção espaçamento', success: lineHeightCheck.removed, details: lineHeightCheck.details }
    ];

    const passedTests = allTests.filter(test => test.success).length;
    const totalTests = allTests.length;

    console.log(`✅ Testes aprovados: ${passedTests}/${totalTests}`);
    console.log(`📈 Taxa de sucesso: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (passedTests === totalTests) {
      console.log('\n🎉 TODOS OS CONTROLES FUNCIONANDO PERFEITAMENTE!');
    } else if (passedTests >= totalTests * 0.8) {
      console.log('\n✅ MAIORIA DOS CONTROLES FUNCIONANDO!');
    } else {
      console.log('\n⚠️ ALGUNS CONTROLES PRECISAM DE AJUSTES');
    }

    console.log('\n🚀 TESTE MANUAL RECOMENDADO:');
    console.log('1. Clique no ícone de configurações (engrenagem)');
    console.log('2. Teste os botões de tema (Claro, Escuro, Sépia)');
    console.log('3. Teste os controles de fonte (+/-)');
    console.log('4. Verifique se não há controle de espaçamento de linha');
    console.log('5. Confirme que as mudanças são aplicadas imediatamente');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testSettingsControls();
