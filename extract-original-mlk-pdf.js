import fs from 'fs';
import pdf from 'pdf-parse';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function extractOriginalMLKPDF() {
  try {
    console.log('📄 EXTRACTING ORIGINAL MLK PDF CONTENT\n');

    const pdfPath = 'C:\\Users\\<USER>\\.claude\\book\\resumos_padronizados_roboto_final\\home\\ubuntu\\resumos_padronizados\\pdf_final\\um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf';
    
    console.log('1. Reading PDF file...');
    console.log(`   Path: ${pdfPath}`);

    // Check if file exists
    if (!fs.existsSync(pdfPath)) {
      console.log('❌ PDF file not found at specified path');
      return;
    }

    // Read the PDF file
    const dataBuffer = fs.readFileSync(pdfPath);
    console.log(`✅ PDF file read successfully (${dataBuffer.length} bytes)`);

    console.log('2. Extracting text from PDF...');
    
    // Parse PDF content
    const pdfData = await pdf(dataBuffer);
    
    console.log(`✅ PDF parsed successfully`);
    console.log(`   Pages: ${pdfData.numpages}`);
    console.log(`   Text length: ${pdfData.text.length} characters`);

    // Clean and format the extracted text
    console.log('3. Cleaning and formatting extracted text...');
    
    let cleanText = pdfData.text
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove page numbers and headers/footers
      .replace(/\d+\s*$/gm, '')
      // Fix common PDF extraction issues
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      // Normalize line breaks
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Clean up multiple spaces
      .replace(/  +/g, ' ')
      .trim();

    console.log(`✅ Text cleaned (${cleanText.length} characters)`);

    // Preview the content
    console.log('\n📋 CONTENT PREVIEW:');
    console.log(cleanText.substring(0, 1000) + '...');

    // Create the content structure for the database
    console.log('4. Creating content structure...');
    
    const contentStructure = {
      chapters: [
        {
          title: "Um Apelo à Consciência: Os Melhores Discursos de Martin Luther King",
          content: cleanText
        }
      ],
      key_points: [
        "Análise completa dos discursos mais importantes de Martin Luther King Jr.",
        "Contexto histórico do movimento pelos direitos civis",
        "Filosofia de resistência não-violenta",
        "Impacto dos discursos na sociedade americana",
        "Legado duradouro de Martin Luther King Jr."
      ],
      practical_exercises: [
        "Analise o contexto histórico dos discursos",
        "Compare diferentes estratégias de ativismo",
        "Reflita sobre a relevância contemporânea das mensagens",
        "Estude a retórica e técnicas de persuasão utilizadas"
      ],
      total_characters: cleanText.length,
      total_pages: pdfData.numpages
    };

    console.log('5. Updating database with original content...');
    
    const bookId = 85; // MLK book ID

    // Remove existing content
    const { error: deleteError } = await supabase
      .from('book_contents')
      .delete()
      .eq('book_id', bookId);

    if (deleteError) {
      console.log('❌ Error deleting existing content:', deleteError.message);
      return;
    }

    // Insert original PDF content
    const { error: insertError } = await supabase
      .from('book_contents')
      .insert({
        book_id: bookId,
        content: contentStructure
      });

    if (insertError) {
      console.log('❌ Error inserting content:', insertError.message);
      return;
    }

    console.log('✅ Original content inserted successfully');

    // Update book description
    const { error: updateError } = await supabase
      .from('books')
      .update({
        description: `Conteúdo extraído do PDF (${cleanText.length.toLocaleString()} caracteres).`
      })
      .eq('id', bookId);

    if (updateError) {
      console.log('❌ Error updating description:', updateError.message);
    } else {
      console.log('✅ Book description updated');
    }

    console.log('\n🎉 ORIGINAL MLK PDF CONTENT RESTORED!');
    console.log('');
    console.log('📊 CONTENT STATISTICS:');
    console.log(`   Original PDF Pages: ${pdfData.numpages}`);
    console.log(`   Extracted Characters: ${cleanText.length.toLocaleString()}`);
    console.log(`   Content Type: Original PDF Summary`);
    console.log('');
    console.log('✅ RESULT:');
    console.log('   Users will now see the actual original PDF content');
    console.log('   Content maintains Kindle-style formatting');
    console.log('   Authentic PDF summary as expected by users');
    console.log('   Professional presentation with proper typography');

  } catch (error) {
    console.error('💥 Error extracting PDF:', error.message);
  }
}

extractOriginalMLKPDF();
