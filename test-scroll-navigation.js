import puppeteer from 'puppeteer';

async function testScrollNavigation() {
  let browser;
  try {
    console.log('🔄 TESTANDO FUNCIONALIDADE DE SCROLL E NAVEGAÇÃO\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Fazer login rápido (se necessário)
    try {
      const loginButton = await page.$('button:has-text("Entrar"), a:has-text("Entrar")');
      if (loginButton) {
        await loginButton.click();
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        await page.type('input[type="email"]', '<EMAIL>', { delay: 50 });
        await page.type('input[type="password"]', 'password123', { delay: 50 });
        
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
          await submitButton.click();
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    } catch (error) {
      console.log('⚠️ Login não necessário ou já logado');
    }

    // Navegar para biblioteca
    console.log('📚 Navegando para biblioteca...');
    await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a, button'));
      const libraryLink = links.find(link => {
        const text = link.textContent?.toLowerCase() || '';
        return text.includes('biblioteca');
      });
      if (libraryLink) libraryLink.click();
    });
    
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Tentar abrir um livro
    console.log('📖 Tentando abrir um livro...');
    const bookOpened = await page.evaluate(() => {
      // Procurar por botões "Ler Agora"
      const buttons = Array.from(document.querySelectorAll('button'));
      const readButton = buttons.find(btn => {
        const text = btn.textContent?.toLowerCase() || '';
        return text.includes('ler');
      });
      
      if (readButton) {
        readButton.click();
        return true;
      }
      return false;
    });

    if (!bookOpened) {
      console.log('❌ Não foi possível abrir um livro');
      return;
    }

    console.log('✅ Livro aberto, aguardando carregamento...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Testar funcionalidades de scroll e navegação
    console.log('🧪 Testando funcionalidades de navegação...\n');

    // 1. Testar scroll com mouse wheel
    console.log('1️⃣ Testando scroll com mouse wheel...');
    const initialScrollPosition = await page.evaluate(() => window.pageYOffset);
    
    await page.mouse.wheel({ deltaY: 500 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const scrollAfterWheel = await page.evaluate(() => window.pageYOffset);
    const wheelScrollWorked = scrollAfterWheel > initialScrollPosition;
    console.log(`   Scroll inicial: ${initialScrollPosition}px`);
    console.log(`   Scroll após wheel: ${scrollAfterWheel}px`);
    console.log(`   ✅ Mouse wheel: ${wheelScrollWorked ? 'FUNCIONANDO' : 'NÃO FUNCIONANDO'}`);

    // 2. Testar navegação por teclado - Seta para baixo
    console.log('\n2️⃣ Testando navegação por teclado (seta para baixo)...');
    const scrollBeforeArrow = await page.evaluate(() => window.pageYOffset);
    
    await page.keyboard.press('ArrowDown');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const scrollAfterArrow = await page.evaluate(() => window.pageYOffset);
    const arrowScrollWorked = scrollAfterArrow > scrollBeforeArrow;
    console.log(`   Scroll antes da seta: ${scrollBeforeArrow}px`);
    console.log(`   Scroll após seta: ${scrollAfterArrow}px`);
    console.log(`   ✅ Seta para baixo: ${arrowScrollWorked ? 'FUNCIONANDO' : 'NÃO FUNCIONANDO'}`);

    // 3. Testar Page Down
    console.log('\n3️⃣ Testando Page Down...');
    const scrollBeforePageDown = await page.evaluate(() => window.pageYOffset);
    
    await page.keyboard.press('PageDown');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const scrollAfterPageDown = await page.evaluate(() => window.pageYOffset);
    const pageDownWorked = scrollAfterPageDown > scrollBeforePageDown;
    console.log(`   Scroll antes Page Down: ${scrollBeforePageDown}px`);
    console.log(`   Scroll após Page Down: ${scrollAfterPageDown}px`);
    console.log(`   ✅ Page Down: ${pageDownWorked ? 'FUNCIONANDO' : 'NÃO FUNCIONANDO'}`);

    // 4. Testar Spacebar
    console.log('\n4️⃣ Testando Spacebar...');
    const scrollBeforeSpace = await page.evaluate(() => window.pageYOffset);
    
    await page.keyboard.press('Space');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const scrollAfterSpace = await page.evaluate(() => window.pageYOffset);
    const spaceWorked = scrollAfterSpace > scrollBeforeSpace;
    console.log(`   Scroll antes Spacebar: ${scrollBeforeSpace}px`);
    console.log(`   Scroll após Spacebar: ${scrollAfterSpace}px`);
    console.log(`   ✅ Spacebar: ${spaceWorked ? 'FUNCIONANDO' : 'NÃO FUNCIONANDO'}`);

    // 5. Testar Home (voltar ao topo)
    console.log('\n5️⃣ Testando Home (voltar ao topo)...');
    await page.keyboard.press('Home');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const scrollAfterHome = await page.evaluate(() => window.pageYOffset);
    const homeWorked = scrollAfterHome === 0;
    console.log(`   Scroll após Home: ${scrollAfterHome}px`);
    console.log(`   ✅ Home: ${homeWorked ? 'FUNCIONANDO' : 'NÃO FUNCIONANDO'}`);

    // 6. Testar End (ir para o final)
    console.log('\n6️⃣ Testando End (ir para o final)...');
    await page.keyboard.press('End');
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const scrollAfterEnd = await page.evaluate(() => window.pageYOffset);
    const maxScroll = await page.evaluate(() => 
      document.documentElement.scrollHeight - window.innerHeight
    );
    const endWorked = scrollAfterEnd >= maxScroll * 0.9; // 90% do máximo
    console.log(`   Scroll após End: ${scrollAfterEnd}px`);
    console.log(`   Scroll máximo: ${maxScroll}px`);
    console.log(`   ✅ End: ${endWorked ? 'FUNCIONANDO' : 'NÃO FUNCIONANDO'}`);

    // 7. Verificar se o progresso está sendo atualizado
    console.log('\n7️⃣ Verificando atualização do progresso...');
    const progressElement = await page.$('[class*="progress"], [class*="concluído"]');
    const hasProgress = !!progressElement;
    console.log(`   ✅ Indicador de progresso: ${hasProgress ? 'PRESENTE' : 'AUSENTE'}`);

    // Resumo final
    console.log('\n📊 RESUMO DOS TESTES:');
    const allTests = [
      { name: 'Mouse Wheel', working: wheelScrollWorked },
      { name: 'Seta para baixo', working: arrowScrollWorked },
      { name: 'Page Down', working: pageDownWorked },
      { name: 'Spacebar', working: spaceWorked },
      { name: 'Home', working: homeWorked },
      { name: 'End', working: endWorked },
      { name: 'Indicador de progresso', working: hasProgress }
    ];

    const workingTests = allTests.filter(test => test.working).length;
    const totalTests = allTests.length;

    allTests.forEach(test => {
      console.log(`   ${test.working ? '✅' : '❌'} ${test.name}`);
    });

    console.log(`\n🎯 RESULTADO FINAL: ${workingTests}/${totalTests} funcionalidades funcionando`);
    
    if (workingTests === totalTests) {
      console.log('🎉 PERFEITO! Todas as funcionalidades de navegação estão funcionando!');
    } else if (workingTests >= totalTests * 0.7) {
      console.log('✅ BOM! A maioria das funcionalidades está funcionando.');
    } else {
      console.log('⚠️ PROBLEMAS: Várias funcionalidades não estão funcionando.');
    }

    console.log('\n🚀 TESTE MANUAL:');
    console.log('Agora você pode testar manualmente:');
    console.log('• Use o scroll do mouse para navegar');
    console.log('• Use as setas do teclado (↑↓) para navegar');
    console.log('• Use Page Up/Page Down para navegação rápida');
    console.log('• Use Spacebar para avançar');
    console.log('• Use Home/End para ir ao início/fim');
    console.log('• Use Esc para fechar o leitor');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testScrollNavigation();
