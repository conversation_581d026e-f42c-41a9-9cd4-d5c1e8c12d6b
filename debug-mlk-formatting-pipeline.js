import puppeteer from 'puppeteer';

async function debugMLKFormattingPipeline() {
  let browser;
  try {
    console.log('🔍 DEBUGGING MLK FORMATTING PIPELINE\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Step 1: Debug BookLoader processing
    console.log('🔧 STEP 1: DEBUGGING BOOKLOADER PROCESSING\n');
    
    const bookLoaderDebug = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${Date.now()}`);
        
        // Get raw book data first
        const rawBook = await BookLoader.loadBook('85');
        
        // Get processed text
        const processedText = await BookLoader.getBookText('85');
        
        // Get formatted text for reader
        const formattedHTML = BookLoader.formatTextForReader(processedText, 'light');
        
        return {
          success: true,
          rawBookTitle: rawBook?.title,
          rawBookAuthor: rawBook?.author,
          rawBookDescription: rawBook?.description,
          hasContent: !!rawBook?.content,
          chaptersCount: rawBook?.content?.chapters?.length || 0,
          
          processedTextLength: processedText.length,
          processedTextPreview: processedText.substring(0, 800),
          
          formattedHTMLLength: formattedHTML.length,
          formattedHTMLPreview: formattedHTML.substring(0, 1200),
          
          // Check for specific HTML elements
          hasChapterTitleClass: formattedHTML.includes('class="chapter-title"'),
          hasSectionTitleClass: formattedHTML.includes('class="section-title"'),
          hasBodyTextClass: formattedHTML.includes('class="body-text"'),
          
          // Count HTML elements
          chapterTitleCount: (formattedHTML.match(/class="chapter-title"/g) || []).length,
          sectionTitleCount: (formattedHTML.match(/class="section-title"/g) || []).length,
          bodyTextCount: (formattedHTML.match(/class="body-text"/g) || []).length
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (bookLoaderDebug.success) {
      console.log('✅ BookLoader Debug Results:');
      console.log(`   Raw Book Title: "${bookLoaderDebug.rawBookTitle}"`);
      console.log(`   Raw Book Author: "${bookLoaderDebug.rawBookAuthor}"`);
      console.log(`   Raw Book Description: "${bookLoaderDebug.rawBookDescription?.substring(0, 80)}..."`);
      console.log(`   Has Content: ${bookLoaderDebug.hasContent}`);
      console.log(`   Chapters Count: ${bookLoaderDebug.chaptersCount}`);
      console.log('');
      console.log(`   Processed Text Length: ${bookLoaderDebug.processedTextLength} chars`);
      console.log(`   Formatted HTML Length: ${bookLoaderDebug.formattedHTMLLength} chars`);
      console.log('');
      console.log('📋 Processed Text Preview:');
      console.log(bookLoaderDebug.processedTextPreview);
      console.log('');
      console.log('🏷️ HTML Structure Analysis:');
      console.log(`   Chapter Title Classes: ${bookLoaderDebug.chapterTitleCount}`);
      console.log(`   Section Title Classes: ${bookLoaderDebug.sectionTitleCount}`);
      console.log(`   Body Text Classes: ${bookLoaderDebug.bodyTextCount}`);
      console.log(`   Has Chapter Title Class: ${bookLoaderDebug.hasChapterTitleClass}`);
      console.log(`   Has Section Title Class: ${bookLoaderDebug.hasSectionTitleClass}`);
      console.log(`   Has Body Text Class: ${bookLoaderDebug.hasBodyTextClass}`);
      console.log('');
      console.log('🔍 Formatted HTML Preview:');
      console.log(bookLoaderDebug.formattedHTMLPreview);
      
    } else {
      console.log('❌ BookLoader Debug Failed:', bookLoaderDebug.error);
    }

    // Step 2: Debug CSS Application
    console.log('\n🎨 STEP 2: DEBUGGING CSS APPLICATION\n');
    
    const cssDebug = await page.evaluate(() => {
      // Create test content with MLK structure
      const testContainer = document.createElement('div');
      testContainer.className = 'formatted-content light';
      testContainer.style.fontSize = '16pt';
      testContainer.innerHTML = `
        <div class="chapter-title">UM APELO À CONSCIÊNCIA: OS MELHORES DISCURSOS DE MARTIN LUTHER KING</div>
        <p class="body-text"><strong>Por Martin Luther King Jr.</strong></p>
        <p class="body-text">Os melhores discursos sobre direitos civis e justiça social de Martin Luther King Jr.</p>
        <div class="section-title">INTRODUÇÃO E CONTEXTUALIZAÇÃO</div>
        <p class="body-text">Em 1955, o panorama social dos Estados Unidos revelava profundas cicatrizes deixadas por séculos de segregação racial.</p>
      `;
      
      document.body.appendChild(testContainer);
      
      // Get computed styles
      const chapterTitle = testContainer.querySelector('.chapter-title');
      const sectionTitle = testContainer.querySelector('.section-title');
      const bodyText = testContainer.querySelector('.body-text');
      
      const chapterStyle = window.getComputedStyle(chapterTitle);
      const sectionStyle = window.getComputedStyle(sectionTitle);
      const bodyStyle = window.getComputedStyle(bodyText);
      
      const results = {
        // Chapter title styles
        chapterFontSize: chapterStyle.fontSize,
        chapterFontWeight: chapterStyle.fontWeight,
        chapterTextAlign: chapterStyle.textAlign,
        chapterTextTransform: chapterStyle.textTransform,
        chapterMarginTop: chapterStyle.marginTop,
        chapterMarginBottom: chapterStyle.marginBottom,
        chapterColor: chapterStyle.color,
        chapterFontFamily: chapterStyle.fontFamily,
        
        // Section title styles
        sectionFontSize: sectionStyle.fontSize,
        sectionFontWeight: sectionStyle.fontWeight,
        sectionTextAlign: sectionStyle.textAlign,
        sectionTextTransform: sectionStyle.textTransform,
        sectionMarginTop: sectionStyle.marginTop,
        sectionMarginBottom: sectionStyle.marginBottom,
        
        // Body text styles
        bodyFontSize: bodyStyle.fontSize,
        bodyFontWeight: bodyStyle.fontWeight,
        bodyTextAlign: bodyStyle.textAlign,
        bodyTextIndent: bodyStyle.textIndent,
        bodyLineHeight: bodyStyle.lineHeight,
        bodyMargin: bodyStyle.margin,
        bodyFontFamily: bodyStyle.fontFamily,
        
        // Container styles
        containerFontSize: window.getComputedStyle(testContainer).fontSize,
        containerFontFamily: window.getComputedStyle(testContainer).fontFamily
      };
      
      // Clean up
      document.body.removeChild(testContainer);
      
      return results;
    });

    console.log('🎨 CSS Application Results:');
    console.log('');
    console.log('📚 CHAPTER TITLE STYLES:');
    console.log(`   Font Size: ${cssDebug.chapterFontSize} (should be ~22pt)`);
    console.log(`   Font Weight: ${cssDebug.chapterFontWeight} (should be 700/bold)`);
    console.log(`   Text Align: ${cssDebug.chapterTextAlign} (should be center)`);
    console.log(`   Text Transform: ${cssDebug.chapterTextTransform} (should be uppercase)`);
    console.log(`   Margin Top: ${cssDebug.chapterMarginTop} (should be ~60pt)`);
    console.log(`   Margin Bottom: ${cssDebug.chapterMarginBottom} (should be ~32pt)`);
    console.log(`   Color: ${cssDebug.chapterColor}`);
    console.log(`   Font Family: ${cssDebug.chapterFontFamily}`);
    console.log('');
    console.log('📑 SECTION TITLE STYLES:');
    console.log(`   Font Size: ${cssDebug.sectionFontSize} (should be ~20pt)`);
    console.log(`   Font Weight: ${cssDebug.sectionFontWeight} (should be 700/bold)`);
    console.log(`   Text Align: ${cssDebug.sectionTextAlign} (should be center)`);
    console.log(`   Text Transform: ${cssDebug.sectionTextTransform} (should be uppercase)`);
    console.log(`   Margin Top: ${cssDebug.sectionMarginTop}`);
    console.log(`   Margin Bottom: ${cssDebug.sectionMarginBottom}`);
    console.log('');
    console.log('📝 BODY TEXT STYLES:');
    console.log(`   Font Size: ${cssDebug.bodyFontSize} (should be 16pt)`);
    console.log(`   Font Weight: ${cssDebug.bodyFontWeight} (should be 400/normal)`);
    console.log(`   Text Align: ${cssDebug.bodyTextAlign} (should be justify)`);
    console.log(`   Text Indent: ${cssDebug.bodyTextIndent} (should be 1cm)`);
    console.log(`   Line Height: ${cssDebug.bodyLineHeight} (should be 1.5)`);
    console.log(`   Font Family: ${cssDebug.bodyFontFamily}`);

    // Step 3: Check actual rendering in PDF reader
    console.log('\n📖 STEP 3: CHECKING ACTUAL PDF READER RENDERING\n');
    
    // Navigate to a book to test actual rendering
    console.log('Attempting to open MLK book in reader...');
    
    try {
      // Try to find and click on the MLK book
      await page.waitForSelector('[data-testid="book-card"], .book-card, .book-item', { timeout: 10000 });
      
      // Look for MLK book specifically
      const mlkBookFound = await page.evaluate(() => {
        const bookElements = document.querySelectorAll('[data-testid="book-card"], .book-card, .book-item, .grid > div');
        for (let element of bookElements) {
          const text = element.textContent || '';
          if (text.includes('Um Apelo à Consciência') || text.includes('Martin Luther King')) {
            element.click();
            return true;
          }
        }
        return false;
      });
      
      if (mlkBookFound) {
        console.log('✅ MLK book found and clicked');
        
        // Wait for reader to load
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check if we're in the reader
        const inReader = await page.evaluate(() => {
          return document.querySelector('.formatted-content') !== null;
        });
        
        if (inReader) {
          console.log('✅ Reader loaded successfully');
          
          // Analyze the actual rendered content
          const readerAnalysis = await page.evaluate(() => {
            const content = document.querySelector('.formatted-content');
            if (!content) return { found: false };
            
            const analysis = {
              found: true,
              innerHTML: content.innerHTML.substring(0, 1500),
              
              // Check for proper elements
              hasChapterTitles: content.querySelectorAll('.chapter-title').length,
              hasSectionTitles: content.querySelectorAll('.section-title').length,
              hasBodyTexts: content.querySelectorAll('.body-text').length,
              
              // Check computed styles of first elements
              firstElementTag: content.firstElementChild?.tagName,
              firstElementClass: content.firstElementChild?.className,
              firstElementText: content.firstElementChild?.textContent?.substring(0, 100),
              
              // Check overall structure
              totalChildren: content.children.length,
              containerClass: content.className,
              containerFontSize: window.getComputedStyle(content).fontSize
            };
            
            return analysis;
          });
          
          console.log('📊 ACTUAL READER ANALYSIS:');
          console.log(`   Content Found: ${readerAnalysis.found}`);
          console.log(`   Chapter Titles: ${readerAnalysis.hasChapterTitles}`);
          console.log(`   Section Titles: ${readerAnalysis.hasSectionTitles}`);
          console.log(`   Body Texts: ${readerAnalysis.hasBodyTexts}`);
          console.log(`   Total Children: ${readerAnalysis.totalChildren}`);
          console.log(`   Container Class: "${readerAnalysis.containerClass}"`);
          console.log(`   Container Font Size: ${readerAnalysis.containerFontSize}`);
          console.log(`   First Element: ${readerAnalysis.firstElementTag}.${readerAnalysis.firstElementClass}`);
          console.log(`   First Element Text: "${readerAnalysis.firstElementText}..."`);
          console.log('');
          console.log('🔍 Rendered HTML Preview:');
          console.log(readerAnalysis.innerHTML);
          
        } else {
          console.log('❌ Reader did not load properly');
        }
        
      } else {
        console.log('❌ MLK book not found in library');
      }
      
    } catch (error) {
      console.log('❌ Error testing reader:', error.message);
    }

    await new Promise(resolve => setTimeout(resolve, 5000));

  } catch (error) {
    console.error('💥 Error during debugging:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

debugMLKFormattingPipeline();
