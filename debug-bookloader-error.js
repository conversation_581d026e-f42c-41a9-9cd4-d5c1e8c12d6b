import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugBookLoaderError() {
  try {
    console.log('🔍 DEBUGANDO ERRO DO BOOKLOADER\n');

    // Testar alguns IDs de livros que sabemos que existem
    const testBookIds = ['3', '4', '7', '85'];

    for (const bookId of testBookIds) {
      console.log(`📖 Testando livro ID: ${bookId}`);
      
      try {
        // Passo 1: Verificar se o livro existe
        console.log('   1️⃣ Verificando se o livro existe...');
        const { data: bookData, error: bookError } = await supabase
          .from('books')
          .select('*')
          .eq('id', bookId)
          .single();

        if (bookError) {
          console.log(`   ❌ Erro ao buscar livro: ${bookError.message}`);
          console.log(`   📋 Detalhes: ${JSON.stringify(bookError, null, 2)}`);
          continue;
        }

        if (!bookData) {
          console.log(`   ❌ Livro não encontrado`);
          continue;
        }

        console.log(`   ✅ Livro encontrado: ${bookData.title}`);

        // Passo 2: Verificar se há conteúdo
        console.log('   2️⃣ Verificando conteúdo...');
        const { data: contentData, error: contentError } = await supabase
          .from('book_contents')
          .select('content')
          .eq('book_id', bookId)
          .single();

        if (contentError) {
          console.log(`   ⚠️ Erro ao buscar conteúdo: ${contentError.message}`);
          console.log(`   📋 Detalhes: ${JSON.stringify(contentError, null, 2)}`);
        } else if (!contentData) {
          console.log(`   ⚠️ Conteúdo não encontrado`);
        } else {
          console.log(`   ✅ Conteúdo encontrado`);
          
          const content = contentData.content;
          if (content.chapters && content.chapters.length > 0) {
            console.log(`   📚 Capítulos: ${content.chapters.length}`);
            console.log(`   📄 Primeiro capítulo: "${content.chapters[0].title}"`);
            console.log(`   📏 Tamanho do primeiro capítulo: ${content.chapters[0].content.length} chars`);
          }
        }

        // Passo 3: Simular o que o BookLoader.getBookText faz
        console.log('   3️⃣ Simulando BookLoader.getBookText...');
        
        try {
          let text = `# ${bookData.title}\n\n**Por ${bookData.author}**\n\n`;
          text += `${bookData.description || 'Descrição não disponível'}\n\n`;
          
          if (contentData && contentData.content && contentData.content.chapters) {
            contentData.content.chapters.forEach(chapter => {
              text += `## ${chapter.title}\n\n`;
              text += `${chapter.content}\n\n`;
            });
            
            if (contentData.content.key_points && contentData.content.key_points.length > 0) {
              text += `## Pontos-Chave\n\n`;
              contentData.content.key_points.forEach(point => {
                text += `- ${point}\n`;
              });
              text += '\n';
            }
          } else {
            // Fallback content
            text += `## Conteúdo Principal\n\n`;
            text += `Este livro apresenta insights valiosos sobre ${bookData.category?.toLowerCase() || 'o tema abordado'}.\n\n`;
            text += `O conteúdo completo está sendo processado e estará disponível em breve.\n\n`;
          }

          console.log(`   ✅ Texto gerado com sucesso: ${text.length} caracteres`);
          console.log(`   📋 Preview: ${text.substring(0, 200)}...`);
          
        } catch (textError) {
          console.log(`   ❌ Erro ao gerar texto: ${textError.message}`);
          console.log(`   📋 Stack: ${textError.stack}`);
        }

      } catch (generalError) {
        console.log(`   💥 Erro geral: ${generalError.message}`);
        console.log(`   📋 Stack: ${generalError.stack}`);
      }
      
      console.log('');
    }

    // Teste adicional: Verificar conectividade com Supabase
    console.log('🔗 TESTANDO CONECTIVIDADE COM SUPABASE...');
    
    try {
      const { data, error } = await supabase
        .from('books')
        .select('count(*)')
        .single();

      if (error) {
        console.log(`❌ Erro de conectividade: ${error.message}`);
      } else {
        console.log(`✅ Conectividade OK - Total de livros: ${data.count}`);
      }
    } catch (connError) {
      console.log(`💥 Erro de conexão: ${connError.message}`);
    }

    // Teste adicional: Verificar se há problemas de CORS ou autenticação
    console.log('\n🔐 TESTANDO AUTENTICAÇÃO...');
    
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        console.log(`⚠️ Problema de autenticação: ${authError.message}`);
      } else {
        console.log(`✅ Autenticação: ${user ? 'Usuário logado' : 'Usuário anônimo'}`);
      }
    } catch (authTestError) {
      console.log(`💥 Erro no teste de autenticação: ${authTestError.message}`);
    }

    console.log('\n🎯 RESUMO DO DEBUG:');
    console.log('Se todos os testes acima passaram, o problema pode estar:');
    console.log('1. Na importação do BookLoader no frontend');
    console.log('2. Em alguma diferença entre o ambiente Node.js e o navegador');
    console.log('3. Em problemas de CORS ou configuração do Vite');
    console.log('4. Em alguma diferença na configuração do Supabase no frontend');
    
  } catch (error) {
    console.error('💥 Erro geral no debug:', error);
  }
}

debugBookLoaderError();
