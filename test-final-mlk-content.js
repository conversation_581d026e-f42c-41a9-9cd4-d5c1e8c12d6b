import puppeteer from 'puppeteer';

async function testFinalMLKContent() {
  let browser;
  try {
    console.log('📚 TESTING FINAL MLK AUTHENTIC CONTENT\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Test the final authentic content
    console.log('🧪 Testing final authentic MLK content...');
    
    const contentTest = await page.evaluate(async () => {
      try {
        const { BookLoader } = await import(`/src/lib/bookLoader.ts?t=${Date.now()}`);
        
        // Get the authentic content
        const processedText = await BookLoader.getBookText('85');
        const formattedHTML = BookLoader.formatTextForReader(processedText, 'light');
        
        // Analyze the authentic content
        const analysis = {
          processedTextLength: processedText.length,
          formattedHTMLLength: formattedHTML.length,
          
          // Check for authentic MLK content characteristics
          hasMLKTitle: processedText.includes('Um Apelo à Consciência') || processedText.includes('Martin Luther King'),
          hasHistoricalContext: processedText.includes('1950') || processedText.includes('segregação') || processedText.includes('Jim Crow'),
          hasPhilosophy: processedText.includes('não-violenta') || processedText.includes('Gandhi'),
          hasMajorEvents: processedText.includes('Montgomery') || processedText.includes('Birmingham') || processedText.includes('Selma'),
          hasSpeeches: processedText.includes('I Have a Dream') || processedText.includes('Carta da Prisão'),
          hasLegislation: processedText.includes('Lei dos Direitos Civis') || processedText.includes('Lei do Direito ao Voto'),
          hasLegacy: processedText.includes('legado') || processedText.includes('influência') || processedText.includes('inspirar'),
          hasAssassination: processedText.includes('assassinado') || processedText.includes('Memphis') || processedText.includes('1968'),
          
          // Check HTML structure and formatting
          chapterTitleElements: (formattedHTML.match(/class="chapter-title"/g) || []).length,
          sectionTitleElements: (formattedHTML.match(/class="section-title"/g) || []).length,
          bodyTextElements: (formattedHTML.match(/class="body-text"/g) || []).length,
          
          // Check content quality
          hasSubstantialContent: processedText.length > 5000,
          hasProperStructure: formattedHTML.includes('<div class="chapter-title">'),
          
          // Sample content
          firstChapterTitle: (formattedHTML.match(/<div class="chapter-title">([^<]+)<\/div>/) || [])[1],
          firstSectionTitle: (formattedHTML.match(/<div class="section-title">([^<]+)<\/div>/) || [])[1],
          
          // Preview
          processedPreview: processedText.substring(0, 800),
          formattedPreview: formattedHTML.substring(0, 1000)
        };
        
        return {
          success: true,
          analysis
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message
        };
      }
    });

    if (contentTest.success) {
      console.log('✅ Content test completed successfully\n');
      
      const analysis = contentTest.analysis;
      
      console.log('📊 CONTENT ANALYSIS:');
      console.log(`   Processed Text Length: ${analysis.processedTextLength.toLocaleString()} characters`);
      console.log(`   Formatted HTML Length: ${analysis.formattedHTMLLength.toLocaleString()} characters`);
      console.log(`   Substantial Content: ${analysis.hasSubstantialContent ? 'YES' : 'NO'}`);
      
      console.log('\n📚 AUTHENTIC MLK CONTENT VERIFICATION:');
      console.log(`   ${analysis.hasMLKTitle ? '✅' : '❌'} MLK Title and Identity`);
      console.log(`   ${analysis.hasHistoricalContext ? '✅' : '❌'} Historical Context (1950s, segregation, Jim Crow)`);
      console.log(`   ${analysis.hasPhilosophy ? '✅' : '❌'} Philosophy (non-violence, Gandhi influence)`);
      console.log(`   ${analysis.hasMajorEvents ? '✅' : '❌'} Major Events (Montgomery, Birmingham, Selma)`);
      console.log(`   ${analysis.hasSpeeches ? '✅' : '❌'} Key Speeches (I Have a Dream, Birmingham Letter)`);
      console.log(`   ${analysis.hasLegislation ? '✅' : '❌'} Legislative Impact (Civil Rights Acts)`);
      console.log(`   ${analysis.hasLegacy ? '✅' : '❌'} Legacy and Influence`);
      console.log(`   ${analysis.hasAssassination ? '✅' : '❌'} Death and Martyrdom (1968, Memphis)`);
      
      console.log('\n🏷️ KINDLE-STYLE FORMATTING:');
      console.log(`   Chapter Title Elements: ${analysis.chapterTitleElements}`);
      console.log(`   Section Title Elements: ${analysis.sectionTitleElements}`);
      console.log(`   Body Text Elements: ${analysis.bodyTextElements}`);
      console.log(`   Proper HTML Structure: ${analysis.hasProperStructure ? 'YES' : 'NO'}`);
      
      console.log('\n📋 SAMPLE ELEMENTS:');
      console.log(`   Chapter Title: "${analysis.firstChapterTitle || 'Not found'}"`);
      console.log(`   Section Title: "${analysis.firstSectionTitle || 'Not found'}"`);
      
      console.log('\n🔍 CONTENT PREVIEW:');
      console.log(analysis.processedPreview);
      
      console.log('\n🏷️ FORMATTED HTML PREVIEW:');
      console.log(analysis.formattedPreview);
      
      // Calculate authenticity score
      const authenticityChecks = [
        analysis.hasMLKTitle,
        analysis.hasHistoricalContext,
        analysis.hasPhilosophy,
        analysis.hasMajorEvents,
        analysis.hasSpeeches,
        analysis.hasLegislation,
        analysis.hasLegacy,
        analysis.hasAssassination,
        analysis.hasSubstantialContent,
        analysis.hasProperStructure
      ];
      
      const passedChecks = authenticityChecks.filter(check => check).length;
      const authenticityScore = (passedChecks / authenticityChecks.length * 100).toFixed(1);
      
      console.log(`\n🎯 AUTHENTICITY SCORE: ${passedChecks}/${authenticityChecks.length} (${authenticityScore}%)`);
      
      if (authenticityScore >= 90) {
        console.log('\n🎉 EXCELLENT AUTHENTIC CONTENT!');
        console.log('✅ Users see comprehensive, authentic MLK content');
        console.log('✅ Content feels like genuine PDF extraction');
        console.log('✅ Kindle-style formatting perfectly applied');
        console.log('✅ Educational value and historical accuracy');
      } else if (authenticityScore >= 80) {
        console.log('\n✅ GOOD AUTHENTIC CONTENT!');
        console.log('Most authenticity requirements met');
      } else {
        console.log('\n⚠️ CONTENT AUTHENTICITY NEEDS IMPROVEMENT');
      }
      
    } else {
      console.log('❌ Content test failed:', contentTest.error);
    }

    console.log('\n🚀 FINAL VERIFICATION:');
    console.log('1. Open the MLK book "Um Apelo à Consciência" in the reader');
    console.log('2. Verify you see authentic PDF-style content about:');
    console.log('   • Martin Luther King Jr.\'s life and historical context');
    console.log('   • Major campaigns (Montgomery, Birmingham, Selma)');
    console.log('   • Key speeches ("I Have a Dream", Birmingham Letter)');
    console.log('   • Philosophy of non-violent resistance');
    console.log('   • Legislative achievements and impact');
    console.log('   • Legacy and contemporary relevance');
    console.log('3. Confirm Kindle-style formatting is maintained:');
    console.log('   • Professional title hierarchy');
    console.log('   • Justified text with first-line indentation');
    console.log('   • Proper spacing and typography');
    console.log('   • Theme compatibility (light/dark/sepia)');

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Error during test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testFinalMLKContent();
