import fs from 'fs';
import path from 'path';
import PDFParser from 'pdf2json';
import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

// Função para extrair texto completo de um PDF
async function extractRealPDFText(pdfPath) {
  return new Promise((resolve, reject) => {
    try {
      console.log(`   📄 Lendo arquivo: ${path.basename(pdfPath)}`);

      const stats = fs.statSync(pdfPath);
      console.log(`   📁 Tamanho do arquivo: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);

      console.log('   🔍 Extraindo texto do PDF...');

      const pdfParser = new PDFParser();

      pdfParser.on('pdfParser_dataError', (errData) => {
        console.error(`   ❌ Erro no parser: ${errData.parserError}`);
        reject(new Error(errData.parserError));
      });

      pdfParser.on('pdfParser_dataReady', (pdfData) => {
        try {
          // Extrair texto de todas as páginas
          let fullText = '';
          let totalPages = 0;

          if (pdfData.Pages) {
            totalPages = pdfData.Pages.length;

            for (let pageIndex = 0; pageIndex < pdfData.Pages.length; pageIndex++) {
              const page = pdfData.Pages[pageIndex];

              if (page.Texts) {
                for (const textItem of page.Texts) {
                  if (textItem.R) {
                    for (const run of textItem.R) {
                      if (run.T) {
                        // Decodificar texto
                        const decodedText = decodeURIComponent(run.T);
                        fullText += decodedText + ' ';
                      }
                    }
                  }
                }
              }

              fullText += '\n\n'; // Separador de página
            }
          }

          fullText = fullText.trim();

          console.log(`   ✅ Extração concluída:`);
          console.log(`      📊 Total de páginas: ${totalPages}`);
          console.log(`      📝 Total de caracteres: ${fullText.length.toLocaleString()}`);
          console.log(`      📏 Média por página: ${Math.round(fullText.length / totalPages)} chars`);

          resolve({
            text: fullText,
            pages: totalPages,
            characters: fullText.length
          });

        } catch (parseError) {
          console.error(`   ❌ Erro no processamento: ${parseError.message}`);
          reject(parseError);
        }
      });

      // Carregar o PDF
      pdfParser.loadPDF(pdfPath);

    } catch (error) {
      console.error(`   ❌ Erro na extração: ${error.message}`);
      reject(error);
    }
  });
}

// Função para estruturar o texto extraído
function structureExtractedText(fullText, filename) {
  console.log('   🏗️ Estruturando texto extraído...');
  
  const lines = fullText.split('\n').filter(line => line.trim().length > 0);
  
  // Detectar capítulos/seções baseado em padrões reais
  const chapters = [];
  let currentChapter = null;
  let currentContent = '';
  
  // Padrões para detectar títulos de capítulos
  const chapterPatterns = [
    /^CAPÍTULO\s+\d+/i,
    /^PARTE\s+\d+/i,
    /^SEÇÃO\s+\d+/i,
    /^\d+\.\s+[A-Z]/,
    /^[A-Z\s]{10,80}$/,
    /^[IVX]+\.\s+/,
  ];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Verificar se é um título de capítulo
    const isChapterTitle = chapterPatterns.some(pattern => pattern.test(line)) ||
                          (line.length < 100 && line.length > 5 && 
                           /^[A-Z]/.test(line) && 
                           !line.includes('.') && 
                           lines[i + 1]?.trim().length > 50);
    
    if (isChapterTitle && currentContent.length > 500) {
      // Salvar capítulo anterior
      if (currentChapter) {
        chapters.push({
          id: `chapter${chapters.length + 1}`,
          title: currentChapter,
          content: currentContent.trim()
        });
      }
      
      // Iniciar novo capítulo
      currentChapter = line;
      currentContent = '';
    } else {
      currentContent += line + '\n';
    }
  }
  
  // Adicionar último capítulo
  if (currentChapter && currentContent.length > 500) {
    chapters.push({
      id: `chapter${chapters.length + 1}`,
      title: currentChapter,
      content: currentContent.trim()
    });
  }
  
  // Se não encontrou capítulos estruturados, dividir por tamanho
  if (chapters.length === 0) {
    console.log('   📄 Dividindo texto por seções...');
    const chunkSize = Math.floor(fullText.length / 10); // Máximo 10 seções
    
    for (let i = 0; i < fullText.length; i += chunkSize) {
      const chunk = fullText.substring(i, i + chunkSize);
      if (chunk.length > 100) {
        chapters.push({
          id: `section${chapters.length + 1}`,
          title: `Seção ${chapters.length + 1}`,
          content: chunk
        });
      }
    }
  }
  
  console.log(`   📚 Estrutura criada: ${chapters.length} capítulos/seções`);
  
  return {
    chapters: chapters,
    full_text: fullText,
    total_characters: fullText.length
  };
}

// Função para extrair metadados do PDF
function extractMetadata(filename, fullText) {
  const nameWithoutExt = filename.replace('.pdf', '');
  
  // Mapeamento manual baseado nos nomes dos arquivos
  const bookMappings = {
    'a_interpretacao_dos_sonhos_sigmund_freud_3': {
      title: 'A Interpretação dos Sonhos',
      author: 'Sigmund Freud',
      category: 'Psicologia'
    },
    'a_sutil_arte_de_ligar_o_foda_se_mark_manson_3': {
      title: 'A Sutil Arte de Ligar o F*da-se',
      author: 'Mark Manson',
      category: 'Autoajuda'
    },
    'o_poder_do_habito_charles_duhigg_3': {
      title: 'O Poder do Hábito',
      author: 'Charles Duhigg',
      category: 'Produtividade'
    },
    'pai_rico_pai_pobre_robert_t_kiyosaki_3': {
      title: 'Pai Rico, Pai Pobre',
      author: 'Robert Kiyosaki',
      category: 'Finanças'
    },
    'habitos_atomicos_james_clear_5': {
      title: 'Hábitos Atômicos',
      author: 'James Clear',
      category: 'Produtividade'
    },
    'como_fazer_amigos_e_influenciar_pessoas_dale_carnegie_3': {
      title: 'Como Fazer Amigos e Influenciar Pessoas',
      author: 'Dale Carnegie',
      category: 'Relacionamentos'
    },
    'o_homem_mais_rico_da_babilonia_george_s_clason_3': {
      title: 'O Homem Mais Rico da Babilônia',
      author: 'George S. Clason',
      category: 'Finanças'
    },
    'mindset_carol_s_dweck_3': {
      title: 'Mindset',
      author: 'Carol S. Dweck',
      category: 'Psicologia'
    },
    'o_milagre_da_manha_hal_elrod_3': {
      title: 'O Milagre da Manhã',
      author: 'Hal Elrod',
      category: 'Produtividade'
    },
    'quem_mexeu_no_meu_queijo_spencer_johnson_3': {
      title: 'Quem Mexeu no Meu Queijo?',
      author: 'Spencer Johnson',
      category: 'Autoajuda'
    }
  };
  
  const mapping = bookMappings[nameWithoutExt];
  
  if (mapping) {
    return {
      title: mapping.title,
      author: mapping.author,
      category: mapping.category,
      description: `Conteúdo completo extraído do PDF original (${fullText.length.toLocaleString()} caracteres).`,
      duration: Math.max(30, Math.min(240, Math.floor(fullText.length / 300))),
      difficulty: fullText.length > 100000 ? 'Avançado' : fullText.length > 50000 ? 'Intermediário' : 'Fácil'
    };
  }
  
  // Fallback para arquivos não mapeados
  return {
    title: nameWithoutExt.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    author: 'Autor Desconhecido',
    category: 'Geral',
    description: `Conteúdo extraído do PDF (${fullText.length.toLocaleString()} caracteres).`,
    duration: Math.max(20, Math.min(180, Math.floor(fullText.length / 300))),
    difficulty: 'Intermediário'
  };
}

// Função principal para processar um PDF específico
async function processRealPDF(filename) {
  console.log(`\n📖 PROCESSANDO PDF REAL: ${filename}`);
  console.log('=' * 60);
  
  try {
    const pdfPath = path.join(PDF_FOLDER, filename);
    
    if (!fs.existsSync(pdfPath)) {
      throw new Error('Arquivo PDF não encontrado');
    }
    
    // 1. Extrair texto completo do PDF
    const extractionResult = await extractRealPDFText(pdfPath);
    
    if (extractionResult.characters < 1000) {
      throw new Error('PDF contém muito pouco texto');
    }
    
    // 2. Estruturar o texto extraído
    const structuredContent = structureExtractedText(extractionResult.text, filename);
    
    // 3. Extrair metadados
    const metadata = extractMetadata(filename, extractionResult.text);
    
    console.log(`   📊 RESUMO:`);
    console.log(`      📝 Título: ${metadata.title}`);
    console.log(`      ✍️ Autor: ${metadata.author}`);
    console.log(`      📄 Páginas: ${extractionResult.pages}`);
    console.log(`      📝 Caracteres: ${extractionResult.characters.toLocaleString()}`);
    console.log(`      📚 Capítulos: ${structuredContent.chapters.length}`);
    console.log(`      ⏱️ Duração: ${metadata.duration} min`);
    
    // 4. Salvar no banco de dados
    console.log('   💾 Salvando no banco de dados...');
    
    // Verificar se o livro já existe
    const { data: existingBook, error: searchError } = await supabase
      .from('books')
      .select('id')
      .eq('title', metadata.title)
      .single();

    let bookId;
    
    if (existingBook) {
      bookId = existingBook.id;
      console.log(`   ✅ Livro encontrado (ID: ${bookId}), atualizando...`);
      
      // Atualizar informações do livro
      await supabase
        .from('books')
        .update({
          author: metadata.author,
          category: metadata.category,
          description: metadata.description,
          duration: metadata.duration,
          difficulty: metadata.difficulty,
          is_featured: true,
          is_free: true,
          pdf_key: filename
        })
        .eq('id', bookId);
    } else {
      // Inserir novo livro
      const { data: newBook, error: insertError } = await supabase
        .from('books')
        .insert({
          title: metadata.title,
          author: metadata.author,
          category: metadata.category,
          description: metadata.description,
          duration: metadata.duration,
          difficulty: metadata.difficulty,
          is_featured: true,
          is_free: true,
          pdf_key: filename
        })
        .select('id')
        .single();

      if (insertError) {
        throw new Error(`Erro ao inserir livro: ${insertError.message}`);
      }
      
      bookId = newBook.id;
      console.log(`   ✅ Novo livro criado (ID: ${bookId})`);
    }

    // 5. Inserir/atualizar conteúdo REAL
    const { error: contentError } = await supabase
      .from('book_contents')
      .update({
        content: {
          chapters: structuredContent.chapters,
          full_text: structuredContent.full_text,
          total_characters: structuredContent.total_characters,
          total_pages: extractionResult.pages,
          extraction_date: new Date().toISOString(),
          source_file: filename
        }
      })
      .eq('book_id', bookId);

    if (contentError) {
      throw new Error(`Erro ao salvar conteúdo: ${contentError.message}`);
    }
    
    console.log(`   🎉 SUCESSO! PDF processado completamente:`);
    console.log(`      📖 ${metadata.title} por ${metadata.author}`);
    console.log(`      📊 ${extractionResult.characters.toLocaleString()} caracteres de ${extractionResult.pages} páginas`);
    console.log(`      💾 Salvo no banco com ID ${bookId}`);
    
    return {
      success: true,
      bookId,
      title: metadata.title,
      author: metadata.author,
      pages: extractionResult.pages,
      characters: extractionResult.characters,
      chapters: structuredContent.chapters.length
    };
    
  } catch (error) {
    console.error(`   ❌ ERRO: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
}

export { processRealPDF };
