import { processRealPDF } from './extract-real-pdf-text.js';

async function processMLKBook() {
  console.log('🎯 PROCESSANDO LIVRO MARTIN LUTHER KING COM CONTEÚDO REAL\n');
  
  const filename = 'um_apelo_a_consciencia_os_melhores_discursos_martin_luther_king_3.pdf';
  
  try {
    const result = await processRealPDF(filename);
    
    if (result.success) {
      console.log('🎉 SUCESSO! Liv<PERSON> King processado com conteúdo real:');
      console.log(`   📖 ${result.title}`);
      console.log(`   ✍️ ${result.author}`);
      console.log(`   📄 ${result.pages} páginas`);
      console.log(`   📝 ${result.characters.toLocaleString()} caracteres`);
      console.log(`   📚 ${result.chapters} capítulos`);
      console.log(`   💾 ID no banco: ${result.bookId}`);
      
      console.log('\n✨ Agora o livro Martin Luther King tem conteúdo REAL extraído do PDF!');
      console.log('Teste no navegador clicando em "Ler Agora" neste livro.');
      
    } else {
      console.log('❌ FALHA no processamento:');
      console.log(`   Erro: ${result.error}`);
    }
    
  } catch (error) {
    console.error('💥 ERRO:', error.message);
  }
}

processMLKBook();
