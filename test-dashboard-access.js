import puppeteer from 'puppeteer';

async function testDashboardAccess() {
  let browser;
  try {
    console.log('🚀 Testando acesso ao dashboard e biblioteca...\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Verificar se há botão de "Entrar" ou similar
    console.log('🔍 Procurando por botões de login/entrar...');
    
    const loginButtons = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button, a'));
      return buttons
        .filter(btn => {
          const text = btn.textContent?.toLowerCase() || '';
          return text.includes('entrar') || 
                 text.includes('login') || 
                 text.includes('acessar') ||
                 text.includes('começar');
        })
        .map(btn => ({
          text: btn.textContent?.trim(),
          className: btn.className
        }));
    });

    console.log(`🔑 Encontrados ${loginButtons.length} botões de login:`);
    loginButtons.forEach((btn, index) => {
      console.log(`   ${index + 1}. "${btn.text}"`);
    });

    if (loginButtons.length > 0) {
      console.log('\n🖱️ Tentando clicar no botão de entrar...');
      
      await page.evaluate(() => {
        const buttons = Array.from(document.querySelectorAll('button, a'));
        const loginButton = buttons.find(btn => {
          const text = btn.textContent?.toLowerCase() || '';
          return text.includes('entrar') || 
                 text.includes('login') || 
                 text.includes('acessar') ||
                 text.includes('começar');
        });
        
        if (loginButton) {
          loginButton.click();
        }
      });

      await new Promise(resolve => setTimeout(resolve, 3000));

      // Verificar se apareceu um formulário de login ou se foi redirecionado
      const hasLoginForm = await page.evaluate(() => {
        return !!document.querySelector('input[type="email"], input[type="password"], form');
      });

      if (hasLoginForm) {
        console.log('📝 Formulário de login detectado. Tentando fazer login de teste...');
        
        // Tentar preencher formulário de teste
        try {
          await page.type('input[type="email"]', '<EMAIL>');
          await page.type('input[type="password"]', 'password123');
          
          // Procurar botão de submit
          await page.evaluate(() => {
            const submitBtn = document.querySelector('button[type="submit"], input[type="submit"]') ||
                             Array.from(document.querySelectorAll('button')).find(btn => 
                               btn.textContent?.toLowerCase().includes('entrar') ||
                               btn.textContent?.toLowerCase().includes('login')
                             );
            if (submitBtn) {
              submitBtn.click();
            }
          });

          await new Promise(resolve => setTimeout(resolve, 5000));
          
        } catch (error) {
          console.log(`⚠️ Erro ao preencher formulário: ${error.message}`);
        }
      }
    }

    // Verificar se agora temos acesso à biblioteca
    console.log('\n📚 Verificando se há biblioteca/dashboard disponível...');
    
    const libraryContent = await page.evaluate(() => {
      const text = document.body.innerText.toLowerCase();
      const hasLibrary = text.includes('biblioteca') || 
                        text.includes('livros') || 
                        text.includes('dashboard') ||
                        text.includes('meus livros');
      
      const bookCards = Array.from(document.querySelectorAll('[class*="card"], [class*="book"], .grid > div'))
        .filter(el => {
          const elText = el.textContent?.toLowerCase() || '';
          return elText.length > 20 && (
            elText.includes('autor') || 
            elText.includes('ler') || 
            elText.includes('capítulo') ||
            elText.includes('resumo')
          );
        });

      return {
        hasLibrary,
        bookCount: bookCards.length,
        books: bookCards.slice(0, 3).map(card => ({
          text: card.textContent?.substring(0, 100) + '...',
          hasReadButton: !!card.querySelector('button, a')
        }))
      };
    });

    console.log(`📖 Biblioteca detectada: ${libraryContent.hasLibrary}`);
    console.log(`📚 Número de livros encontrados: ${libraryContent.bookCount}`);
    
    if (libraryContent.books.length > 0) {
      console.log('📋 Primeiros livros encontrados:');
      libraryContent.books.forEach((book, index) => {
        console.log(`   ${index + 1}. ${book.text} (Botão: ${book.hasReadButton})`);
      });

      // Se encontramos livros, tentar clicar em um botão de leitura
      if (libraryContent.bookCount > 0) {
        console.log('\n🖱️ Tentando clicar em um livro para ler...');
        
        const clickResult = await page.evaluate(() => {
          const bookCards = Array.from(document.querySelectorAll('[class*="card"], [class*="book"], .grid > div'));
          const bookWithButton = bookCards.find(card => {
            const button = card.querySelector('button, a');
            if (button) {
              const text = button.textContent?.toLowerCase() || '';
              return text.includes('ler') || text.includes('abrir') || text.includes('ver');
            }
            return false;
          });

          if (bookWithButton) {
            const button = bookWithButton.querySelector('button, a');
            button.click();
            return { success: true, buttonText: button.textContent?.trim() };
          }
          return { success: false };
        });

        if (clickResult.success) {
          console.log(`✅ Clicou no botão: "${clickResult.buttonText}"`);
          
          await new Promise(resolve => setTimeout(resolve, 5000));
          
          // Verificar se chegamos na página de leitura
          const readingPage = await page.evaluate(() => {
            const text = document.body.innerText;
            return {
              url: window.location.href,
              title: document.title,
              hasBookContent: text.length > 500,
              hasError: text.toLowerCase().includes('não encontrado') || 
                       text.toLowerCase().includes('erro') ||
                       text.toLowerCase().includes('placeholder'),
              contentPreview: text.substring(0, 200) + '...'
            };
          });

          console.log('\n📄 Resultado da página de leitura:');
          console.log(`   🌐 URL: ${readingPage.url}`);
          console.log(`   📝 Título: ${readingPage.title}`);
          console.log(`   📚 Tem conteúdo: ${readingPage.hasBookContent}`);
          console.log(`   ❌ Tem erro: ${readingPage.hasError}`);
          console.log(`   📄 Preview: ${readingPage.contentPreview}`);

          if (readingPage.hasError) {
            console.log('\n❌ BUG CONFIRMADO: A página de leitura mostra erro ou conteúdo placeholder!');
          } else if (readingPage.hasBookContent) {
            console.log('\n✅ SUCESSO: A página parece mostrar conteúdo real do livro!');
          }
        } else {
          console.log('⚠️ Não foi possível encontrar um botão de leitura clicável');
        }
      }
    }

    // Screenshot final
    await page.screenshot({ 
      path: 'dashboard-test.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot salvo como dashboard-test.png');

    await new Promise(resolve => setTimeout(resolve, 3000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testDashboardAccess();
