import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyMLKContent() {
  try {
    console.log('🔍 VERIFICANDO CONTEÚDO DO MLK (ID 85)\n');

    const bookId = 85;

    // 1. Verificar dados do livro
    console.log('1. Verificando dados do livro...');
    const { data: book, error: bookError } = await supabase
      .from('books')
      .select('*')
      .eq('id', bookId)
      .single();

    if (bookError) {
      console.log('❌ Erro ao buscar livro:', bookError.message);
      return;
    }

    console.log(`   📚 Título: "${book.title}"`);
    console.log(`   ✍️ Autor: "${book.author}"`);
    console.log(`   📂 Categoria: "${book.category}"`);
    console.log(`   📝 Descrição: "${book.description}"`);
    console.log(`   ⏱️ Duração: ${book.duration} min`);

    // 2. Verificar conteúdo do livro
    console.log('\n2. Verificando conteúdo do livro...');
    const { data: content, error: contentError } = await supabase
      .from('book_contents')
      .select('*')
      .eq('book_id', bookId);

    if (contentError) {
      console.log('❌ Erro ao buscar conteúdo:', contentError.message);
      return;
    }

    if (!content || content.length === 0) {
      console.log('❌ Nenhum conteúdo encontrado');
      return;
    }

    console.log(`   ✅ ${content.length} entrada(s) de conteúdo encontrada(s)`);

    const bookContent = content[0];
    const contentData = bookContent.content;

    console.log('\n3. Analisando estrutura do conteúdo...');
    console.log(`   📊 Tipo: ${typeof contentData}`);

    if (typeof contentData === 'object' && contentData.chapters) {
      console.log(`   📚 Capítulos: ${contentData.chapters.length}`);
      console.log(`   📈 Caracteres totais: ${contentData.total_characters?.toLocaleString() || 'N/A'}`);
      console.log(`   📄 Páginas estimadas: ${contentData.total_pages || 'N/A'}`);
      console.log(`   🔗 Fonte: ${contentData.source || 'N/A'}`);
      console.log(`   🛠️ Método de extração: ${contentData.extraction_method || 'N/A'}`);
      console.log(`   📅 Data de extração: ${contentData.extraction_date || 'N/A'}`);

      if (contentData.chapters.length > 0) {
        const firstChapter = contentData.chapters[0];
        console.log(`\n4. Analisando primeiro capítulo...`);
        console.log(`   📖 Título: "${firstChapter.title}"`);
        console.log(`   📏 Tamanho: ${firstChapter.content?.length?.toLocaleString() || 0} caracteres`);

        if (firstChapter.content) {
          const content = firstChapter.content;
          
          // Verificar se é conteúdo real do MLK
          const mlkKeywords = [
            'Martin Luther King',
            'direitos civis',
            'discurso',
            'Montgomery',
            'Birmingham',
            'dream',
            'não-violência',
            'segregação',
            'I have a dream',
            'boicote',
            'marcha',
            'Washington'
          ];

          const foundKeywords = mlkKeywords.filter(keyword => 
            content.toLowerCase().includes(keyword.toLowerCase())
          );

          console.log(`\n5. Verificando autenticidade do conteúdo...`);
          console.log(`   🔍 Palavras-chave do MLK encontradas: ${foundKeywords.length}/${mlkKeywords.length}`);
          
          if (foundKeywords.length > 0) {
            console.log(`   ✅ Palavras encontradas: ${foundKeywords.join(', ')}`);
          }

          const isRealMLKContent = foundKeywords.length >= 3 && content.length > 10000;
          console.log(`   🎯 Conteúdo autêntico do MLK: ${isRealMLKContent ? '✅ SIM' : '❌ NÃO'}`);

          // Preview do conteúdo
          console.log(`\n6. Preview do conteúdo (primeiros 500 caracteres):`);
          console.log(`"${content.substring(0, 500)}..."`);

          // Verificar se é dados estruturados (JSON)
          if (content.includes('"id":') && content.includes('"action":')) {
            console.log('\n⚠️ ATENÇÃO: Conteúdo parece ser dados estruturados (JSON)');
            console.log('   Isso pode ser dados de tracking, não texto para leitura');
            
            // Tentar extrair texto legível
            const textMatches = content.match(/"text":\s*"([^"]+)"/g);
            if (textMatches && textMatches.length > 0) {
              console.log(`   📝 ${textMatches.length} campos de texto encontrados no JSON`);
              console.log('   📋 Exemplos:');
              textMatches.slice(0, 3).forEach((match, index) => {
                const text = match.replace(/"text":\s*"/, '').replace(/"$/, '');
                console.log(`      ${index + 1}. "${text.substring(0, 100)}..."`);
              });
            }
          }

          // Estatísticas
          console.log(`\n7. Estatísticas do conteúdo:`);
          console.log(`   📏 Tamanho total: ${content.length.toLocaleString()} caracteres`);
          console.log(`   📝 Palavras estimadas: ${Math.round(content.length / 5).toLocaleString()}`);
          console.log(`   📄 Páginas estimadas: ${Math.ceil(content.length / 2000)}`);
          
          const isReadable = content.length > 1000 && 
                           !content.startsWith('{') && 
                           !content.includes('"id":');
          
          console.log(`   📖 Adequado para leitura: ${isReadable ? '✅ SIM' : '❌ NÃO'}`);

        } else {
          console.log('❌ Primeiro capítulo não tem conteúdo');
        }
      }
    } else {
      console.log('❌ Estrutura de conteúdo não reconhecida');
      console.log(`   Dados: ${JSON.stringify(contentData).substring(0, 200)}...`);
    }

    // 8. Testar consulta do BookLoader
    console.log('\n8. Testando consulta do BookLoader...');
    const { data: bookWithContent, error: loaderError } = await supabase
      .from('books')
      .select(`
        *,
        book_contents (
          content
        )
      `)
      .eq('id', bookId)
      .single();

    if (loaderError) {
      console.log('❌ Erro na consulta do BookLoader:', loaderError.message);
    } else if (bookWithContent.book_contents && bookWithContent.book_contents.length > 0) {
      console.log('✅ BookLoader consegue carregar o conteúdo');
      const loaderContent = bookWithContent.book_contents[0].content;
      console.log(`   📚 Capítulos carregados: ${loaderContent.chapters?.length || 0}`);
    } else {
      console.log('❌ BookLoader não encontrou conteúdo');
    }

    console.log('\n🎯 CONCLUSÃO FINAL:');
    if (contentData && contentData.total_characters > 100000) {
      console.log('✅ Conteúdo volumoso encontrado (mais de 100k caracteres)');
      console.log('✅ Dados foram extraídos de arquivo real');
      
      if (contentData.chapters && contentData.chapters[0] && contentData.chapters[0].content) {
        const mainContent = contentData.chapters[0].content;
        if (mainContent.includes('"id":') && mainContent.includes('"action":')) {
          console.log('⚠️ PROBLEMA: Conteúdo é dados estruturados, não texto para leitura');
          console.log('💡 RECOMENDAÇÃO: Extrair texto legível dos dados JSON ou usar conteúdo alternativo');
        } else {
          console.log('✅ Conteúdo parece ser texto legível');
        }
      }
    } else {
      console.log('❌ Conteúdo insuficiente ou não encontrado');
    }

  } catch (error) {
    console.error('💥 Erro durante verificação:', error.message);
  }
}

verifyMLKContent();
