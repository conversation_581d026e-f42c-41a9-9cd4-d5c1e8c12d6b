import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Sidebar } from '@/components/layout/Sidebar';
import { LibraryGrid } from '@/components/library/LibraryGrid';
import { DashboardStats } from './DashboardStats';
import { RecentActivity } from './RecentActivity';
import { QuickActions } from './QuickActions';
import { ReadingStreak } from './ReadingStreak';
import { PersonalizedRecommendations } from './PersonalizedRecommendations';
import { ProfileSection } from '@/components/profile/ProfileSection';
import { PDFReader } from '@/components/reader/PDFReader';
import { supabase } from '@/lib/supabase';

interface DashboardProps {
  user: any;
  onSignOut: () => void;
}

export default function Dashboard({ user, onSignOut }: DashboardProps) {
  const [activeSection, setActiveSection] = useState('home');
  const [books, setBooks] = useState([]);
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentBook, setCurrentBook] = useState(null);
  const [showReader, setShowReader] = useState(false);

  useEffect(() => {
    loadDashboardData();
    loadBooks();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Simulated dashboard data - replace with actual API call
      const mockDashboard = {
        recently_read: [
          {
            id: '1',
            title: 'Hábitos Atômicos',
            author: 'James Clear',
            progress_percentage: 75,
            last_read_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            is_favorited: true,
            summaries: {
              estimated_reading_time: 25,
              books: {
                title: 'Hábitos Atômicos',
                author: 'James Clear',
                cover_image_url: 'https://images.pexels.com/photos/1029141/pexels-photo-1029141.jpeg?auto=compress&cs=tinysrgb&w=400'
              }
            }
          },
          {
            id: '2',
            title: 'O Investidor Inteligente',
            author: 'Benjamin Graham',
            progress_percentage: 45,
            last_read_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            is_favorited: false,
            summaries: {
              estimated_reading_time: 30,
              books: {
                title: 'O Investidor Inteligente',
                author: 'Benjamin Graham',
                cover_image_url: 'https://images.pexels.com/photos/159711/books-bookstore-book-reading-159711.jpeg?auto=compress&cs=tinysrgb&w=400'
              }
            }
          }
        ],
        favorites: [],
        completed: [],
        in_progress: [],
        subscription: {
          subscription_type: 'free',
          status: 'active',
          remaining_free_access: 5
        },
        stats: {
          total_summaries_read: 12,
          total_reading_time: 480, // minutes
          favorites_count: 5,
          in_progress_count: 3
        },
        streak: {
          current: 7,
          longest: 15,
          this_week: 5
        }
      };
      
      setDashboardData(mockDashboard);
    } catch (error) {
      console.error('Error loading dashboard:', error);
    }
  };

  const loadBooks = async () => {
    try {
      const { data, error } = await supabase
        .from('books')
        .select('*')
        .order('is_featured', { ascending: false });

      if (error) throw error;
      setBooks(data || []);
    } catch (error) {
      console.error('Error loading books:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleReadBook = (bookId: string) => {
    console.log('Dashboard: Opening book with ID:', bookId);
    
    // Find the book data
    const book = books.find(b => b.id.toString() === bookId.toString());
    console.log('Dashboard: Found book:', book);
    
    if (book) {
      setCurrentBook(book);
      setShowReader(true);
    } else {
      console.error('Dashboard: Book not found for ID:', bookId);
      // Still try to open the reader with basic info
      setCurrentBook({
        id: bookId,
        title: 'Livro Selecionado',
        author: 'Autor',
        category: 'Geral'
      });
      setShowReader(true);
    }
  };

  const handleCloseReader = () => {
    console.log('Dashboard: Closing reader');
    setShowReader(false);
    setCurrentBook(null);
  };

  const handleFavoriteBook = (bookId: string) => {
    console.log('Toggling favorite for book:', bookId);
    // Implement favorite toggle logic
  };

  // Nova função para voltar ao dashboard
  const handleLogoClick = () => {
    setActiveSection('home');
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'home':
        return (
          <div className="space-y-8">
            {/* Quick Actions */}
            <QuickActions onSectionChange={setActiveSection} />

            {/* Stats Grid */}
            {dashboardData && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <DashboardStats stats={dashboardData.stats} />
                </div>
                <div>
                  <ReadingStreak streak={dashboardData.streak} />
                </div>
              </div>
            )}

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Recent Activity */}
              <div className="lg:col-span-2">
                {dashboardData && (
                  <RecentActivity 
                    recentBooks={dashboardData.recently_read}
                    onContinueReading={handleReadBook}
                  />
                )}
              </div>

              {/* Personalized Recommendations */}
              <div>
                <PersonalizedRecommendations 
                  books={books.filter(book => book.is_featured).slice(0, 3)}
                  onReadBook={handleReadBook}
                />
              </div>
            </div>

            {/* Featured Books */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Resumos em Destaque</h2>
                <button 
                  onClick={() => setActiveSection('library')}
                  className="text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors"
                >
                  Ver todos →
                </button>
              </div>
              <LibraryGrid
                books={books.filter(book => book.is_featured).slice(0, 8)}
                onReadBook={handleReadBook}
                onFavoriteBook={handleFavoriteBook}
                isLoading={loading}
              />
            </div>
          </div>
        );

      case 'library':
        return (
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Biblioteca</h1>
              <p className="text-gray-600 text-lg">
                Explore nossa coleção completa de resumos profissionais.
              </p>
            </motion.div>

            <LibraryGrid
              books={books}
              onReadBook={handleReadBook}
              onFavoriteBook={handleFavoriteBook}
              isLoading={loading}
            />
          </div>
        );

      case 'favorites':
        return (
          <div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Favoritos</h1>
              <p className="text-gray-600 text-lg">
                Seus resumos favoritos em um só lugar.
              </p>
            </motion.div>

            <LibraryGrid
              books={[]} // Replace with favorite books
              onReadBook={handleReadBook}
              onFavoriteBook={handleFavoriteBook}
              isLoading={loading}
            />
          </div>
        );

      case 'profile':
        return (
          <ProfileSection 
            user={user} 
            dashboardData={dashboardData}
          />
        );

      default:
        return (
          <div className="text-center py-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Seção em Desenvolvimento
            </h2>
            <p className="text-gray-600">
              Esta funcionalidade estará disponível em breve.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* PDF Reader */}
      <AnimatePresence>
        {showReader && currentBook && (
          <PDFReader
            bookId={currentBook.id.toString()}
            bookTitle={currentBook.title}
            bookAuthor={currentBook.author}
            content=""
            onClose={handleCloseReader}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <Sidebar 
        activeSection={activeSection} 
        onSectionChange={setActiveSection}
        onLogoClick={handleLogoClick} // Passa a função para o Sidebar
      />

      {/* Main Content */}
      <div className="ml-70 min-h-screen">
        {/* Header */}
        <header className="bg-white/80 backdrop-blur-xl border-b border-gray-100 px-8 py-4 sticky top-0 z-30">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold text-gray-900 capitalize">
                {activeSection === 'home' ? 'Dashboard' : 
                 activeSection === 'profile' ? 'Perfil' : activeSection}
              </h2>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-gray-900 to-gray-700 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user.email?.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  {user.email?.split('@')[0]}
                </div>
              </div>
              <button
                onClick={onSignOut}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors px-3 py-1 rounded-lg hover:bg-gray-100"
              >
                Sair
              </button>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="p-8">
          <div className="max-w-7xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>
    </div>
  );
}