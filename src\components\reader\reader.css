/* KINDLE-STYLE TEXT FORMATTING WITH ROBOTO FONT */
/* Base: 16pt body text, 22pt chapter titles, pure black (#000000) */
/* Optimized for A4 PDF and Kindle 6" display */

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');

/* Main container with Kindle-style specifications */
.formatted-content {
  font-family: 'Roboto', sans-serif !important;
  font-size: 16pt; /* Base size: 16pt - no !important to allow inline override */
  line-height: 1.5 !important; /* Line height: 1.5 */
  color: #000000 !important; /* Pure black (#000000) */
  text-align: justify !important; /* Justified alignment */

  /* Enable automatic hyphenation for justified text */
  hyphens: auto !important;
  -webkit-hyphens: auto !important;
  -moz-hyphens: auto !important;
  -ms-hyphens: auto !important;

  /* Font antialiasing for smooth rendering */
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  text-rendering: optimizeLegibility !important;

  /* Page margins: Top 3cm, Bottom 2.5cm, Left/Right 2.5cm */
  padding: 3cm 2.5cm 2.5cm 2.5cm !important;
  max-width: 21cm !important; /* A4 width */
  margin: 0 auto !important;
}

/* Theme-specific colors */
.formatted-content.light {
  color: #000000 !important; /* Pure black for light theme */
}

.formatted-content.dark {
  color: #f9fafb !important; /* Light gray for dark theme */
}

.formatted-content.sepia {
  color: #92400e !important; /* Dark brown for sepia theme */
}

/* 1. CHAPTER TITLES - "CAPÍTULO UM" */
.formatted-content .chapter-title {
  font-family: 'Roboto', sans-serif !important;
  font-weight: 700 !important; /* Roboto Bold */
  font-size: 1.375em !important; /* 22pt relative to 16pt base (22/16 = 1.375) */
  text-transform: uppercase !important; /* Uppercase */
  text-align: center !important; /* Centered alignment */
  color: inherit !important; /* Inherit theme color */

  /* Spacing: 60pt above, 32pt below */
  margin-top: 3.75em !important; /* 60pt/16pt = 3.75em */
  margin-bottom: 2em !important; /* 32pt/16pt = 2em */

  /* Typography settings */
  letter-spacing: 0.05em !important;
  line-height: 1.2 !important;
  text-decoration: none !important; /* No underline */
  font-style: normal !important; /* No italic */

  /* Visual separation from body text */
  display: block !important;
  width: 100% !important;
  padding: 0 !important;
}

/* 3. NUMBERED LISTS AND ENUMERATIONS - Visually distinct from main text */
.formatted-content .numbered-list {
  font-family: 'Roboto', sans-serif !important;
  font-weight: 400 !important; /* Regular */
  font-size: 1em !important; /* Same as body text */
  line-height: 1.5 !important;
  color: inherit !important;
  text-align: left !important; /* Left-aligned for lists */

  /* Visual separation from body text */
  margin: 1em 0 !important; /* Space above and below */
  padding-left: 2em !important; /* Indent from left margin */
  text-indent: 0 !important; /* No first-line indent for lists */

  /* List styling */
  list-style-type: decimal !important;
  display: list-item !important;
}

/* Section titles (## headings) - smaller than chapter titles */
.formatted-content .section-title {
  font-family: 'Roboto', sans-serif !important;
  font-weight: 700 !important; /* Bold */
  font-size: 1.25em !important; /* 20pt relative to 16pt base */
  text-transform: uppercase !important;
  text-align: center !important;
  color: inherit !important;
  margin-top: 2.5em !important; /* More space above for separation */
  margin-bottom: 1.5em !important; /* More space below */
  letter-spacing: 0.03em !important;
  line-height: 1.3 !important;

  /* Visual separation */
  border-bottom: 1px solid currentColor !important;
  padding-bottom: 0.5em !important;
  width: 60% !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Subsection titles (### headings) - even smaller */
.formatted-content .subsection-title {
  font-family: 'Roboto', sans-serif !important;
  font-weight: 600 !important; /* Semi-bold */
  font-size: 1.125em !important; /* 18pt relative to 16pt base */
  text-transform: uppercase !important;
  text-align: left !important; /* Left-aligned for subsections */
  color: inherit !important;
  margin-top: 1.5em !important;
  margin-bottom: 1em !important;
  letter-spacing: 0.02em !important;
  line-height: 1.3 !important;

  /* Visual distinction */
  font-style: italic !important;
}

/* 2. BODY TEXT - Main content paragraphs */
.formatted-content .body-text {
  font-family: 'Roboto', sans-serif !important;
  font-weight: 400 !important; /* Roboto Regular */
  font-size: 1em !important; /* 16pt (inherits from container) */
  line-height: 1.5 !important; /* Line height: 1.5 */
  text-align: justify !important; /* Justified alignment */
  color: inherit !important; /* Inherit theme color */

  /* First line indent: 1.0cm - using multiple approaches */
  text-indent: 1cm !important;

  /* Paragraph spacing: 0pt (no extra spacing between paragraphs) */
  margin: 0 !important;
  margin-bottom: 0 !important;
  padding: 0 !important;

  /* Ensure proper text flow */
  display: block !important;
  width: 100% !important;
}

/* Backup approach for text-indent using margin */
.formatted-content .body-text:not(:has(strong)) {
  margin-left: 0 !important;
  padding-left: 1cm !important;
  text-indent: -1cm !important;
}

/* Override any conflicting styles and ensure text-indent works */
.formatted-content p.body-text {
  text-indent: 1cm !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Force text-indent with higher specificity */
div.formatted-content p.body-text {
  text-indent: 1cm !important;
}

/* Force text-indent with maximum specificity */
.formatted-content p.body-text,
.formatted-content .body-text {
  text-indent: 1cm !important;
}

/* Ensure it works even with dynamic font sizing */
div.formatted-content p.body-text {
  text-indent: 1cm !important;
}

/* Special handling for author and description lines */
.formatted-content .body-text strong,
.formatted-content .body-text:has(strong) {
  text-indent: 0 !important;
}

/* Author line specifically should not have indent */
.formatted-content .body-text:nth-child(2) {
  text-indent: 0 !important;
}

/* Description paragraph should not have indent */
.formatted-content .body-text:nth-child(3) {
  text-indent: 0 !important;
}

/* Force text-indent for all other body text with maximum specificity */
.formatted-content p.body-text:not(:nth-child(2)):not(:nth-child(3)),
.formatted-content .body-text:not(:nth-child(2)):not(:nth-child(3)) {
  text-indent: 1cm !important;
}

/* Alternative approach using padding-left for first line effect */
.formatted-content .body-text:not(:nth-child(2)):not(:nth-child(3))::before {
  content: "";
  display: inline-block;
  width: 1cm;
}

/* 4. SPECIAL FORMATTING RULES */

/* Remove extra spacing between paragraphs - Kindle style */
.formatted-content p {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding: 0 !important;
}

/* First paragraph after any title should not have indent */
.formatted-content .chapter-title + .body-text,
.formatted-content .section-title + .body-text,
.formatted-content .subsection-title + .body-text {
  text-indent: 0 !important;
  margin-top: 0 !important;
}

/* Ensure proper text hierarchy and visual separation */
.formatted-content .chapter-title + *,
.formatted-content .section-title + *,
.formatted-content .subsection-title + * {
  margin-top: 0 !important;
}

/* Add visual breathing room between sections */
.formatted-content .section-title {
  margin-top: 3em !important;
  margin-bottom: 2em !important;
}

.formatted-content .subsection-title {
  margin-top: 2em !important;
  margin-bottom: 1.5em !important;
}

/* Ensure paragraphs have proper spacing from each other */
.formatted-content .body-text + .body-text {
  margin-top: 0 !important;
}

/* Add subtle separation for better readability */
.formatted-content .body-text:not(:first-child):not(.chapter-title + .body-text):not(.section-title + .body-text):not(.subsection-title + .body-text) {
  margin-top: 0.1em !important;
}

/* Block quotes or emphasized text */
.formatted-content .emphasized-text {
  font-style: italic !important;
  margin: 1em 0 !important;
  text-indent: 0 !important;
  text-align: center !important;
}

/* 5. THEME-SPECIFIC COLORS WITH PROPER CONTRAST */

/* Dark theme - Light text on dark background */
.formatted-content.dark,
.formatted-content.dark .chapter-title,
.formatted-content.dark .section-title,
.formatted-content.dark .subsection-title,
.formatted-content.dark .body-text,
.formatted-content.dark .numbered-list,
.formatted-content.dark .emphasized-text {
  color: #f9fafb !important; /* Light gray for excellent readability */
}

/* Sepia theme - Dark brown text on sepia background */
.formatted-content.sepia,
.formatted-content.sepia .chapter-title,
.formatted-content.sepia .section-title,
.formatted-content.sepia .subsection-title,
.formatted-content.sepia .body-text,
.formatted-content.sepia .numbered-list,
.formatted-content.sepia .emphasized-text {
  color: #92400e !important; /* Dark brown for warm reading experience */
}

/* Light theme - Pure black text on white background */
.formatted-content.light,
.formatted-content.light .chapter-title,
.formatted-content.light .section-title,
.formatted-content.light .subsection-title,
.formatted-content.light .body-text,
.formatted-content.light .numbered-list,
.formatted-content.light .emphasized-text {
  color: #000000 !important; /* Pure black for maximum contrast */
}

/* 6. USER INTERACTION AND ACCESSIBILITY */

/* Ensure text is selectable for copying */
.formatted-content {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* 7. RESPONSIVE DESIGN FOR DIFFERENT SCREEN SIZES */

/* Tablet and smaller screens */
@media (max-width: 768px) {
  .formatted-content {
    padding: 2cm 1.5cm 2cm 1.5cm !important; /* Reduced margins for mobile */
    /* font-size removed to allow dynamic control */
  }

  .formatted-content .chapter-title {
    font-size: 1.286em !important; /* 18pt relative to 14pt base */
    margin-top: 3em !important;
    margin-bottom: 1.5em !important;
  }

  .formatted-content .section-title {
    font-size: 1.143em !important; /* 16pt relative to 14pt base */
    margin-top: 2em !important;
    margin-bottom: 1em !important;
  }

  .formatted-content .body-text {
    text-indent: 0.8cm !important; /* Slightly smaller indent */
  }
}

/* Mobile phones */
@media (max-width: 480px) {
  .formatted-content {
    padding: 1.5cm 1cm 1.5cm 1cm !important;
    /* font-size removed to allow dynamic control */
  }

  .formatted-content .body-text {
    text-indent: 0.6cm !important;
  }
}

/* 8. PRINT STYLES FOR PDF EXPORT */
@media print {
  .formatted-content {
    color: #000000 !important;
    background: white !important;
    font-size: 12pt !important; /* Standard print size */
    padding: 2.5cm !important; /* Standard print margins */
  }

  .formatted-content .chapter-title,
  .formatted-content .section-title,
  .formatted-content .body-text,
  .formatted-content .numbered-list {
    color: #000000 !important;
  }

  /* Proper page breaks for printing */
  .formatted-content .chapter-title {
    page-break-before: always;
    page-break-after: avoid;
  }

  .formatted-content .section-title {
    page-break-after: avoid;
  }

  .formatted-content .body-text {
    page-break-inside: avoid;
    orphans: 3; /* Minimum lines at bottom of page */
    widows: 3;  /* Minimum lines at top of page */
  }
}

/* 9. FONT SIZE CONTROL COMPATIBILITY */
/* Ensure dynamic font sizing works with relative units */
.formatted-content * {
  font-family: inherit !important;
}
