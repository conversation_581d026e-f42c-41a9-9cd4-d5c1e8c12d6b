import fs from 'fs';
import path from 'path';

// Pasta dos PDFs
const PDF_FOLDER = './resumos_padronizados_roboto_final/home/<USER>/resumos_padronizados/pdf_final';

// Função para extrair informações do nome do arquivo
function extractBookInfo(filename) {
  const nameWithoutExt = filename.replace('.pdf', '');
  
  // Mapear nomes de arquivos para informações dos livros
  const bookMappings = {
    'a_sutil_arte_de_ligar_o_foda_se_mark_manson_3': {
      title: 'A Sutil Arte de Ligar o F*da-se',
      author: '<PERSON>',
      category: 'Autoajuda'
    },
    'o_poder_do_habito_charles_duhigg_3': {
      title: 'O Poder do Hábito',
      author: '<PERSON>',
      category: 'Produtividade'
    },
    'pai_rico_pai_pobre_robert_t_kiyosaki_3': {
      title: '<PERSON><PERSON>, <PERSON><PERSON>',
      author: '<PERSON>',
      category: '<PERSON><PERSON><PERSON><PERSON>'
    },
    'pense_e_enriqueca_napoleon_hill_3': {
      title: '<PERSON><PERSON>',
      author: '<PERSON>',
      category: '<PERSON>anças'
    },
    'habitos_atomicos_james_clear_5': {
      title: 'Hábitos Atômicos',
      author: 'James Clear',
      category: 'Produtividade'
    },
    'habitos_atomicos_james_clear_6': {
      title: 'Hábitos Atômicos',
      author: 'James Clear',
      category: 'Produtividade'
    },
    'a_interpretacao_dos_sonhos_sigmund_freud_3': {
      title: 'A Interpretação dos Sonhos',
      author: 'Sigmund Freud',
      category: 'Psicologia'
    },
    'cem_anos_de_solidao_gabriel_garcia_marquez_3': {
      title: 'Cem Anos de Solidão',
      author: 'Gabriel García Márquez',
      category: 'Literatura'
    },
    'o_codigo_da_vinci_dan_brown_3': {
      title: 'O Código da Vinci',
      author: 'Dan Brown',
      category: 'Literatura'
    },
    'tornar_se_pessoa_carl_rogers_3': {
      title: 'Tornar-se Pessoa',
      author: 'Carl Rogers',
      category: 'Psicologia'
    },
    'como_fazer_amigos_e_influenciar_pessoas_dale_carnegie_3': {
      title: 'Como Fazer Amigos e Influenciar Pessoas',
      author: 'Dale Carnegie',
      category: 'Autoajuda'
    },
    'mindset_carol_s_dweck_3': {
      title: 'Mindset: A Nova Psicologia do Sucesso',
      author: 'Carol S. Dweck',
      category: 'Psicologia'
    },
    'o_homem_em_busca_de_um_sentido_viktor_frankl_3': {
      title: 'O Homem em Busca de um Sentido',
      author: 'Viktor Frankl',
      category: 'Psicologia'
    },
    'inteligencia_emocional_daniel_goleman_3': {
      title: 'Inteligência Emocional',
      author: 'Daniel Goleman',
      category: 'Psicologia'
    },
    'os_7_habitos_das_pessoas_altamente_eficazes_stephen_covey_3': {
      title: 'Os 7 Hábitos das Pessoas Altamente Eficazes',
      author: 'Stephen Covey',
      category: 'Produtividade'
    },
    'o_milagre_da_manha_hal_elrod_3': {
      title: 'O Milagre da Manhã',
      author: 'Hal Elrod',
      category: 'Produtividade'
    },
    'quem_mexeu_no_meu_queijo_spencer_johnson_3': {
      title: 'Quem Mexeu no Meu Queijo?',
      author: 'Spencer Johnson',
      category: 'Autoajuda'
    },
    'o_monge_e_o_executivo_james_c_hunter_3': {
      title: 'O Monge e o Executivo',
      author: 'James C. Hunter',
      category: 'Liderança'
    },
    'essencialismo_greg_mckeown_3': {
      title: 'Essencialismo',
      author: 'Greg McKeown',
      category: 'Produtividade'
    },
    'o_poder_do_agora_eckhart_tolle_3': {
      title: 'O Poder do Agora',
      author: 'Eckhart Tolle',
      category: 'Espiritualidade'
    }
  };

  return bookMappings[nameWithoutExt] || {
    title: nameWithoutExt.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    author: 'Autor Desconhecido',
    category: 'Geral'
  };
}

// Função principal para listar todos os PDFs
function listAllPDFs() {
  try {
    console.log('📚 Listando todos os PDFs encontrados...\n');
    
    const files = fs.readdirSync(PDF_FOLDER).filter(file => file.endsWith('.pdf'));
    console.log(`Total de PDFs encontrados: ${files.length}\n`);

    files.forEach((file, index) => {
      const bookInfo = extractBookInfo(file);
      console.log(`${index + 1}. 📖 ${bookInfo.title}`);
      console.log(`   ✍️  Autor: ${bookInfo.author}`);
      console.log(`   📂 Categoria: ${bookInfo.category}`);
      console.log(`   📄 Arquivo: ${file}`);
      console.log('');
    });

    console.log('🎯 Próximos passos:');
    console.log('1. Vou processar estes PDFs e extrair o conteúdo');
    console.log('2. Criar estrutura padronizada para cada resumo');
    console.log('3. Inserir no banco de dados Supabase');
    console.log('4. Testar a funcionalidade de leitura no site');
    
  } catch (error) {
    console.error('❌ Erro ao listar PDFs:', error);
  }
}

// Executar a listagem
listAllPDFs();
