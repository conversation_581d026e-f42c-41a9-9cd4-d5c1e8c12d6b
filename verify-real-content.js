import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyRealContent() {
  try {
    console.log('🔍 VERIFICANDO CONTEÚDO REAL DOS LIVROS...\n');

    // Testar os livros específicos que foram atualizados
    const testBooks = [
      { id: 7, expectedTitle: 'A Interpretação dos Sonhos', expectedAuthor: 'Sigmund <PERSON>' },
      { id: 4, expectedTitle: 'A Sutil Arte de Ligar o F*da-se', expectedAuthor: '<PERSON>' },
      { id: 2, expectedTitle: 'O Poder do Hábito', expectedAuthor: '<PERSON>' }
    ];

    for (const testBook of testBooks) {
      console.log(`📖 Testando: ${testBook.expectedTitle}`);
      
      // Buscar dados do livro
      const { data: bookData, error: bookError } = await supabase
        .from('books')
        .select('*')
        .eq('id', testBook.id)
        .single();

      if (bookError) {
        console.log(`   ❌ Erro ao buscar livro: ${bookError.message}`);
        continue;
      }

      console.log(`   📝 Título: ${bookData.title}`);
      console.log(`   ✍️ Autor: ${bookData.author}`);
      console.log(`   📂 Categoria: ${bookData.category}`);
      console.log(`   ⏱️ Duração: ${bookData.duration} min`);

      // Buscar conteúdo do livro
      const { data: contentData, error: contentError } = await supabase
        .from('book_contents')
        .select('content')
        .eq('book_id', testBook.id)
        .single();

      if (contentError) {
        console.log(`   ❌ Erro ao buscar conteúdo: ${contentError.message}`);
        continue;
      }

      const content = contentData.content;
      console.log(`   📚 Capítulos encontrados: ${content.chapters?.length || 0}`);
      console.log(`   🔑 Pontos-chave: ${content.key_points?.length || 0}`);
      console.log(`   💡 Exercícios práticos: ${content.practical_exercises?.length || 0}`);

      // Verificar se o conteúdo é real (não genérico)
      if (content.chapters && content.chapters.length > 0) {
        const firstChapter = content.chapters[0];
        console.log(`   📄 Primeiro capítulo: "${firstChapter.title}"`);
        
        const contentPreview = firstChapter.content.substring(0, 200) + '...';
        console.log(`   📋 Preview do conteúdo: ${contentPreview}`);

        // Verificar se contém conteúdo específico (não genérico)
        const isRealContent = checkIfRealContent(testBook.expectedTitle, firstChapter.content);
        console.log(`   ✅ Conteúdo é real (não genérico): ${isRealContent ? 'SIM' : 'NÃO'}`);

        if (!isRealContent) {
          console.log(`   ⚠️ PROBLEMA: Conteúdo ainda parece genérico!`);
        }
      }

      console.log('');
    }

    // Testar o BookLoader diretamente
    console.log('🧪 TESTANDO BOOKLOADER DIRETAMENTE...\n');
    
    // Simular o que acontece quando o usuário clica em "Ler Agora"
    for (const testBook of testBooks) {
      console.log(`📖 Simulando leitura de: ${testBook.expectedTitle}`);
      
      try {
        // Simular a consulta que o BookLoader faz
        const { data: bookWithContent, error } = await supabase
          .from('books')
          .select(`
            *,
            book_contents (
              content
            )
          `)
          .eq('id', testBook.id)
          .single();

        if (error) {
          console.log(`   ❌ Erro na consulta: ${error.message}`);
          continue;
        }

        if (bookWithContent.book_contents && bookWithContent.book_contents.length > 0) {
          const content = bookWithContent.book_contents[0].content;
          console.log(`   ✅ Conteúdo carregado com sucesso`);
          console.log(`   📚 Capítulos: ${content.chapters?.length || 0}`);
          
          if (content.chapters && content.chapters.length > 0) {
            const specificContent = content.chapters[0].content;
            const hasSpecificContent = checkIfRealContent(testBook.expectedTitle, specificContent);
            console.log(`   🎯 Conteúdo específico detectado: ${hasSpecificContent ? 'SIM' : 'NÃO'}`);
            
            if (hasSpecificContent) {
              console.log(`   🎉 SUCESSO: ${testBook.expectedTitle} agora mostra conteúdo real!`);
            } else {
              console.log(`   ❌ PROBLEMA: ${testBook.expectedTitle} ainda mostra conteúdo genérico!`);
            }
          }
        } else {
          console.log(`   ❌ Nenhum conteúdo encontrado`);
        }
      } catch (error) {
        console.log(`   ❌ Erro no teste: ${error.message}`);
      }
      
      console.log('');
    }

    console.log('🎯 RESULTADO FINAL:');
    console.log('Os livros testados agora devem mostrar conteúdo real e específico');
    console.log('em vez de templates genéricos baseados em categoria.');
    console.log('\nTeste a funcionalidade no navegador clicando em "Ler Agora"!');

  } catch (error) {
    console.error('💥 Erro na verificação:', error);
  }
}

// Função para verificar se o conteúdo é real (específico) ou genérico
function checkIfRealContent(bookTitle, content) {
  const lowerContent = content.toLowerCase();
  
  // Verificar por frases genéricas que indicam conteúdo template
  const genericPhrases = [
    'esta obra examina como pensamentos, emoções e comportamentos se interconectam',
    'apresenta ideias importantes sobre',
    'oferece perspectivas valiosas',
    'explora conceitos centrais',
    'esta seção foca na aplicação prática',
    'os conceitos apresentados fornecem uma base sólida'
  ];

  const hasGenericContent = genericPhrases.some(phrase => lowerContent.includes(phrase));
  
  // Verificar por conteúdo específico baseado no livro
  let hasSpecificContent = false;
  
  if (bookTitle.includes('Interpretação dos Sonhos')) {
    hasSpecificContent = lowerContent.includes('freud') || 
                        lowerContent.includes('sonho') || 
                        lowerContent.includes('inconsciente') ||
                        lowerContent.includes('psicanálise');
  } else if (bookTitle.includes('Sutil Arte')) {
    hasSpecificContent = lowerContent.includes('mark manson') || 
                        lowerContent.includes('não tente') || 
                        lowerContent.includes('f*da-se') ||
                        lowerContent.includes('positivo o tempo todo');
  } else if (bookTitle.includes('Poder do Hábito')) {
    hasSpecificContent = lowerContent.includes('charles duhigg') || 
                        lowerContent.includes('loop') || 
                        lowerContent.includes('deixa') ||
                        lowerContent.includes('rotina') ||
                        lowerContent.includes('recompensa');
  }
  
  return hasSpecificContent && !hasGenericContent;
}

verifyRealContent();
