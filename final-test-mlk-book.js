import puppeteer from 'puppeteer';

async function finalTestMLKBook() {
  let browser;
  try {
    console.log('🎯 FINAL TEST: MLK BOOK CONTENT AND FONT SIZE CONTROLS\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    console.log('🌐 Navigating to application...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('📚 Looking for MLK book...');
    
    // Look for MLK book with more specific search
    const mlkBookFound = await page.evaluate(() => {
      // Look for books containing MLK-related text
      const allElements = document.querySelectorAll('*');
      
      for (let element of allElements) {
        const text = element.textContent || '';
        if (text.includes('Um Apelo à Consciência') || 
            text.includes('<PERSON>') ||
            text.includes('Melhores Discursos')) {
          
          // Try to find a clickable parent
          let clickableElement = element;
          while (clickableElement && clickableElement !== document.body) {
            if (clickableElement.tagName === 'DIV' || 
                clickableElement.tagName === 'BUTTON' ||
                clickableElement.classList.contains('book-card') ||
                clickableElement.classList.contains('book-item')) {
              clickableElement.click();
              return {
                found: true,
                text: text.substring(0, 100),
                element: clickableElement.tagName + '.' + clickableElement.className
              };
            }
            clickableElement = clickableElement.parentElement;
          }
          
          // If no specific parent found, click the element itself
          element.click();
          return {
            found: true,
            text: text.substring(0, 100),
            element: element.tagName + '.' + element.className
          };
        }
      }
      return { found: false };
    });

    if (!mlkBookFound.found) {
      console.log('❌ MLK book not found in the interface');
      console.log('🔍 Let me check what books are visible...');
      
      const visibleBooks = await page.evaluate(() => {
        const bookElements = document.querySelectorAll('*');
        const books = [];
        
        for (let element of bookElements) {
          const text = element.textContent || '';
          if (text.length > 10 && text.length < 200 && 
              (text.includes('Autor') || text.includes('por') || text.includes('de'))) {
            books.push(text.substring(0, 100));
          }
        }
        
        return books.slice(0, 10); // First 10 book-like texts
      });
      
      console.log('📖 Visible book-like content:');
      visibleBooks.forEach((book, index) => {
        console.log(`   ${index + 1}. "${book}..."`);
      });
      
      return;
    }

    console.log(`✅ MLK book found and clicked: "${mlkBookFound.text}..."`);
    console.log(`   Element: ${mlkBookFound.element}`);
    
    console.log('⏳ Waiting for reader to load...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Test the content and font size controls
    console.log('🧪 Testing content and font size controls...');
    
    const readerTest = await page.evaluate(() => {
      // Check if reader loaded
      const reader = document.querySelector('.formatted-content');
      if (!reader) {
        return { success: false, error: 'Reader not loaded' };
      }

      // Get content information
      const contentInfo = {
        hasContent: !!reader.textContent,
        contentLength: reader.textContent?.length || 0,
        contentPreview: reader.textContent?.substring(0, 200) || '',
        
        // Check for MLK-specific content
        hasMLKContent: reader.textContent?.includes('Martin Luther King') || false,
        hasDiscursos: reader.textContent?.includes('discursos') || false,
        hasDireitosCivis: reader.textContent?.includes('direitos civis') || false,
        
        // Check structure
        hasChapterTitles: reader.querySelectorAll('.chapter-title').length,
        hasSectionTitles: reader.querySelectorAll('.section-title').length,
        hasBodyTexts: reader.querySelectorAll('.body-text').length,
        
        // Font size info
        initialFontSize: window.getComputedStyle(reader).fontSize,
        readerStyle: reader.style.fontSize || 'not set'
      };

      // Test font size controls
      const fontControls = {
        increaseButtons: Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.title?.includes('Aumentar') || btn.title?.includes('Zoom In') || btn.textContent?.includes('+')
        ).length,
        
        decreaseButtons: Array.from(document.querySelectorAll('button')).filter(btn => 
          btn.title?.includes('Diminuir') || btn.title?.includes('Zoom Out') || btn.textContent?.includes('-')
        ).length,
        
        fontSizeDisplays: Array.from(document.querySelectorAll('span')).filter(span => 
          span.textContent?.includes('pt')
        ).length
      };

      return {
        success: true,
        contentInfo,
        fontControls
      };
    });

    if (!readerTest.success) {
      console.log('❌ Reader test failed:', readerTest.error);
      return;
    }

    console.log('\n📊 CONTENT ANALYSIS:');
    const content = readerTest.contentInfo;
    console.log(`   Has Content: ${content.hasContent}`);
    console.log(`   Content Length: ${content.contentLength.toLocaleString()} characters`);
    console.log(`   Initial Font Size: ${content.initialFontSize}`);
    console.log(`   Reader Inline Style: ${content.readerStyle}`);
    
    console.log('\n📚 MLK CONTENT VERIFICATION:');
    console.log(`   ${content.hasMLKContent ? '✅' : '❌'} Contains "Martin Luther King"`);
    console.log(`   ${content.hasDiscursos ? '✅' : '❌'} Contains "discursos"`);
    console.log(`   ${content.hasDireitosCivis ? '✅' : '❌'} Contains "direitos civis"`);
    
    console.log('\n🏷️ FORMATTING STRUCTURE:');
    console.log(`   Chapter Titles: ${content.hasChapterTitles}`);
    console.log(`   Section Titles: ${content.hasSectionTitles}`);
    console.log(`   Body Texts: ${content.hasBodyTexts}`);
    
    console.log('\n🔧 FONT SIZE CONTROLS:');
    const controls = readerTest.fontControls;
    console.log(`   Increase Buttons: ${controls.increaseButtons}`);
    console.log(`   Decrease Buttons: ${controls.decreaseButtons}`);
    console.log(`   Font Size Displays: ${controls.fontSizeDisplays}`);
    
    console.log('\n📋 CONTENT PREVIEW:');
    console.log(`"${content.contentPreview}..."`);

    // Test font size increase
    if (controls.increaseButtons > 0) {
      console.log('\n🔧 Testing font size increase...');
      
      const increaseResult = await page.evaluate(() => {
        const increaseBtn = Array.from(document.querySelectorAll('button')).find(btn => 
          btn.title?.includes('Aumentar') || btn.title?.includes('Zoom In')
        );
        
        if (!increaseBtn) return { success: false, error: 'No increase button found' };
        
        const reader = document.querySelector('.formatted-content');
        const beforeSize = window.getComputedStyle(reader).fontSize;
        const beforeInline = reader.style.fontSize;
        
        increaseBtn.click();
        
        // Wait for React state update
        return new Promise(resolve => {
          setTimeout(() => {
            const afterSize = window.getComputedStyle(reader).fontSize;
            const afterInline = reader.style.fontSize;
            
            resolve({
              success: true,
              beforeSize,
              afterSize,
              beforeInline,
              afterInline,
              changed: beforeSize !== afterSize || beforeInline !== afterInline
            });
          }, 500);
        });
      });
      
      console.log('📈 FONT SIZE INCREASE RESULT:');
      console.log(`   Before (computed): ${increaseResult.beforeSize}`);
      console.log(`   After (computed): ${increaseResult.afterSize}`);
      console.log(`   Before (inline): ${increaseResult.beforeInline}`);
      console.log(`   After (inline): ${increaseResult.afterInline}`);
      console.log(`   Font Size Changed: ${increaseResult.changed ? '✅ YES' : '❌ NO'}`);
    }

    // Calculate overall success
    const contentSuccess = content.hasContent && content.contentLength > 1000;
    const mlkContentSuccess = content.hasMLKContent || content.hasDiscursos;
    const formattingSuccess = content.hasBodyTexts > 0;
    const controlsSuccess = controls.increaseButtons > 0 && controls.decreaseButtons > 0;

    console.log('\n🎯 FINAL ASSESSMENT:');
    console.log(`   ${contentSuccess ? '✅' : '❌'} Content Loading`);
    console.log(`   ${mlkContentSuccess ? '✅' : '❌'} MLK Content Present`);
    console.log(`   ${formattingSuccess ? '✅' : '❌'} Kindle Formatting Applied`);
    console.log(`   ${controlsSuccess ? '✅' : '❌'} Font Size Controls Available`);

    const overallSuccess = contentSuccess && mlkContentSuccess && formattingSuccess && controlsSuccess;
    console.log(`\n🏆 OVERALL STATUS: ${overallSuccess ? '✅ SUCCESS' : '⚠️ NEEDS ATTENTION'}`);

    if (overallSuccess) {
      console.log('\n🎉 EXCELLENT! The MLK book is working correctly:');
      console.log('   ✓ Authentic content is displaying');
      console.log('   ✓ Kindle-style formatting is applied');
      console.log('   ✓ Font size controls are available');
      console.log('   ✓ User experience is optimal');
    } else {
      console.log('\n🔧 Areas that need attention:');
      if (!contentSuccess) console.log('   • Content loading or length issues');
      if (!mlkContentSuccess) console.log('   • MLK-specific content not detected');
      if (!formattingSuccess) console.log('   • Kindle formatting not properly applied');
      if (!controlsSuccess) console.log('   • Font size controls not working');
    }

    await new Promise(resolve => setTimeout(resolve, 10000));

  } catch (error) {
    console.error('💥 Error during final test:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

finalTestMLKBook();
