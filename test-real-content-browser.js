import puppeteer from 'puppeteer';

async function testRealContentInBrowser() {
  let browser;
  try {
    console.log('🎯 TESTE FINAL: Verificando conteúdo real no navegador\n');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1400, height: 900 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    
    // Capturar logs do console
    page.on('console', msg => {
      const text = msg.text();
      if (text.includes('Loading book') || text.includes('Book') || text.includes('content') || text.includes('Freud')) {
        console.log(`🔵 Console: ${text}`);
      }
    });

    console.log('🌐 Navegando para a aplicação...');
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Fazer login
    console.log('🔐 Fazendo login...');
    const loginClicked = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button, a'));
      const loginButton = buttons.find(btn => {
        const text = btn.textContent?.toLowerCase() || '';
        return text.includes('entrar');
      });
      
      if (loginButton) {
        loginButton.click();
        return true;
      }
      return false;
    });
    
    if (loginClicked) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      try {
        await page.type('input[type="email"]', '<EMAIL>', { delay: 50 });
        await page.type('input[type="password"]', 'password123', { delay: 50 });
        
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
          await submitButton.click();
          await new Promise(resolve => setTimeout(resolve, 5000));
          console.log('✅ Login realizado');
        }
      } catch (error) {
        console.log('⚠️ Erro no login, continuando...');
      }
    }

    // Navegar para biblioteca
    console.log('📚 Navegando para biblioteca...');
    const libraryClicked = await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a, button'));
      const libraryLink = links.find(link => {
        const text = link.textContent?.toLowerCase() || '';
        return text.includes('biblioteca');
      });
      
      if (libraryLink) {
        libraryLink.click();
        return true;
      }
      return false;
    });
    
    if (libraryClicked) {
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log('✅ Navegou para biblioteca');
    }

    // Procurar especificamente pelo livro "A Interpretação dos Sonhos"
    console.log('🔍 Procurando por "A Interpretação dos Sonhos"...');
    
    const freudBookFound = await page.evaluate(() => {
      const allText = document.body.innerText;
      return allText.includes('Interpretação dos Sonhos') || allText.includes('Sigmund Freud');
    });

    console.log(`📖 Livro do Freud encontrado na página: ${freudBookFound ? 'SIM' : 'NÃO'}`);

    if (freudBookFound) {
      // Tentar clicar no livro do Freud
      console.log('🖱️ Tentando clicar no livro do Freud...');
      
      const clickResult = await page.evaluate(() => {
        // Procurar por cards que contenham "Freud" ou "Interpretação"
        const allElements = Array.from(document.querySelectorAll('*'));
        const freudElements = allElements.filter(el => {
          const text = el.textContent || '';
          return (text.includes('Freud') || text.includes('Interpretação dos Sonhos')) && 
                 text.length < 500; // Evitar elementos muito grandes
        });

        for (const element of freudElements) {
          // Procurar por botão dentro ou próximo deste elemento
          const button = element.querySelector('button') || 
                        element.closest('[class*="card"]')?.querySelector('button') ||
                        element.parentElement?.querySelector('button');
          
          if (button) {
            const buttonText = button.textContent?.toLowerCase() || '';
            if (buttonText.includes('ler') || buttonText.includes('abrir') || buttonText.includes('play')) {
              button.click();
              return { 
                success: true, 
                buttonText: button.textContent?.trim(),
                elementText: element.textContent?.substring(0, 100)
              };
            }
          }
        }
        
        return { success: false };
      });

      if (clickResult.success) {
        console.log(`✅ Clicou no botão: "${clickResult.buttonText}"`);
        console.log(`📄 Elemento: ${clickResult.elementText}...`);
        
        // Aguardar carregamento da página de leitura
        console.log('⏳ Aguardando carregamento da página de leitura...');
        await new Promise(resolve => setTimeout(resolve, 8000));
        
        // Verificar se o conteúdo real do Freud está sendo exibido
        console.log('🔍 Verificando conteúdo da página de leitura...');
        
        const contentAnalysis = await page.evaluate(() => {
          const bodyText = document.body.innerText;
          
          // Verificar por conteúdo específico do Freud
          const hasFreudContent = bodyText.includes('Freud') ||
                                 bodyText.includes('sonho é a via régia') ||
                                 bodyText.includes('inconsciente') ||
                                 bodyText.includes('psicanálise') ||
                                 bodyText.includes('associação livre');

          // Verificar por conteúdo genérico (que não deveria estar lá)
          const hasGenericContent = bodyText.includes('Esta obra examina como pensamentos, emoções e comportamentos se interconectam') ||
                                   bodyText.includes('apresenta conceitos fundamentais que podem transformar') ||
                                   bodyText.includes('oferece perspectivas valiosas sobre');

          return {
            hasFreudContent,
            hasGenericContent,
            contentLength: bodyText.length,
            contentPreview: bodyText.substring(0, 500),
            title: document.title,
            url: window.location.href
          };
        });

        console.log('\n📊 ANÁLISE DO CONTEÚDO:');
        console.log(`📝 Título da página: ${contentAnalysis.title}`);
        console.log(`🌐 URL: ${contentAnalysis.url}`);
        console.log(`📏 Tamanho do conteúdo: ${contentAnalysis.contentLength} caracteres`);
        console.log(`✅ Conteúdo específico do Freud: ${contentAnalysis.hasFreudContent ? 'SIM' : 'NÃO'}`);
        console.log(`❌ Conteúdo genérico detectado: ${contentAnalysis.hasGenericContent ? 'SIM' : 'NÃO'}`);
        
        console.log('\n📄 Preview do conteúdo:');
        console.log(contentAnalysis.contentPreview + '...');

        // Resultado final
        console.log('\n🎯 RESULTADO FINAL:');
        if (contentAnalysis.hasFreudContent && !contentAnalysis.hasGenericContent) {
          console.log('🎉 SUCESSO TOTAL! O livro agora mostra conteúdo REAL do Freud!');
          console.log('✅ Problema resolvido: Conteúdo específico em vez de template genérico');
        } else if (contentAnalysis.hasFreudContent && contentAnalysis.hasGenericContent) {
          console.log('⚠️ SUCESSO PARCIAL: Tem conteúdo do Freud, mas ainda há conteúdo genérico');
        } else if (!contentAnalysis.hasFreudContent && contentAnalysis.hasGenericContent) {
          console.log('❌ PROBLEMA PERSISTE: Ainda mostra conteúdo genérico em vez do Freud');
        } else {
          console.log('❓ RESULTADO INCERTO: Não foi possível determinar o tipo de conteúdo');
        }

      } else {
        console.log('❌ Não foi possível clicar no livro do Freud');
      }
    } else {
      console.log('❌ Livro do Freud não encontrado na biblioteca');
    }

    // Screenshot final
    await page.screenshot({ 
      path: 'real-content-test.png', 
      fullPage: true 
    });
    console.log('\n📸 Screenshot salvo como real-content-test.png');

    await new Promise(resolve => setTimeout(resolve, 5000));

  } catch (error) {
    console.error('💥 Erro durante o teste:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

testRealContentInBrowser();
