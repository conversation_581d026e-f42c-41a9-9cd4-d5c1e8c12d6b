import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://qmeelujsnpbcdkzhcwmm.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFtZWVsdWpzbnBiY2Rremhjd21tIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYzNjIzNDUsImV4cCI6MjA2MTkzODM0NX0.qvYIv-E2rwMeMc3l7ua2ty5cLODiOmOnBpWhlXwbwY4';
const supabase = createClient(supabaseUrl, supabaseKey);

async function updateMLKTitle() {
  try {
    console.log('📝 UPDATING MLK BOOK TITLE\n');

    const bookId = 36;
    const correctTitle = "Um Apelo à Consciência: Os Melhores Discursos de Martin <PERSON>";

    // Try different approaches to update the title
    console.log('1. Attempting title update...');
    
    const { data: updateResult, error: updateError } = await supabase
      .from('books')
      .update({ title: correctTitle })
      .eq('id', bookId)
      .select();

    if (updateError) {
      console.log('❌ Error updating title:', updateError.message);
    } else {
      console.log('✅ Title update successful');
      console.log(`   Updated ${updateResult.length} record(s)`);
      if (updateResult.length > 0) {
        console.log(`   New title: "${updateResult[0].title}"`);
      }
    }

    // Verify the current state
    console.log('\n2. Verifying current book state...');
    
    const { data: currentBook, error: fetchError } = await supabase
      .from('books')
      .select('*')
      .eq('id', bookId)
      .single();

    if (fetchError) {
      console.log('❌ Error fetching book:', fetchError.message);
    } else {
      console.log('✅ Current book state:');
      console.log(`   ID: ${currentBook.id}`);
      console.log(`   Title: "${currentBook.title}"`);
      console.log(`   Author: "${currentBook.author}"`);
      console.log(`   Description: "${currentBook.description}"`);
      
      const titleIsCorrect = currentBook.title === correctTitle;
      console.log(`   Title status: ${titleIsCorrect ? 'CORRECT' : 'NEEDS UPDATE'}`);
      
      if (!titleIsCorrect) {
        console.log(`   Expected: "${correctTitle}"`);
        console.log(`   Actual: "${currentBook.title}"`);
      }
    }

    // Also update the description to be more complete
    console.log('\n3. Updating description...');
    
    const betterDescription = "Os melhores discursos sobre direitos civis e justiça social de Martin Luther King Jr., incluindo 'I Have a Dream' e outros discursos históricos que mudaram o mundo.";
    
    const { error: descError } = await supabase
      .from('books')
      .update({ 
        title: correctTitle,
        description: betterDescription 
      })
      .eq('id', bookId);

    if (descError) {
      console.log('❌ Error updating description:', descError.message);
    } else {
      console.log('✅ Description updated successfully');
    }

    // Final verification
    console.log('\n4. Final verification...');
    
    const { data: finalBook, error: finalError } = await supabase
      .from('books')
      .select('*')
      .eq('id', bookId)
      .single();

    if (finalError) {
      console.log('❌ Error in final verification:', finalError.message);
    } else {
      console.log('✅ Final book state:');
      console.log(`   Title: "${finalBook.title}"`);
      console.log(`   Author: "${finalBook.author}"`);
      console.log(`   Description: "${finalBook.description}"`);
      
      const allCorrect = finalBook.title === correctTitle && 
                        finalBook.author === "Martin Luther King Jr." &&
                        finalBook.description.includes("I Have a Dream");
      
      console.log(`\n🎯 Status: ${allCorrect ? 'ALL METADATA CORRECT' : 'SOME ISSUES REMAIN'}`);
    }

  } catch (error) {
    console.error('💥 Error updating MLK title:', error);
  }
}

updateMLKTitle();
